@echo off
echo === PCILeech 编译脚本 ===
echo.

REM 设置工作目录
cd /d "G:\FPGA\20250327\100t20250618\100t484-1"

REM 查找MSBuild
set MSBUILD_PATH=""
if exist "%ProgramFiles%\Microsoft Visual Studio\2022\Professional\MSBuild\Current\Bin\MSBuild.exe" (
    set MSBUILD_PATH="%ProgramFiles%\Microsoft Visual Studio\2022\Professional\MSBuild\Current\Bin\MSBuild.exe"
    goto :found_msbuild
)
if exist "%ProgramFiles%\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\MSBuild.exe" (
    set MSBUILD_PATH="%ProgramFiles%\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\MSBuild.exe"
    goto :found_msbuild
)
if exist "%ProgramFiles(x86)%\Microsoft Visual Studio\2019\Professional\MSBuild\Current\Bin\MSBuild.exe" (
    set MSBUILD_PATH="%ProgramFiles(x86)%\Microsoft Visual Studio\2019\Professional\MSBuild\Current\Bin\MSBuild.exe"
    goto :found_msbuild
)
if exist "%ProgramFiles(x86)%\Microsoft Visual Studio\2019\Community\MSBuild\Current\Bin\MSBuild.exe" (
    set MSBUILD_PATH="%ProgramFiles(x86)%\Microsoft Visual Studio\2019\Community\MSBuild\Current\Bin\MSBuild.exe"
    goto :found_msbuild
)

echo 错误: 未找到MSBuild
echo 请安装 Visual Studio 2019 或 2022
pause
exit /b 1

:found_msbuild
echo 找到MSBuild: %MSBUILD_PATH%
echo.

REM 编译项目
echo 开始编译PCILeech项目...
%MSBUILD_PATH% "pcileech-master\pcileech\pcileech.vcxproj" /p:Configuration=Release /p:Platform=x64 /verbosity:minimal

if %ERRORLEVEL% neq 0 (
    echo.
    echo 主项目编译失败，尝试编译整个解决方案...
    %MSBUILD_PATH% "pcileech-master\pcileech.sln" /p:Configuration=Release /p:Platform=x64 /verbosity:minimal
)

if %ERRORLEVEL% neq 0 (
    echo.
    echo Release配置编译失败，尝试Debug配置...
    %MSBUILD_PATH% "pcileech-master\pcileech\pcileech.vcxproj" /p:Configuration=Debug /p:Platform=x64 /verbosity:minimal
)

echo.
echo 检查编译结果...

REM 检查可执行文件
if exist "pcileech-master\files\pcileech.exe" (
    echo 成功: 找到 pcileech-master\files\pcileech.exe
    goto :success
)
if exist "pcileech-master\pcileech\x64\Release\pcileech.exe" (
    echo 成功: 找到 pcileech-master\pcileech\x64\Release\pcileech.exe
    echo 复制到标准位置...
    if not exist "pcileech-master\files" mkdir "pcileech-master\files"
    copy "pcileech-master\pcileech\x64\Release\pcileech.exe" "pcileech-master\files\pcileech.exe"
    goto :success
)
if exist "pcileech-master\pcileech\x64\Debug\pcileech.exe" (
    echo 成功: 找到 pcileech-master\pcileech\x64\Debug\pcileech.exe
    echo 复制到标准位置...
    if not exist "pcileech-master\files" mkdir "pcileech-master\files"
    copy "pcileech-master\pcileech\x64\Debug\pcileech.exe" "pcileech-master\files\pcileech.exe"
    goto :success
)

echo 错误: 编译失败，未找到可执行文件
pause
exit /b 1

:success
echo.
echo === 编译成功 ===
echo 可执行文件: pcileech-master\files\pcileech.exe

REM 显示文件信息
for %%i in ("pcileech-master\files\pcileech.exe") do (
    echo 文件大小: %%~zi 字节
    echo 修改时间: %%~ti
)

echo.
echo 测试基本功能...
"pcileech-master\files\pcileech.exe" info

echo.
echo 编译完成！
echo 下一步: 运行性能测试
echo 命令: test_ft601_optimization.ps1
pause
