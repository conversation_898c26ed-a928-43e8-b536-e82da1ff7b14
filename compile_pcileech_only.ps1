# 编译PCILeech主项目（跳过依赖项）
# 专门用于编译优化版PCILeech的脚本

param(
    [Parameter(Mandatory=$false)]
    [string]$ProjectPath = "G:\FPGA\20250327\100t20250618\100t484-1"
)

$ErrorActionPreference = "Stop"

Write-Host "=== PCILeech 优化版编译脚本 ===" -ForegroundColor Green
Write-Host "项目路径: $ProjectPath" -ForegroundColor Yellow

Set-Location $ProjectPath

# 查找MSBuild
Write-Host "`n[1/4] 查找MSBuild..." -ForegroundColor Cyan
$msbuildPaths = @(
    "${env:ProgramFiles}\Microsoft Visual Studio\2022\Professional\MSBuild\Current\Bin\MSBuild.exe",
    "${env:ProgramFiles}\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\MSBuild.exe",
    "${env:ProgramFiles}\Microsoft Visual Studio\2022\Enterprise\MSBuild\Current\Bin\MSBuild.exe",
    "${env:ProgramFiles(x86)}\Microsoft Visual Studio\2019\Professional\MSBuild\Current\Bin\MSBuild.exe",
    "${env:ProgramFiles(x86)}\Microsoft Visual Studio\2019\Community\MSBuild\Current\Bin\MSBuild.exe",
    "${env:ProgramFiles(x86)}\Microsoft Visual Studio\2019\Enterprise\MSBuild\Current\Bin\MSBuild.exe"
)

$msbuildPath = $null
foreach ($path in $msbuildPaths) {
    if (Test-Path $path) {
        $msbuildPath = $path
        Write-Host "  找到MSBuild: $path" -ForegroundColor Green
        break
    }
}

if (-not $msbuildPath) {
    Write-Host "  ✗ 未找到MSBuild" -ForegroundColor Red
    Write-Host "  请安装 Visual Studio 2019 或 2022" -ForegroundColor Yellow
    exit 1
}

# 检查项目文件
Write-Host "`n[2/4] 检查项目文件..." -ForegroundColor Cyan
$vcxproj = Join-Path $ProjectPath "pcileech-master\pcileech\pcileech.vcxproj"
if (-not (Test-Path $vcxproj)) {
    Write-Host "  ✗ 项目文件不存在: $vcxproj" -ForegroundColor Red
    exit 1
}
Write-Host "  ✓ 项目文件存在" -ForegroundColor Green

# 检查优化文件
$optimizedFiles = @(
    "pcileech-master\pcileech\device_optimized.c",
    "pcileech-master\pcileech\memdump_optimized.c",
    "pcileech-master\pcileech\ft601_performance_config.h",
    "pcileech-master\pcileech\ft601_performance_config.c"
)

$missingFiles = @()
foreach ($file in $optimizedFiles) {
    $fullPath = Join-Path $ProjectPath $file
    if (Test-Path $fullPath) {
        Write-Host "    ✓ $file" -ForegroundColor Green
    } else {
        Write-Host "    ✗ $file" -ForegroundColor Red
        $missingFiles += $file
    }
}

if ($missingFiles.Count -gt 0) {
    Write-Host "  ⚠ 缺少 $($missingFiles.Count) 个优化文件，但继续编译..." -ForegroundColor Yellow
}

# 尝试编译
Write-Host "`n[3/4] 开始编译..." -ForegroundColor Cyan

try {
    # 方法1: 尝试编译整个解决方案
    Write-Host "  尝试编译完整解决方案..." -ForegroundColor Yellow
    
    $slnPath = Join-Path $ProjectPath "pcileech-master\pcileech.sln"
    $output = & $msbuildPath $slnPath /p:Configuration=Release /p:Platform=x64 /verbosity:minimal /nologo 2>&1
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "  ✓ 完整解决方案编译成功" -ForegroundColor Green
    } else {
        Write-Host "  ⚠ 完整解决方案编译失败，尝试只编译主项目..." -ForegroundColor Yellow
        
        # 方法2: 只编译主项目
        Write-Host "  尝试编译主项目..." -ForegroundColor Yellow
        $output = & $msbuildPath $vcxproj /p:Configuration=Release /p:Platform=x64 /verbosity:minimal /nologo 2>&1
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "  ✓ 主项目编译成功" -ForegroundColor Green
        } else {
            Write-Host "  ✗ 主项目编译也失败" -ForegroundColor Red
            Write-Host "编译输出:" -ForegroundColor Yellow
            $output | ForEach-Object { Write-Host "    $_" -ForegroundColor Gray }
            
            # 方法3: 尝试Debug配置
            Write-Host "  尝试Debug配置..." -ForegroundColor Yellow
            $output = & $msbuildPath $vcxproj /p:Configuration=Debug /p:Platform=x64 /verbosity:minimal /nologo 2>&1
            
            if ($LASTEXITCODE -ne 0) {
                Write-Host "  ✗ Debug配置也失败" -ForegroundColor Red
                throw "所有编译尝试都失败"
            } else {
                Write-Host "  ✓ Debug配置编译成功" -ForegroundColor Green
            }
        }
    }
} catch {
    Write-Host "  编译过程出错: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "  编译输出:" -ForegroundColor Yellow
    $output | ForEach-Object { Write-Host "    $_" -ForegroundColor Gray }
}

# 检查编译结果
Write-Host "`n[4/4] 检查编译结果..." -ForegroundColor Cyan

$possibleExePaths = @(
    "pcileech-master\files\pcileech.exe",
    "pcileech-master\pcileech\x64\Release\pcileech.exe",
    "pcileech-master\pcileech\x64\Debug\pcileech.exe",
    "pcileech-master\x64\Release\pcileech.exe",
    "pcileech-master\x64\Debug\pcileech.exe"
)

$foundExe = $null
foreach ($exePath in $possibleExePaths) {
    $fullExePath = Join-Path $ProjectPath $exePath
    if (Test-Path $fullExePath) {
        $foundExe = $fullExePath
        break
    }
}

if ($foundExe) {
    $exe = Get-Item $foundExe
    Write-Host "  ✓ 编译成功!" -ForegroundColor Green
    Write-Host "  可执行文件: $foundExe" -ForegroundColor White
    Write-Host "  文件大小: $($exe.Length) 字节" -ForegroundColor White
    Write-Host "  修改时间: $($exe.LastWriteTime)" -ForegroundColor White
    
    # 如果不在标准位置，复制到files目录
    $standardPath = Join-Path $ProjectPath "pcileech-master\files\pcileech.exe"
    if ($foundExe -ne $standardPath) {
        Write-Host "  复制到标准位置..." -ForegroundColor Yellow
        $filesDir = Join-Path $ProjectPath "pcileech-master\files"
        if (-not (Test-Path $filesDir)) {
            New-Item -ItemType Directory -Path $filesDir -Force | Out-Null
        }
        Copy-Item $foundExe $standardPath -Force
        Write-Host "  ✓ 已复制到: pcileech-master\files\pcileech.exe" -ForegroundColor Green
    }
    
    # 测试基本功能
    Write-Host "`n测试基本功能..." -ForegroundColor Cyan
    try {
        $testOutput = & $standardPath "info" 2>&1
        if ($testOutput -match "PCILeech") {
            Write-Host "  ✓ 基本功能测试通过" -ForegroundColor Green
        } else {
            Write-Host "  ⚠ 基本功能测试可能有问题" -ForegroundColor Yellow
            Write-Host "  输出: $testOutput" -ForegroundColor Gray
        }
    } catch {
        Write-Host "  ⚠ 无法运行基本功能测试: $($_.Exception.Message)" -ForegroundColor Yellow
    }
    
} else {
    Write-Host "  ✗ 编译失败，未找到可执行文件" -ForegroundColor Red
    Write-Host "  检查的路径:" -ForegroundColor Yellow
    foreach ($path in $possibleExePaths) {
        Write-Host "    - $path" -ForegroundColor Gray
    }
}

# 总结
Write-Host "`n=== 编译完成 ===" -ForegroundColor Green

if ($foundExe) {
    Write-Host "状态: 成功" -ForegroundColor Green
    Write-Host "下一步: 运行性能测试" -ForegroundColor Yellow
    Write-Host "命令: .\test_ft601_optimization.ps1" -ForegroundColor Gray
} else {
    Write-Host "状态: 失败" -ForegroundColor Red
    Write-Host "建议: 检查Visual Studio安装和依赖项" -ForegroundColor Yellow
}

Write-Host "`n编译日志已保存到控制台输出" -ForegroundColor Cyan
