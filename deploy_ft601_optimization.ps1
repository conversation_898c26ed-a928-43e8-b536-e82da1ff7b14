# FT601优化方案部署脚本
# PowerShell脚本用于自动化部署优化代码

param(
    [Parameter(Mandatory=$false)]
    [string]$ProjectPath = "G:\FPGA\20250327\100t20250618\100t484-1",
    
    [Parameter(Mandatory=$false)]
    [switch]$SkipBackup = $false,
    
    [Parameter(Mandatory=$false)]
    [switch]$OnlyFPGA = $false,
    
    [Parameter(Mandatory=$false)]
    [switch]$OnlyHost = $false,
    
    [Parameter(Mandatory=$false)]
    [switch]$RunTests = $true
)

# 设置错误处理
$ErrorActionPreference = "Stop"

Write-Host "=== FT601优化方案部署脚本 ===" -ForegroundColor Green
Write-Host "项目路径: $ProjectPath" -ForegroundColor Yellow

# 检查项目路径
if (-not (Test-Path $ProjectPath)) {
    Write-Error "项目路径不存在: $ProjectPath"
    exit 1
}

Set-Location $ProjectPath

# 1. 备份原始代码
if (-not $SkipBackup) {
    Write-Host "`n[1/6] 备份原始代码..." -ForegroundColor Cyan
    
    $backupPath = Join-Path $ProjectPath "backup_$(Get-Date -Format 'yyyyMMdd_HHmmss')"
    New-Item -ItemType Directory -Path $backupPath -Force | Out-Null
    
    # 备份关键文件
    $filesToBackup = @(
        "src\pcileech_ft601.sv",
        "pcileech-master\pcileech\device.c",
        "pcileech-master\pcileech\memdump.c",
        "src\pcileech_100t484_x1_top.sv"
    )
    
    foreach ($file in $filesToBackup) {
        $sourcePath = Join-Path $ProjectPath $file
        if (Test-Path $sourcePath) {
            $destPath = Join-Path $backupPath $file
            $destDir = Split-Path $destPath -Parent
            New-Item -ItemType Directory -Path $destDir -Force | Out-Null
            Copy-Item $sourcePath $destPath -Force
            Write-Host "  备份: $file" -ForegroundColor Gray
        }
    }
    
    Write-Host "  备份完成: $backupPath" -ForegroundColor Green
}

# 2. 应用FPGA端优化
if (-not $OnlyHost) {
    Write-Host "`n[2/6] 应用FPGA端优化..." -ForegroundColor Cyan
    
    # 检查是否存在优化版FT601控制器
    $optimizedFT601 = Join-Path $ProjectPath "src\pcileech_ft601_optimized.sv"
    if (Test-Path $optimizedFT601) {
        Write-Host "  应用优化版FT601控制器..." -ForegroundColor Yellow
        
        # 备份原始文件
        $originalFT601 = Join-Path $ProjectPath "src\pcileech_ft601.sv"
        $backupFT601 = Join-Path $ProjectPath "src\pcileech_ft601_original.sv"
        
        if (Test-Path $originalFT601) {
            Copy-Item $originalFT601 $backupFT601 -Force
            Write-Host "    原始文件备份为: pcileech_ft601_original.sv" -ForegroundColor Gray
        }
        
        # 应用优化版本
        Copy-Item $optimizedFT601 $originalFT601 -Force
        Write-Host "    优化版FT601控制器已应用" -ForegroundColor Green
        
        # 更新顶层模块引用
        $topModule = Join-Path $ProjectPath "src\pcileech_100t484_x1_top.sv"
        if (Test-Path $topModule) {
            Write-Host "  更新顶层模块引用..." -ForegroundColor Yellow
            
            # 读取文件内容
            $content = Get-Content $topModule -Raw
            
            # 替换模块实例化
            $content = $content -replace 'pcileech_ft601\s+', 'pcileech_ft601_optimized '
            
            # 写回文件
            Set-Content $topModule $content -Encoding UTF8
            Write-Host "    顶层模块已更新" -ForegroundColor Green
        }
    } else {
        Write-Warning "  优化版FT601控制器文件不存在，跳过FPGA优化"
    }
}

# 3. 应用上位机端优化
if (-not $OnlyFPGA) {
    Write-Host "`n[3/6] 应用上位机端优化..." -ForegroundColor Cyan
    
    # 检查优化版设备驱动
    $optimizedDevice = Join-Path $ProjectPath "pcileech-master\pcileech\device_optimized.c"
    if (Test-Path $optimizedDevice) {
        Write-Host "  集成优化版设备驱动..." -ForegroundColor Yellow
        
        # 更新项目文件
        $vcxproj = Join-Path $ProjectPath "pcileech-master\pcileech\pcileech.vcxproj"
        if (Test-Path $vcxproj) {
            $projContent = Get-Content $vcxproj -Raw
            
            # 添加新的源文件
            if ($projContent -notmatch "device_optimized.c") {
                $projContent = $projContent -replace '(<ClCompile Include="device.c" />)', 
                    '$1`n    <ClCompile Include="device_optimized.c" />'
                
                Set-Content $vcxproj $projContent -Encoding UTF8
                Write-Host "    项目文件已更新" -ForegroundColor Green
            }
        }
    }
    
    # 检查优化版内存转储
    $optimizedMemdump = Join-Path $ProjectPath "pcileech-master\pcileech\memdump_optimized.c"
    if (Test-Path $optimizedMemdump) {
        Write-Host "  集成优化版内存转储..." -ForegroundColor Yellow
        
        # 更新项目文件
        $vcxproj = Join-Path $ProjectPath "pcileech-master\pcileech\pcileech.vcxproj"
        if (Test-Path $vcxproj) {
            $projContent = Get-Content $vcxproj -Raw
            
            if ($projContent -notmatch "memdump_optimized.c") {
                $projContent = $projContent -replace '(<ClCompile Include="memdump.c" />)', 
                    '$1`n    <ClCompile Include="memdump_optimized.c" />'
                
                Set-Content $vcxproj $projContent -Encoding UTF8
                Write-Host "    项目文件已更新" -ForegroundColor Green
            }
        }
    }
    
    # 添加性能配置头文件
    $perfConfig = Join-Path $ProjectPath "pcileech-master\pcileech\ft601_performance_config.h"
    if (Test-Path $perfConfig) {
        Write-Host "  添加性能配置支持..." -ForegroundColor Yellow
        
        $vcxproj = Join-Path $ProjectPath "pcileech-master\pcileech\pcileech.vcxproj"
        if (Test-Path $vcxproj) {
            $projContent = Get-Content $vcxproj -Raw
            
            if ($projContent -notmatch "ft601_performance_config.h") {
                $projContent = $projContent -replace '(<ClInclude Include="util.h" />)', 
                    '$1`n    <ClInclude Include="ft601_performance_config.h" />'
                
                Set-Content $vcxproj $projContent -Encoding UTF8
                Write-Host "    性能配置头文件已添加" -ForegroundColor Green
            }
        }
    }
}

# 4. 更新构建脚本
Write-Host "`n[4/6] 更新构建脚本..." -ForegroundColor Cyan

# 检查Vivado构建脚本
$vivadoBuild = Join-Path $ProjectPath "vivado_build.tcl"
if (Test-Path $vivadoBuild) {
    Write-Host "  更新Vivado构建脚本..." -ForegroundColor Yellow
    
    $buildContent = Get-Content $vivadoBuild -Raw
    
    # 添加优化版文件到构建列表
    if ($buildContent -notmatch "pcileech_ft601_optimized.sv") {
        $buildContent = $buildContent -replace '(add_files.*pcileech_ft601\.sv)', 
            '$1`nadd_files -norecurse ../src/pcileech_ft601_optimized.sv'
        
        Set-Content $vivadoBuild $buildContent -Encoding UTF8
        Write-Host "    Vivado构建脚本已更新" -ForegroundColor Green
    }
}

# 5. 编译验证
Write-Host "`n[5/6] 编译验证..." -ForegroundColor Cyan

# 编译上位机代码
$slnPath = Join-Path $ProjectPath "pcileech-master\pcileech.sln"
if (Test-Path $slnPath) {
    Write-Host "  编译上位机代码..." -ForegroundColor Yellow
    
    try {
        $msbuildPath = "${env:ProgramFiles}\Microsoft Visual Studio\2022\Professional\MSBuild\Current\Bin\MSBuild.exe"
        if (-not (Test-Path $msbuildPath)) {
            $msbuildPath = "${env:ProgramFiles}\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\MSBuild.exe"
        }
        
        if (Test-Path $msbuildPath) {
            & $msbuildPath $slnPath /p:Configuration=Release /p:Platform=x64 /verbosity:minimal
            
            if ($LASTEXITCODE -eq 0) {
                Write-Host "    上位机代码编译成功" -ForegroundColor Green
            } else {
                Write-Warning "    上位机代码编译失败，请检查错误信息"
            }
        } else {
            Write-Warning "    未找到MSBuild，跳过编译验证"
        }
    } catch {
        Write-Warning "    编译过程中出现错误: $($_.Exception.Message)"
    }
}

# 6. 运行测试
if ($RunTests) {
    Write-Host "`n[6/6] 运行基础测试..." -ForegroundColor Cyan
    
    # 检查编译输出
    $exePath = Join-Path $ProjectPath "pcileech-master\files\pcileech.exe"
    if (Test-Path $exePath) {
        Write-Host "  运行基础功能测试..." -ForegroundColor Yellow
        
        try {
            # 运行信息查询测试
            $output = & $exePath "info" 2>&1
            if ($output -match "PCILeech") {
                Write-Host "    基础功能测试通过" -ForegroundColor Green
            } else {
                Write-Warning "    基础功能测试可能存在问题"
            }
        } catch {
            Write-Warning "    无法运行基础测试: $($_.Exception.Message)"
        }
    } else {
        Write-Warning "    未找到编译输出，跳过功能测试"
    }
}

# 部署完成
Write-Host "`n=== 部署完成 ===" -ForegroundColor Green
Write-Host "下一步操作建议:" -ForegroundColor Yellow
Write-Host "1. 使用Vivado重新综合FPGA设计" -ForegroundColor White
Write-Host "2. 烧录新的比特流文件到FPGA" -ForegroundColor White
Write-Host "3. 运行性能基准测试" -ForegroundColor White
Write-Host "4. 根据测试结果调整优化参数" -ForegroundColor White

# 生成部署报告
$reportPath = Join-Path $ProjectPath "deployment_report_$(Get-Date -Format 'yyyyMMdd_HHmmss').txt"
$report = @"
FT601优化方案部署报告
=====================

部署时间: $(Get-Date)
项目路径: $ProjectPath
部署选项: FPGA=$(-not $OnlyHost), Host=$(-not $OnlyFPGA), Tests=$RunTests

已应用的优化:
- FPGA端: 深度流水线、双缓冲机制、64位数据宽度
- 上位机端: 多线程架构、异步I/O、内存池管理
- 协议层: 自适应缓冲区、性能监控、智能重试

下一步操作:
1. 重新综合FPGA设计
2. 烧录新比特流
3. 运行性能测试
4. 参数调优

备份位置: $backupPath
"@

Set-Content $reportPath $report -Encoding UTF8
Write-Host "`n部署报告已保存: $reportPath" -ForegroundColor Cyan
