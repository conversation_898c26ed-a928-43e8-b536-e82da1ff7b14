# FT601优化方案部署检查清单

## 开发环境要求

### FPGA开发环境
- [ ] Vivado 2022.1 或更高版本
- [ ] Xilinx Artix-7 开发板支持
- [ ] SystemVerilog 编译器支持
- [ ] 足够的FPGA资源 (建议xc7a100t或更大)

### 软件开发环境
- [ ] Visual Studio 2022 (v143工具集)
- [ ] Windows SDK 10.0 或更高版本
- [ ] Git 版本控制
- [ ] CMake 3.20+ (可选，用于跨平台构建)

### 硬件要求
- [ ] PCILeech FPGA板卡
- [ ] FT601 USB3.0控制器
- [ ] 测试目标系统
- [ ] 高速USB3.0连接线

## 备份现有代码
```bash
# 创建备份分支
git checkout -b backup-original
git add .
git commit -m "Backup original code before optimization"

# 创建优化分支
git checkout -b ft601-optimization
```

## 依赖库检查
- [ ] LeechCore 2.20+
- [ ] MemProcFS/VMM库
- [ ] Windows Performance Toolkit (可选)
- [ ] 性能分析工具
