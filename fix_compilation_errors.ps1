# 修复编译错误的脚本
# 专门处理PCILeech优化版的编译问题

param(
    [Parameter(Mandatory=$false)]
    [string]$ProjectPath = "G:\FPGA\20250327\100t20250618\100t484-1"
)

$ErrorActionPreference = "Stop"

Write-Host "=== PCILeech 编译错误修复 ===" -ForegroundColor Green
Write-Host "项目路径: $ProjectPath" -ForegroundColor Yellow

Set-Location $ProjectPath

# 1. 检查并修复头文件包含问题
Write-Host "`n[1/5] 检查头文件包含..." -ForegroundColor Cyan

$deviceFile = "pcileech-master\pcileech\device.c"
if (Test-Path $deviceFile) {
    $content = Get-Content $deviceFile -Raw
    
    # 确保正确包含头文件
    if ($content -notmatch '#include "ft601_performance_config.h"') {
        Write-Host "  添加缺失的头文件包含..." -ForegroundColor Yellow
        $content = $content -replace '(#include "vmmx.h")', '$1`n#include "ft601_performance_config.h"'
        Set-Content $deviceFile $content -Encoding UTF8
    }
    Write-Host "  ✓ 头文件包含检查完成" -ForegroundColor Green
}

# 2. 检查并修复结构体定义
Write-Host "`n[2/5] 检查结构体定义..." -ForegroundColor Cyan

$headerFile = "pcileech-master\pcileech\ft601_performance_config.h"
if (Test-Path $headerFile) {
    $content = Get-Content $headerFile -Raw
    
    # 确保有正确的包含保护
    if ($content -notmatch '#ifndef __FT601_PERFORMANCE_CONFIG_H__') {
        Write-Host "  添加头文件保护..." -ForegroundColor Yellow
        $newContent = @"
#ifndef __FT601_PERFORMANCE_CONFIG_H__
#define __FT601_PERFORMANCE_CONFIG_H__

#ifdef __cplusplus
extern "C" {
#endif

#include <windows.h>
#include <leechcore.h>

$content

#ifdef __cplusplus
}
#endif

#endif /* __FT601_PERFORMANCE_CONFIG_H__ */
"@
        Set-Content $headerFile $newContent -Encoding UTF8
    }
    Write-Host "  ✓ 结构体定义检查完成" -ForegroundColor Green
}

# 3. 检查并修复常量定义
Write-Host "`n[3/5] 检查常量定义..." -ForegroundColor Cyan

$optimizedFile = "pcileech-master\pcileech\memdump_optimized.c"
if (Test-Path $optimizedFile) {
    $content = Get-Content $optimizedFile -Raw
    
    # 确保包含必要的头文件
    if ($content -notmatch '#include "ft601_performance_config.h"') {
        Write-Host "  添加性能配置头文件..." -ForegroundColor Yellow
        $content = $content -replace '(#include "util.h")', '$1`n#include "ft601_performance_config.h"'
        Set-Content $optimizedFile $content -Encoding UTF8
    }
    Write-Host "  ✓ 常量定义检查完成" -ForegroundColor Green
}

# 4. 尝试编译并检查错误
Write-Host "`n[4/5] 尝试编译..." -ForegroundColor Cyan

# 查找MSBuild
$msbuildPaths = @(
    "${env:ProgramFiles}\Microsoft Visual Studio\2022\Professional\MSBuild\Current\Bin\MSBuild.exe",
    "${env:ProgramFiles}\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\MSBuild.exe",
    "${env:ProgramFiles(x86)}\Microsoft Visual Studio\2019\Professional\MSBuild\Current\Bin\MSBuild.exe",
    "${env:ProgramFiles(x86)}\Microsoft Visual Studio\2019\Community\MSBuild\Current\Bin\MSBuild.exe"
)

$msbuildPath = $null
foreach ($path in $msbuildPaths) {
    if (Test-Path $path) {
        $msbuildPath = $path
        break
    }
}

if ($msbuildPath) {
    Write-Host "  MSBuild路径: $msbuildPath" -ForegroundColor White
    
    try {
        # 清理项目
        & $msbuildPath "pcileech-master\pcileech\pcileech.vcxproj" /t:Clean /p:Configuration=Release /p:Platform=x64 /verbosity:quiet /nologo
        
        # 尝试编译
        $output = & $msbuildPath "pcileech-master\pcileech\pcileech.vcxproj" /p:Configuration=Release /p:Platform=x64 /verbosity:normal /nologo 2>&1
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "  ✓ 编译成功!" -ForegroundColor Green
            
            # 检查输出文件
            if (Test-Path "pcileech-master\pcileech\x64\Release\pcileech.exe") {
                $exe = Get-Item "pcileech-master\pcileech\x64\Release\pcileech.exe"
                Write-Host "    可执行文件: $($exe.FullName)" -ForegroundColor White
                Write-Host "    文件大小: $($exe.Length) 字节" -ForegroundColor White
                Write-Host "    修改时间: $($exe.LastWriteTime)" -ForegroundColor White
                
                # 复制到标准位置
                if (-not (Test-Path "pcileech-master\files")) {
                    New-Item -ItemType Directory -Path "pcileech-master\files" -Force | Out-Null
                }
                Copy-Item $exe.FullName "pcileech-master\files\pcileech.exe" -Force
                Write-Host "    ✓ 已复制到标准位置" -ForegroundColor Green
            }
        } else {
            Write-Host "  ✗ 编译失败" -ForegroundColor Red
            Write-Host "  编译输出:" -ForegroundColor Yellow
            $output | ForEach-Object { Write-Host "    $_" -ForegroundColor Gray }
        }
    } catch {
        Write-Host "  编译过程出错: $($_.Exception.Message)" -ForegroundColor Red
    }
} else {
    Write-Host "  ✗ 未找到MSBuild" -ForegroundColor Red
}

# 5. 总结
Write-Host "`n[5/5] 修复总结..." -ForegroundColor Cyan

if (Test-Path "pcileech-master\files\pcileech.exe") {
    Write-Host "  ✓ 编译成功，可执行文件已生成" -ForegroundColor Green
    Write-Host "  位置: pcileech-master\files\pcileech.exe" -ForegroundColor White
} else {
    Write-Host "  ✗ 编译仍有问题，需要进一步调试" -ForegroundColor Red
    Write-Host "  建议:" -ForegroundColor Yellow
    Write-Host "    1. 检查Visual Studio安装" -ForegroundColor Gray
    Write-Host "    2. 确认所有依赖项已安装" -ForegroundColor Gray
    Write-Host "    3. 手动在Visual Studio中打开项目进行调试" -ForegroundColor Gray
}

Write-Host "`n=== 修复完成 ===" -ForegroundColor Green
