# 最小编译测试脚本
# 专门用于诊断编译问题

param(
    [Parameter(Mandatory=$false)]
    [string]$ProjectPath = "G:\FPGA\20250327\100t20250618\100t484-1"
)

Write-Host "=== 最小编译测试 ===" -ForegroundColor Green
Write-Host "项目路径: $ProjectPath" -ForegroundColor Yellow

Set-Location $ProjectPath

# 查找MSBuild
$msbuildPath = $null
$msbuildPaths = @(
    "${env:ProgramFiles}\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\MSBuild.exe",
    "${env:ProgramFiles(x86)}\Microsoft Visual Studio\2019\Community\MSBuild\Current\Bin\MSBuild.exe"
)

foreach ($path in $msbuildPaths) {
    if (Test-Path $path) {
        $msbuildPath = $path
        break
    }
}

if (-not $msbuildPath) {
    Write-Host "未找到MSBuild" -ForegroundColor Red
    exit 1
}

Write-Host "MSBuild: $msbuildPath" -ForegroundColor Cyan

# 尝试编译单个文件
Write-Host "`n测试编译 ft601_performance_config.c..." -ForegroundColor Yellow

$output = & $msbuildPath "pcileech-master\pcileech\pcileech.vcxproj" /p:Configuration=Release /p:Platform=x64 /verbosity:detailed /nologo 2>&1

# 分析输出
$errors = $output | Where-Object { $_ -match "error" }
$warnings = $output | Where-Object { $_ -match "warning" }

if ($LASTEXITCODE -eq 0) {
    Write-Host "编译成功!" -ForegroundColor Green
    
    if (Test-Path "pcileech-master\pcileech\x64\Release\pcileech.exe") {
        $exe = Get-Item "pcileech-master\pcileech\x64\Release\pcileech.exe"
        Write-Host "可执行文件: $($exe.Length) 字节" -ForegroundColor Green
        
        # 复制到标准位置
        if (-not (Test-Path "pcileech-master\files")) {
            New-Item -ItemType Directory -Path "pcileech-master\files" -Force | Out-Null
        }
        Copy-Item $exe.FullName "pcileech-master\files\pcileech.exe" -Force
        Write-Host "已复制到标准位置" -ForegroundColor Green
    }
} else {
    Write-Host "编译失败" -ForegroundColor Red
    
    if ($errors.Count -gt 0) {
        Write-Host "`n错误 ($($errors.Count)):" -ForegroundColor Red
        $errors | Select-Object -First 10 | ForEach-Object { 
            Write-Host "  $_" -ForegroundColor Red 
        }
    }
    
    if ($warnings.Count -gt 0) {
        Write-Host "`n警告 ($($warnings.Count)):" -ForegroundColor Yellow
        $warnings | Select-Object -First 5 | ForEach-Object { 
            Write-Host "  $_" -ForegroundColor Yellow 
        }
    }
    
    # 查找特定错误模式
    $syntaxErrors = $output | Where-Object { $_ -match "syntax error" }
    $redefinitionErrors = $output | Where-Object { $_ -match "redefinition" }
    
    if ($syntaxErrors.Count -gt 0) {
        Write-Host "`n语法错误:" -ForegroundColor Magenta
        $syntaxErrors | Select-Object -First 5 | ForEach-Object { 
            Write-Host "  $_" -ForegroundColor Magenta 
        }
    }
    
    if ($redefinitionErrors.Count -gt 0) {
        Write-Host "`n重定义错误:" -ForegroundColor Magenta
        $redefinitionErrors | ForEach-Object { 
            Write-Host "  $_" -ForegroundColor Magenta 
        }
    }
}

Write-Host "`n=== 测试完成 ===" -ForegroundColor Green
