﻿  charutil.c
  device.c
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\device.c(1,1): warning C4819: The file contains a character that cannot be represented in the current code page (936). Save the file in Unicode format to prevent data loss
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\ft601_performance_config.h(1,1): warning C4819: The file contains a character that cannot be represented in the current code page (936). Save the file in Unicode format to prevent data loss
  (compiling source file 'device.c')
  
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\ft601_performance_config.h(70,2): warning C4091: 'typedef ': ignored on left of 'tdFT601_PERFORMANCE_CONFIG' when no variable is declared
  (compiling source file 'device.c')
  
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\ft601_performance_config.h(96,2): warning C4091: 'typedef ': ignored on left of 'tdFT601_PERFORMANCE_STATS' when no variable is declared
  (compiling source file 'device.c')
  
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\ft601_performance_config.h(145,40): error C2146: syntax error: missing ')' before identifier 'pConfig'
  (compiling source file 'device.c')
  
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\ft601_performance_config.h(145,10): error C2081: 'PFT601_PERFORMANCE_CONFIG_PTR': name in formal parameter list illegal
  (compiling source file 'device.c')
  
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\ft601_performance_config.h(145,40): error C2061: syntax error: identifier 'pConfig'
  (compiling source file 'device.c')
  
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\ft601_performance_config.h(145,40): error C2059: syntax error: ';'
  (compiling source file 'device.c')
  
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\ft601_performance_config.h(146,1): error C2059: syntax error: ')'
  (compiling source file 'device.c')
  
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\ft601_performance_config.h(150,39): error C2146: syntax error: missing ')' before identifier 'pStats'
  (compiling source file 'device.c')
  
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\ft601_performance_config.h(150,10): error C2081: 'PFT601_PERFORMANCE_STATS_PTR': name in formal parameter list illegal
  (compiling source file 'device.c')
  
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\ft601_performance_config.h(150,39): error C2061: syntax error: identifier 'pStats'
  (compiling source file 'device.c')
  
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\ft601_performance_config.h(150,39): error C2059: syntax error: ';'
  (compiling source file 'device.c')
  
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\ft601_performance_config.h(150,45): error C2059: syntax error: ','
  (compiling source file 'device.c')
  
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\ft601_performance_config.h(152,1): error C2059: syntax error: ')'
  (compiling source file 'device.c')
  
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\ft601_performance_config.h(181,43): error C2146: syntax error: missing ')' before identifier 'pConfig'
  (compiling source file 'device.c')
  
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\ft601_performance_config.h(181,43): error C2061: syntax error: identifier 'pConfig'
  (compiling source file 'device.c')
  
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\ft601_performance_config.h(181,43): error C2059: syntax error: ';'
  (compiling source file 'device.c')
  
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\ft601_performance_config.h(181,50): error C2059: syntax error: ','
  (compiling source file 'device.c')
  
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\ft601_performance_config.h(183,1): error C2059: syntax error: ')'
  (compiling source file 'device.c')
  
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\ft601_performance_config.h(187,43): error C2146: syntax error: missing ')' before identifier 'pConfig'
  (compiling source file 'device.c')
  
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\ft601_performance_config.h(187,43): error C2061: syntax error: identifier 'pConfig'
  (compiling source file 'device.c')
  
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\ft601_performance_config.h(187,43): error C2059: syntax error: ';'
  (compiling source file 'device.c')
  
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\ft601_performance_config.h(187,50): error C2059: syntax error: ','
  (compiling source file 'device.c')
  
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\ft601_performance_config.h(189,1): error C2059: syntax error: ')'
  (compiling source file 'device.c')
  
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\ft601_performance_config.h(193,43): error C2146: syntax error: missing ')' before identifier 'pConfig'
  (compiling source file 'device.c')
  
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\ft601_performance_config.h(193,43): error C2061: syntax error: identifier 'pConfig'
  (compiling source file 'device.c')
  
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\ft601_performance_config.h(193,43): error C2059: syntax error: ';'
  (compiling source file 'device.c')
  
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\ft601_performance_config.h(193,50): error C2059: syntax error: ','
  (compiling source file 'device.c')
  
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\ft601_performance_config.h(195,1): error C2059: syntax error: ')'
  (compiling source file 'device.c')
  
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\ft601_performance_config.h(202,40): error C2146: syntax error: missing ')' before identifier 'pResults'
  (compiling source file 'device.c')
  
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\ft601_performance_config.h(202,11): error C2081: 'PFT601_PERFORMANCE_STATS_PTR': name in formal parameter list illegal
  (compiling source file 'device.c')
  
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\ft601_performance_config.h(202,40): error C2061: syntax error: identifier 'pResults'
  (compiling source file 'device.c')
  
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\ft601_performance_config.h(202,40): error C2059: syntax error: ';'
  (compiling source file 'device.c')
  
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\ft601_performance_config.h(203,1): error C2059: syntax error: ')'
  (compiling source file 'device.c')
  
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\ft601_performance_config.h(223,39): error C2061: syntax error: identifier 'pStats'
  (compiling source file 'device.c')
  
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\ft601_performance_config.h(223,39): error C2059: syntax error: ';'
  (compiling source file 'device.c')
  
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\ft601_performance_config.h(223,45): error C2059: syntax error: ','
  (compiling source file 'device.c')
  
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\ft601_performance_config.h(225,1): error C2059: syntax error: ')'
  (compiling source file 'device.c')
  
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\ft601_performance_config.h(229,39): error C2146: syntax error: missing ')' before identifier 'pStats'
  (compiling source file 'device.c')
  
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\ft601_performance_config.h(229,39): error C2061: syntax error: identifier 'pStats'
  (compiling source file 'device.c')
  
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\ft601_performance_config.h(229,39): error C2059: syntax error: ';'
  (compiling source file 'device.c')
  
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\ft601_performance_config.h(229,45): error C2059: syntax error: ','
  (compiling source file 'device.c')
  
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\ft601_performance_config.h(231,1): error C2059: syntax error: ')'
  (compiling source file 'device.c')
  
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\ft601_performance_config.h(235,39): error C2146: syntax error: missing ')' before identifier 'pStats'
  (compiling source file 'device.c')
  
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\ft601_performance_config.h(235,39): error C2061: syntax error: identifier 'pStats'
  (compiling source file 'device.c')
  
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\ft601_performance_config.h(235,39): error C2059: syntax error: ';'
  (compiling source file 'device.c')
  
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\ft601_performance_config.h(235,45): error C2059: syntax error: ','
  (compiling source file 'device.c')
  
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\ft601_performance_config.h(237,1): error C2059: syntax error: ')'
  (compiling source file 'device.c')
  
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\ft601_performance_config.h(249,67): error C2146: syntax error: missing ')' before identifier 'pConfig'
  (compiling source file 'device.c')
  
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\ft601_performance_config.h(249,67): error C2061: syntax error: identifier 'pConfig'
  (compiling source file 'device.c')
  
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\ft601_performance_config.h(249,67): error C2059: syntax error: ';'
  (compiling source file 'device.c')
  
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\ft601_performance_config.h(249,74): error C2059: syntax error: ')'
  (compiling source file 'device.c')
  
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\ft601_performance_config.h(250,65): error C2146: syntax error: missing ')' before identifier 'pConfig'
  (compiling source file 'device.c')
  
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\ft601_performance_config.h(250,65): error C2061: syntax error: identifier 'pConfig'
  (compiling source file 'device.c')
  
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\ft601_performance_config.h(250,65): error C2059: syntax error: ';'
  (compiling source file 'device.c')
  
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\ft601_performance_config.h(250,72): error C2059: syntax error: ')'
  (compiling source file 'device.c')
  
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\device.c(218,5): warning C4013: 'FT601_InitDefaultConfig' undefined; assuming extern returning int
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\device.c(231,22): error C2039: 'dwBufferSize': is not a member of 'tdFT601_PERFORMANCE_CONFIG'
      G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\ft601_performance_config.h(45,16):
      see declaration of 'tdFT601_PERFORMANCE_CONFIG'
  
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\device.c(247,5): error C2374: 'g_fPerfConfigInitialized': redefinition; multiple initialization
      G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\device.c(15,13):
      see declaration of 'g_fPerfConfigInitialized'
  
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\device.c(248,5): error C2059: syntax error: 'return'
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\device.c(249,1): error C2059: syntax error: '}'
  executor.c
  extra.c
  help.c
  kmd.c
  memdump.c
  mempatch.c
  ob_cachemap.c
  ob_core.c
  ob_map.c
  ob_set.c
  oscompatibility.c
  pcileech.c
  statistics.c
  umd.c
  util.c
  vfs.c
  vfslist.c
  vmmx.c
  Generating Code...
  Compiling...
  device_optimized.c
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\device_optimized.c(1,1): warning C4819: The file contains a character that cannot be represented in the current code page (936). Save the file in Unicode format to prevent data loss
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\device_optimized.c(70,9): error C2143: syntax error: missing ')' before 'if'
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\device_optimized.c(88,5): error C2059: syntax error: 'return'
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\device_optimized.c(89,1): error C2059: syntax error: '}'
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\device_optimized.c(143,1): error C2449: found '{' at file scope (missing function header?)
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\device_optimized.c(175,1): error C2059: syntax error: '}'
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\device_optimized.c(198,1): error C2449: found '{' at file scope (missing function header?)
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\device_optimized.c(204,1): error C2059: syntax error: '}'
  memdump_optimized.c
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\memdump_optimized.c(1,1): warning C4819: The file contains a character that cannot be represented in the current code page (936). Save the file in Unicode format to prevent data loss
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\memdump_optimized.c(41,27): error C2065: 'OPTIMIZED_WRITER_THREADS': undeclared identifier
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\memdump_optimized.c(41,27): error C2057: expected constant expression
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\memdump_optimized.c(42,12): error C2229: struct 'tdOPTIMIZED_FILEWRITE' has an illegal zero-sized array
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\memdump_optimized.c(49,5): error C2061: syntax error: identifier 'OPTIMIZED_FILEWRITE_DATA'
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\memdump_optimized.c(61,1): error C2059: syntax error: '}'
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\memdump_optimized.c(64,62): error C2146: syntax error: missing ')' before identifier 'ctx'
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\memdump_optimized.c(64,62): error C2061: syntax error: identifier 'ctx'
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\memdump_optimized.c(64,62): error C2059: syntax error: ';'
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\memdump_optimized.c(64,65): error C2059: syntax error: ')'
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\memdump_optimized.c(65,1): error C2449: found '{' at file scope (missing function header?)
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\memdump_optimized.c(131,1): error C2059: syntax error: '}'
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\memdump_optimized.c(193,1): error C2449: found '{' at file scope (missing function header?)
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\memdump_optimized.c(238,5): error C2059: syntax error: '}'
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\memdump_optimized.c(241,8): error C2143: syntax error: missing '{' before '->'
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\memdump_optimized.c(241,8): error C2059: syntax error: '->'
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\memdump_optimized.c(243,5): error C2059: syntax error: 'if'
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\memdump_optimized.c(249,5): error C2059: syntax error: 'for'
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\memdump_optimized.c(249,19): error C2143: syntax error: missing '{' before '<'
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\memdump_optimized.c(249,19): error C2059: syntax error: '<'
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\memdump_optimized.c(249,44): error C2143: syntax error: missing '{' before '++'
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\memdump_optimized.c(249,44): error C2059: syntax error: '++'
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\memdump_optimized.c(249,46): error C2059: syntax error: ')'
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\memdump_optimized.c(261,8): error C2143: syntax error: missing '{' before '->'
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\memdump_optimized.c(261,8): error C2059: syntax error: '->'
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\memdump_optimized.c(262,29): error C2143: syntax error: missing ')' before '&'
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\memdump_optimized.c(262,29): error C2143: syntax error: missing '{' before '&'
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\memdump_optimized.c(262,29): error C2059: syntax error: '&'
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\memdump_optimized.c(262,46): error C2059: syntax error: ')'
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\memdump_optimized.c(265,5): error C2059: syntax error: 'for'
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\memdump_optimized.c(265,19): error C2143: syntax error: missing '{' before '<'
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\memdump_optimized.c(265,19): error C2059: syntax error: '<'
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\memdump_optimized.c(265,48): error C2143: syntax error: missing '{' before '++'
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\memdump_optimized.c(265,48): error C2059: syntax error: '++'
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\memdump_optimized.c(265,50): error C2059: syntax error: ')'
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\memdump_optimized.c(274,5): error C2059: syntax error: 'for'
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\memdump_optimized.c(274,19): error C2143: syntax error: missing '{' before '<'
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\memdump_optimized.c(274,19): error C2059: syntax error: '<'
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\memdump_optimized.c(274,48): error C2143: syntax error: missing '{' before '++'
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\memdump_optimized.c(274,48): error C2059: syntax error: '++'
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\memdump_optimized.c(274,50): error C2059: syntax error: ')'
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\memdump_optimized.c(282,5): error C2059: syntax error: 'return'
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\memdump_optimized.c(284,5): error C2143: syntax error: missing '{' before ':'
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\memdump_optimized.c(284,5): error C2059: syntax error: ':'
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\memdump_optimized.c(288,5): error C2059: syntax error: 'return'
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\memdump_optimized.c(289,1): error C2059: syntax error: '}'
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\memdump_optimized.c(309,5): error C2059: syntax error: 'if'
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\memdump_optimized.c(319,5): error C2059: syntax error: 'if'
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\memdump_optimized.c(320,5): error C2059: syntax error: 'if'
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\memdump_optimized.c(321,27): error C2143: syntax error: missing ')' before '&'
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\memdump_optimized.c(321,27): error C2143: syntax error: missing '{' before '&'
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\memdump_optimized.c(321,27): error C2059: syntax error: '&'
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\memdump_optimized.c(321,41): error C2059: syntax error: ')'
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\memdump_optimized.c(324,9): error C2059: syntax error: 'if'
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\memdump_optimized.c(327,5): error C2059: syntax error: '}'
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\memdump_optimized.c(335,5): error C2059: syntax error: 'if'
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\memdump_optimized.c(356,5): error C2040: 'LocalFree': 'int ()' differs in levels of indirection from 'HLOCAL (HLOCAL)'
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\memdump_optimized.c(357,1): error C2059: syntax error: '}'
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\memdump_optimized.c(398,5): error C2059: syntax error: '}'
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\memdump_optimized.c(402,5): error C2059: syntax error: '}'
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\memdump_optimized.c(406,5): error C2059: syntax error: 'if'
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\memdump_optimized.c(411,1): error C2059: syntax error: '}'
  ft601_performance_config.c
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\ft601_performance_config.c(1,1): warning C4819: The file contains a character that cannot be represented in the current code page (936). Save the file in Unicode format to prevent data loss
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\ft601_performance_config.h(1,1): warning C4819: The file contains a character that cannot be represented in the current code page (936). Save the file in Unicode format to prevent data loss
  (compiling source file 'ft601_performance_config.c')
  
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\ft601_performance_config.h(70,2): warning C4091: 'typedef ': ignored on left of 'tdFT601_PERFORMANCE_CONFIG' when no variable is declared
  (compiling source file 'ft601_performance_config.c')
  
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\ft601_performance_config.h(96,2): warning C4091: 'typedef ': ignored on left of 'tdFT601_PERFORMANCE_STATS' when no variable is declared
  (compiling source file 'ft601_performance_config.c')
  
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\ft601_performance_config.h(145,40): error C2146: syntax error: missing ')' before identifier 'pConfig'
  (compiling source file 'ft601_performance_config.c')
  
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\ft601_performance_config.h(145,10): error C2081: 'PFT601_PERFORMANCE_CONFIG_PTR': name in formal parameter list illegal
  (compiling source file 'ft601_performance_config.c')
  
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\ft601_performance_config.h(145,40): error C2061: syntax error: identifier 'pConfig'
  (compiling source file 'ft601_performance_config.c')
  
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\ft601_performance_config.h(145,40): error C2059: syntax error: ';'
  (compiling source file 'ft601_performance_config.c')
  
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\ft601_performance_config.h(146,1): error C2059: syntax error: ')'
  (compiling source file 'ft601_performance_config.c')
  
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\ft601_performance_config.h(150,39): error C2146: syntax error: missing ')' before identifier 'pStats'
  (compiling source file 'ft601_performance_config.c')
  
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\ft601_performance_config.h(150,10): error C2081: 'PFT601_PERFORMANCE_STATS_PTR': name in formal parameter list illegal
  (compiling source file 'ft601_performance_config.c')
  
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\ft601_performance_config.h(150,39): error C2061: syntax error: identifier 'pStats'
  (compiling source file 'ft601_performance_config.c')
  
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\ft601_performance_config.h(150,39): error C2059: syntax error: ';'
  (compiling source file 'ft601_performance_config.c')
  
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\ft601_performance_config.h(150,45): error C2059: syntax error: ','
  (compiling source file 'ft601_performance_config.c')
  
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\ft601_performance_config.h(152,1): error C2059: syntax error: ')'
  (compiling source file 'ft601_performance_config.c')
  
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\ft601_performance_config.h(181,43): error C2146: syntax error: missing ')' before identifier 'pConfig'
  (compiling source file 'ft601_performance_config.c')
  
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\ft601_performance_config.h(181,43): error C2061: syntax error: identifier 'pConfig'
  (compiling source file 'ft601_performance_config.c')
  
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\ft601_performance_config.h(181,43): error C2059: syntax error: ';'
  (compiling source file 'ft601_performance_config.c')
  
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\ft601_performance_config.h(181,50): error C2059: syntax error: ','
  (compiling source file 'ft601_performance_config.c')
  
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\ft601_performance_config.h(183,1): error C2059: syntax error: ')'
  (compiling source file 'ft601_performance_config.c')
  
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\ft601_performance_config.h(187,43): error C2146: syntax error: missing ')' before identifier 'pConfig'
  (compiling source file 'ft601_performance_config.c')
  
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\ft601_performance_config.h(187,43): error C2061: syntax error: identifier 'pConfig'
  (compiling source file 'ft601_performance_config.c')
  
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\ft601_performance_config.h(187,43): error C2059: syntax error: ';'
  (compiling source file 'ft601_performance_config.c')
  
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\ft601_performance_config.h(187,50): error C2059: syntax error: ','
  (compiling source file 'ft601_performance_config.c')
  
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\ft601_performance_config.h(189,1): error C2059: syntax error: ')'
  (compiling source file 'ft601_performance_config.c')
  
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\ft601_performance_config.h(193,43): error C2146: syntax error: missing ')' before identifier 'pConfig'
  (compiling source file 'ft601_performance_config.c')
  
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\ft601_performance_config.h(193,43): error C2061: syntax error: identifier 'pConfig'
  (compiling source file 'ft601_performance_config.c')
  
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\ft601_performance_config.h(193,43): error C2059: syntax error: ';'
  (compiling source file 'ft601_performance_config.c')
  
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\ft601_performance_config.h(193,50): error C2059: syntax error: ','
  (compiling source file 'ft601_performance_config.c')
  
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\ft601_performance_config.h(195,1): error C2059: syntax error: ')'
  (compiling source file 'ft601_performance_config.c')
  
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\ft601_performance_config.h(202,40): error C2146: syntax error: missing ')' before identifier 'pResults'
  (compiling source file 'ft601_performance_config.c')
  
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\ft601_performance_config.h(202,11): error C2081: 'PFT601_PERFORMANCE_STATS_PTR': name in formal parameter list illegal
  (compiling source file 'ft601_performance_config.c')
  
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\ft601_performance_config.h(202,40): error C2061: syntax error: identifier 'pResults'
  (compiling source file 'ft601_performance_config.c')
  
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\ft601_performance_config.h(202,40): error C2059: syntax error: ';'
  (compiling source file 'ft601_performance_config.c')
  
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\ft601_performance_config.h(203,1): error C2059: syntax error: ')'
  (compiling source file 'ft601_performance_config.c')
  
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\ft601_performance_config.h(223,39): error C2061: syntax error: identifier 'pStats'
  (compiling source file 'ft601_performance_config.c')
  
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\ft601_performance_config.h(223,39): error C2059: syntax error: ';'
  (compiling source file 'ft601_performance_config.c')
  
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\ft601_performance_config.h(223,45): error C2059: syntax error: ','
  (compiling source file 'ft601_performance_config.c')
  
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\ft601_performance_config.h(225,1): error C2059: syntax error: ')'
  (compiling source file 'ft601_performance_config.c')
  
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\ft601_performance_config.h(229,39): error C2146: syntax error: missing ')' before identifier 'pStats'
  (compiling source file 'ft601_performance_config.c')
  
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\ft601_performance_config.h(229,39): error C2061: syntax error: identifier 'pStats'
  (compiling source file 'ft601_performance_config.c')
  
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\ft601_performance_config.h(229,39): error C2059: syntax error: ';'
  (compiling source file 'ft601_performance_config.c')
  
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\ft601_performance_config.h(229,45): error C2059: syntax error: ','
  (compiling source file 'ft601_performance_config.c')
  
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\ft601_performance_config.h(231,1): error C2059: syntax error: ')'
  (compiling source file 'ft601_performance_config.c')
  
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\ft601_performance_config.h(235,39): error C2146: syntax error: missing ')' before identifier 'pStats'
  (compiling source file 'ft601_performance_config.c')
  
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\ft601_performance_config.h(235,39): error C2061: syntax error: identifier 'pStats'
  (compiling source file 'ft601_performance_config.c')
  
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\ft601_performance_config.h(235,39): error C2059: syntax error: ';'
  (compiling source file 'ft601_performance_config.c')
  
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\ft601_performance_config.h(235,45): error C2059: syntax error: ','
  (compiling source file 'ft601_performance_config.c')
  
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\ft601_performance_config.h(237,1): error C2059: syntax error: ')'
  (compiling source file 'ft601_performance_config.c')
  
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\ft601_performance_config.h(249,67): error C2146: syntax error: missing ')' before identifier 'pConfig'
  (compiling source file 'ft601_performance_config.c')
  
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\ft601_performance_config.h(249,67): error C2061: syntax error: identifier 'pConfig'
  (compiling source file 'ft601_performance_config.c')
  
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\ft601_performance_config.h(249,67): error C2059: syntax error: ';'
  (compiling source file 'ft601_performance_config.c')
  
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\ft601_performance_config.h(249,74): error C2059: syntax error: ')'
  (compiling source file 'ft601_performance_config.c')
  
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\ft601_performance_config.h(250,65): error C2146: syntax error: missing ')' before identifier 'pConfig'
  (compiling source file 'ft601_performance_config.c')
  
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\ft601_performance_config.h(250,65): error C2061: syntax error: identifier 'pConfig'
  (compiling source file 'ft601_performance_config.c')
  
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\ft601_performance_config.h(250,65): error C2059: syntax error: ';'
  (compiling source file 'ft601_performance_config.c')
  
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\ft601_performance_config.h(250,72): error C2059: syntax error: ')'
  (compiling source file 'ft601_performance_config.c')
  
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\ft601_performance_config.c(19,1): error C2449: found '{' at file scope (missing function header?)
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\ft601_performance_config.c(47,1): error C2059: syntax error: '}'
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\ft601_performance_config.c(64,1): error C2449: found '{' at file scope (missing function header?)
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\ft601_performance_config.c(74,1): error C2059: syntax error: '}'
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\ft601_performance_config.c(99,32): error C2143: syntax error: missing ';' before '/'
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\ft601_performance_config.c(101,13): error C2065: 'dElapsedSeconds': undeclared identifier
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\ft601_performance_config.c(105,35): error C2065: 'qwCurrentBytesRead': undeclared identifier
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\ft601_performance_config.c(109,67): error C2065: 'dElapsedSeconds': undeclared identifier
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\ft601_performance_config.c(115,40): error C2065: 'FT601_OPT_PERF_HISTORY_SIZE': undeclared identifier
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\ft601_performance_config.c(127,39): error C2065: 'FT601_OPT_PERF_HISTORY_SIZE': undeclared identifier
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\ft601_performance_config.c(127,37): warning C4018: '<': signed/unsigned mismatch
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\ft601_performance_config.c(130,61): error C2065: 'FT601_OPT_PERF_HISTORY_SIZE': undeclared identifier
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\ft601_performance_config.c(135,17): warning C4013: 'FT601_UpdateAdaptiveParameters' undefined; assuming extern returning int
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\ft601_performance_config.c(140,17): warning C4013: 'FT601_LogPerformanceData' undefined; assuming extern returning int
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\ft601_performance_config.c(143,31): error C2065: 'qwCurrentBytesRead': undeclared identifier
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\ft601_performance_config.c(144,34): error C2065: 'qwCurrentBytesWritten': undeclared identifier
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\ft601_performance_config.c(147,5): error C2059: syntax error: '}'
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\ft601_performance_config.c(150,1): error C2059: syntax error: '}'
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\ft601_performance_config.c(217,5): error C2059: syntax error: 'if'
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\ft601_performance_config.c(221,5): error C2059: syntax error: 'return'
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\ft601_performance_config.c(222,1): error C2059: syntax error: '}'
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\ft601_performance_config.c(311,6): error C2371: 'FT601_UpdateAdaptiveParameters': redefinition; different basic types
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\ft601_performance_config.c(320,37): error C2065: 'FT601_OPT_QUEUE_DEPTH_MAX': undeclared identifier
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\ft601_performance_config.c(320,35): warning C4018: '<': signed/unsigned mismatch
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\ft601_performance_config.c(321,37): error C2065: 'FT601_OPT_QUEUE_DEPTH_MAX': undeclared identifier
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\ft601_performance_config.c(321,37): warning C4018: '<': signed/unsigned mismatch
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\ft601_performance_config.c(327,36): error C2065: 'FT601_OPT_BURST_SIZE_MAX': undeclared identifier
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\ft601_performance_config.c(327,34): warning C4018: '<': signed/unsigned mismatch
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\ft601_performance_config.c(328,36): error C2065: 'FT601_OPT_BURST_SIZE_MAX': undeclared identifier
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\ft601_performance_config.c(328,36): warning C4018: '<': signed/unsigned mismatch
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\ft601_performance_config.c(335,9): error C2059: syntax error: 'if'
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\ft601_performance_config.c(339,5): error C2059: syntax error: '}'
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\ft601_performance_config.c(414,52): error C2039: 'dwBufferSize': is not a member of 'tdFT601_PERFORMANCE_CONFIG'
      G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\ft601_performance_config.h(45,16):
      see declaration of 'tdFT601_PERFORMANCE_CONFIG'
  
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\ft601_performance_config.c(414,24): warning C4473: 'fprintf' : not enough arguments passed for format string
      G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\ft601_performance_config.c(414,24):
      placeholders and their parameters expect 1 variadic arguments, but 0 were provided
      G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\ft601_performance_config.c(414,24):
      the missing variadic argument 1 is required by format string '%u'
  
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\ft601_performance_config.c(439,9): error C2065: 'dwQueueDepth': undeclared identifier
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\ft601_performance_config.c(442,9): error C2065: 'dwQueueDepth': undeclared identifier
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\ft601_performance_config.c(447,45): error C2065: 'dwQueueDepth': undeclared identifier
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\ft601_performance_config.c(462,18): error C2039: 'dwBufferSize': is not a member of 'tdFT601_PERFORMANCE_CONFIG'
      G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\ft601_performance_config.h(45,16):
      see declaration of 'tdFT601_PERFORMANCE_CONFIG'
  
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\ft601_performance_config.c(466,18): error C2039: 'dwBufferSize': is not a member of 'tdFT601_PERFORMANCE_CONFIG'
      G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\ft601_performance_config.h(45,16):
      see declaration of 'tdFT601_PERFORMANCE_CONFIG'
  
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\ft601_performance_config.c(470,18): error C2039: 'dwBufferSize': is not a member of 'tdFT601_PERFORMANCE_CONFIG'
      G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\ft601_performance_config.h(45,16):
      see declaration of 'tdFT601_PERFORMANCE_CONFIG'
  
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\ft601_performance_config.c(488,18): error C2039: 'dwBufferSize': is not a member of 'tdFT601_PERFORMANCE_CONFIG'
      G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\ft601_performance_config.h(45,16):
      see declaration of 'tdFT601_PERFORMANCE_CONFIG'
  
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\ft601_performance_config.c(645,54): error C2039: 'dwBufferSize': is not a member of 'tdFT601_PERFORMANCE_CONFIG'
      G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\ft601_performance_config.h(45,16):
      see declaration of 'tdFT601_PERFORMANCE_CONFIG'
  
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\ft601_performance_config.c(645,12): warning C4473: 'printf' : not enough arguments passed for format string
      G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\ft601_performance_config.c(645,12):
      placeholders and their parameters expect 1 variadic arguments, but 0 were provided
      G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\ft601_performance_config.c(645,12):
      the missing variadic argument 1 is required by format string '%u'
  
  Generating Code...
