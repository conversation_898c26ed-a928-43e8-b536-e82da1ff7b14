﻿  charutil.c
  device.c
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\device.c(1,1): warning C4819: The file contains a character that cannot be represented in the current code page (936). Save the file in Unicode format to prevent data loss
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\ft601_performance_config.h(1,1): warning C4819: The file contains a character that cannot be represented in the current code page (936). Save the file in Unicode format to prevent data loss
  (compiling source file 'device.c')
  
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\ft601_performance_config.h(89,31): error C2065: 'FT601_OPT_PERF_HISTORY_SIZE': undeclared identifier
  (compiling source file 'device.c')
  
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\ft601_performance_config.h(89,31): error C2057: expected constant expression
  (compiling source file 'device.c')
  
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\ft601_performance_config.h(90,11): error C2229: struct 'tdFT601_PERFORMANCE_STATS' has an illegal zero-sized array
  (compiling source file 'device.c')
  
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\ft601_performance_config.h(215,19): error C2146: syntax error: missing ')' before identifier 'pdThroughputMBps'
  (compiling source file 'device.c')
  
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\ft601_performance_config.h(215,11): error C2081: 'PDOUBLE': name in formal parameter list illegal
  (compiling source file 'device.c')
  
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\ft601_performance_config.h(215,19): error C2061: syntax error: identifier 'pdThroughputMBps'
  (compiling source file 'device.c')
  
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\ft601_performance_config.h(215,19): error C2059: syntax error: ';'
  (compiling source file 'device.c')
  
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\ft601_performance_config.h(216,1): error C2059: syntax error: ')'
  (compiling source file 'device.c')
  
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\ft601_performance_config.h(220,10): error C2371: 'PFT601_PERFORMANCE_CONFIG': redefinition; different basic types
  (compiling source file 'device.c')
      G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\ft601_performance_config.h(66,30):
      see declaration of 'PFT601_PERFORMANCE_CONFIG'
  
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\ft601_performance_config.h(220,36): error C2146: syntax error: missing ';' before identifier 'pConfig'
  (compiling source file 'device.c')
  
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\ft601_performance_config.h(221,1): error C2059: syntax error: ')'
  (compiling source file 'device.c')
  
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\device.c(218,5): warning C4013: 'FT601_InitDefaultConfig' undefined; assuming extern returning int
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\device.c(231,22): error C2039: 'dwBufferSize': is not a member of 'tdFT601_PERFORMANCE_CONFIG'
      G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\ft601_performance_config.h(41,16):
      see declaration of 'tdFT601_PERFORMANCE_CONFIG'
  
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\device.c(247,5): error C2374: 'g_fPerfConfigInitialized': redefinition; multiple initialization
      G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\device.c(15,13):
      see declaration of 'g_fPerfConfigInitialized'
  
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\device.c(248,5): error C2059: syntax error: 'return'
G:\FPGA\20250327\100t20250618\100t484-1\pcileech-master\pcileech\device.c(249,1): error C2059: syntax error: '}'
  executor.c
  extra.c
  help.c
  kmd.c
  memdump.c
  mempatch.c
  ob_cachemap.c
  ob_core.c
  ob_map.c
  ob_set.c
  oscompatibility.c
  pcileech.c
  statistics.c
  umd.c
  util.c
  vfs.c
  vfslist.c
  vmmx.c
  Generating Code...
