CC=clang
CFLAGS += -I. -I../includes -D MACOS -D _GNU_SOURCE -D _FILE_OFFSET_BITS=64 -pthread
CFLAGS += -fPIE -fPIC -fstack-protector -D_FORTIFY_SOURCE=2 -O1
CFLAGS += -Wall -Wno-enum-compare -Wno-pointer-sign -Wno-multichar -Wno-unused-variable -Wno-unused-value
CFLAGS += -Wno-pointer-to-int-cast -Wno-int-to-pointer-cast
CFLAGS += -mmacosx-version-min=11.0
# DEBUG FLAGS BELOW
#CFLAGS += -O0
#CFLAGS += -fsanitize=address
# DEBUG FLAGS ABOVE
LDFLAGS += -ldl -L. ./leechcore.dylib ./vmm.dylib
LDFLAGS += -Wl,-rpath,@loader_path
LDFLAGS += -mmacosx-version-min=11.0

DEPS = pcileech.h
OBJ  = oscompatibility.o charutil.o device.o pcileech.o executor.o extra.o help.o kmd.o memdump.o mempatch.o statistics.o umd.o util.o vfslist.o vfs.o vmmx.o ob/ob_cachemap.o ob/ob_core.o ob/ob_map.o ob/ob_set.o

# ARCH SPECIFIC FLAGS:
CFLAGS_X86_64  = $(CFLAGS) -arch x86_64
CFLAGS_ARM64   = $(CFLAGS) -arch arm64
LDFLAGS_X86_64 = $(LDFLAGS) -arch x86_64
LDFLAGS_ARM64  = $(LDFLAGS) -arch arm64
OBJ_X86_64 = $(OBJ:.o=.o.x86_64)
OBJ_ARM64  = $(OBJ:.o=.o.arm64)

all: pcileech

%.o.x86_64: %.c $(DEPS)
	$(CC) $(CFLAGS_X86_64) -c -o $@ $<

%.o.arm64: %.c $(DEPS)
	$(CC) $(CFLAGS_ARM64) -c -o $@ $<

pcileech_x86_64: $(OBJ_X86_64)
	cp ../files/vmm.dylib . || cp ../../MemProcFS*/files/vmm.dylib . || true
	cp ../files/leechcore.dylib . || cp ../../LeechCore*/files/leechcore.dylib . || true
	$(CC) $(LDFLAGS_X86_64) -o $@ $^

pcileech_arm64: $(OBJ_ARM64)
	cp ../files/vmm.dylib . || cp ../../MemProcFS*/files/vmm.dylib . || true
	cp ../files/leechcore.dylib . || cp ../../LeechCore*/files/leechcore.dylib . || true
	$(CC) $(LDFLAGS_ARM64) -o $@ $^

pcileech: pcileech_x86_64 pcileech_arm64
	lipo -create -output pcileech pcileech_x86_64 pcileech_arm64
	install_name_tool -id @rpath/pcileech pcileech
	mv pcileech ../files/ |true
	mv vmm.dylib ../files/ |true
	mv leechcore.dylib ../files/ |true
	rm -f *.o *.o.x86_64 *.o.arm64 || true
	rm -f */*.o */*.o.x86_64 */*.o.arm64 || true
	rm -f *.dylib || true
	true

clean:
	rm -f *.o *.o.x86_64 *.o.arm64 || true
	rm -f */*.o */*.o.x86_64 */*.o.arm64 || true
	rm -f *.dylib || true
