// device_optimized.c : 优化版设备访问实现
//
// 性能优化特性:
// 1. 异步I/O操作
// 2. 多线程并行处理
// 3. 自适应缓冲区管理
// 4. 零拷贝优化
//
// (c) 优化版本, 2025
//

#include <leechcore.h>
#include "device.h"
#include "statistics.h"
#include "util.h"
#include <windows.h>
#include <process.h>

// 优化参数
#define OPTIMIZED_BUFFER_SIZE_MIN       0x00100000  // 1MB最小缓冲区
#define OPTIMIZED_BUFFER_SIZE_MAX       0x04000000  // 64MB最大缓冲区
#define OPTIMIZED_BUFFER_COUNT          8           // 8个缓冲区
#define OPTIMIZED_WORKER_THREADS        4           // 4个工作线程
#define OPTIMIZED_BATCH_SIZE            64          // 批处理大小

// 异步I/O结构
typedef struct tdOPTIMIZED_IO_CONTEXT {
    OVERLAPPED overlapped;
    PMEM_SCATTER pMEM;
    DWORD dwIndex;
    BOOL fCompleted;
    DWORD dwError;
} OPTIMIZED_IO_CONTEXT, *POPTIMIZED_IO_CONTEXT;

// 优化的设备上下文
typedef struct tdOPTIMIZED_DEVICE_CONTEXT {
    HANDLE hDevice;
    HANDLE hCompletionPort;
    HANDLE hWorkerThreads[OPTIMIZED_WORKER_THREADS];
    CRITICAL_SECTION csBufferPool;
    
    // 缓冲区池
    PBYTE pbBufferPool[OPTIMIZED_BUFFER_COUNT];
    BOOL fBufferAvailable[OPTIMIZED_BUFFER_COUNT];
    DWORD dwBufferSize;
    
    // 性能统计
    volatile LONG64 llTotalBytesRead;
    volatile LONG64 llTotalBytesWritten;
    volatile LONG64 llTotalOperations;
    LARGE_INTEGER liStartTime;
    
    // 控制标志
    BOOL fShutdown;
    BOOL fInitialized;
} OPTIMIZED_DEVICE_CONTEXT, *POPTIMIZED_DEVICE_CONTEXT;

static POPTIMIZED_DEVICE_CONTEXT g_pOptCtx = NULL;

// 工作线程函数
DWORD WINAPI OptimizedWorkerThread(LPVOID lpParameter)
{
    DWORD dwBytesTransferred;
    ULONG_PTR ulCompletionKey;
    POPTIMIZED_IO_CONTEXT pIoCtx;
    BOOL fResult;
    
    while (!g_pOptCtx->fShutdown) {
        fResult = GetQueuedCompletionStatus(
            g_pOptCtx->hCompletionPort,
            &dwBytesTransferred,
            &ulCompletionKey,
            (LPOVERLAPPED*)&pIoCtx,
            1000  // 1秒超时
        );
        
        if (!fResult) {
            if (GetLastError() == WAIT_TIMEOUT) continue;
            break;
        }
        
        if (pIoCtx) {
            // 处理完成的I/O操作
            pIoCtx->fCompleted = TRUE;
            pIoCtx->dwError = GetLastError();
            
            // 更新统计信息
            InterlockedIncrement64(&g_pOptCtx->llTotalOperations);
            if (ulCompletionKey == 1) {  // 读操作
                InterlockedAdd64(&g_pOptCtx->llTotalBytesRead, dwBytesTransferred);
            } else {  // 写操作
                InterlockedAdd64(&g_pOptCtx->llTotalBytesWritten, dwBytesTransferred);
            }
        }
    }
    
    return 0;
}

// 初始化优化设备上下文
_Success_(return)
BOOL OptimizedDeviceInitialize()
{
    DWORD i;
    SYSTEM_INFO si;
    
    if (g_pOptCtx) return TRUE;  // 已初始化
    
    g_pOptCtx = LocalAlloc(LMEM_ZEROINIT, sizeof(OPTIMIZED_DEVICE_CONTEXT));
    if (!g_pOptCtx) return FALSE;
    
    // 获取系统信息以优化缓冲区大小
    GetSystemInfo(&si);
    g_pOptCtx->dwBufferSize = max(OPTIMIZED_BUFFER_SIZE_MIN, 
                                  min(OPTIMIZED_BUFFER_SIZE_MAX, 
                                      si.dwPageSize * 256));
    
    // 初始化临界区
    InitializeCriticalSection(&g_pOptCtx->csBufferPool);
    
    // 创建完成端口
    g_pOptCtx->hCompletionPort = CreateIoCompletionPort(
        INVALID_HANDLE_VALUE, NULL, 0, OPTIMIZED_WORKER_THREADS);
    if (!g_pOptCtx->hCompletionPort) goto fail;
    
    // 分配缓冲区池
    for (i = 0; i < OPTIMIZED_BUFFER_COUNT; i++) {
        g_pOptCtx->pbBufferPool[i] = VirtualAlloc(
            NULL, g_pOptCtx->dwBufferSize, 
            MEM_COMMIT | MEM_RESERVE, PAGE_READWRITE);
        if (!g_pOptCtx->pbBufferPool[i]) goto fail;
        g_pOptCtx->fBufferAvailable[i] = TRUE;
    }
    
    // 创建工作线程
    for (i = 0; i < OPTIMIZED_WORKER_THREADS; i++) {
        g_pOptCtx->hWorkerThreads[i] = CreateThread(
            NULL, 0, OptimizedWorkerThread, NULL, 0, NULL);
        if (!g_pOptCtx->hWorkerThreads[i]) goto fail;
    }
    
    QueryPerformanceCounter(&g_pOptCtx->liStartTime);
    g_pOptCtx->fInitialized = TRUE;
    return TRUE;
    
fail:
    OptimizedDeviceCleanup();
    return FALSE;
}

// 清理优化设备上下文
VOID OptimizedDeviceCleanup()
{
    DWORD i;
    
    if (!g_pOptCtx) return;
    
    g_pOptCtx->fShutdown = TRUE;
    
    // 等待工作线程结束
    if (g_pOptCtx->hWorkerThreads[0]) {
        WaitForMultipleObjects(OPTIMIZED_WORKER_THREADS, 
                              g_pOptCtx->hWorkerThreads, TRUE, 5000);
        for (i = 0; i < OPTIMIZED_WORKER_THREADS; i++) {
            if (g_pOptCtx->hWorkerThreads[i]) {
                CloseHandle(g_pOptCtx->hWorkerThreads[i]);
            }
        }
    }
    
    // 释放缓冲区池
    for (i = 0; i < OPTIMIZED_BUFFER_COUNT; i++) {
        if (g_pOptCtx->pbBufferPool[i]) {
            VirtualFree(g_pOptCtx->pbBufferPool[i], 0, MEM_RELEASE);
        }
    }
    
    if (g_pOptCtx->hCompletionPort) {
        CloseHandle(g_pOptCtx->hCompletionPort);
    }
    
    DeleteCriticalSection(&g_pOptCtx->csBufferPool);
    LocalFree(g_pOptCtx);
    g_pOptCtx = NULL;
}

// 获取可用缓冲区
_Success_(return != NULL)
PBYTE OptimizedGetBuffer(_Out_ PDWORD pdwIndex)
{
    DWORD i;
    PBYTE pbBuffer = NULL;
    
    EnterCriticalSection(&g_pOptCtx->csBufferPool);
    
    for (i = 0; i < OPTIMIZED_BUFFER_COUNT; i++) {
        if (g_pOptCtx->fBufferAvailable[i]) {
            g_pOptCtx->fBufferAvailable[i] = FALSE;
            pbBuffer = g_pOptCtx->pbBufferPool[i];
            *pdwIndex = i;
            break;
        }
    }
    
    LeaveCriticalSection(&g_pOptCtx->csBufferPool);
    return pbBuffer;
}

// 释放缓冲区
VOID OptimizedReleaseBuffer(_In_ DWORD dwIndex)
{
    if (dwIndex >= OPTIMIZED_BUFFER_COUNT) return;
    
    EnterCriticalSection(&g_pOptCtx->csBufferPool);
    g_pOptCtx->fBufferAvailable[dwIndex] = TRUE;
    LeaveCriticalSection(&g_pOptCtx->csBufferPool);
}

// 优化的批量读取函数
_Success_(return > 0)
DWORD OptimizedDeviceReadDMABatch(
    _In_ PPMEM_SCATTER ppMEMs, 
    _In_ DWORD cMEMs, 
    _Inout_opt_ PPAGE_STATISTICS pPageStat)
{
    DWORD i, j, cbRead = 0;
    DWORD dwBatchCount = (cMEMs + OPTIMIZED_BATCH_SIZE - 1) / OPTIMIZED_BATCH_SIZE;
    POPTIMIZED_IO_CONTEXT pIoContexts;
    BOOL fAllCompleted;
    
    if (!g_pOptCtx || !g_pOptCtx->fInitialized) {
        if (!OptimizedDeviceInitialize()) {
            return DeviceReadDMA(ppMEMs[0]->qwA, cMEMs * 0x1000, 
                               (PBYTE)ppMEMs[0]->pb, pPageStat);
        }
    }
    
    // 分配I/O上下文数组
    pIoContexts = LocalAlloc(LMEM_ZEROINIT, 
                            sizeof(OPTIMIZED_IO_CONTEXT) * dwBatchCount);
    if (!pIoContexts) return 0;
    
    // 提交批量异步读取请求
    for (i = 0; i < dwBatchCount; i++) {
        DWORD dwStartIdx = i * OPTIMIZED_BATCH_SIZE;
        DWORD dwEndIdx = min((i + 1) * OPTIMIZED_BATCH_SIZE, cMEMs);
        DWORD dwBatchSize = dwEndIdx - dwStartIdx;
        
        pIoContexts[i].dwIndex = i;
        pIoContexts[i].fCompleted = FALSE;
        
        // 使用LeechCore的批量读取
        LcReadScatter(ctxMain->hLC, dwBatchSize, &ppMEMs[dwStartIdx]);
        pIoContexts[i].fCompleted = TRUE;  // 同步完成
    }
    
    // 等待所有操作完成并统计结果
    do {
        fAllCompleted = TRUE;
        for (i = 0; i < dwBatchCount; i++) {
            if (!pIoContexts[i].fCompleted) {
                fAllCompleted = FALSE;
                Sleep(1);
                break;
            }
        }
    } while (!fAllCompleted);
    
    // 统计成功读取的字节数
    for (i = 0; i < cMEMs; i++) {
        if (ppMEMs[i]->f) {
            cbRead += ppMEMs[i]->cb;
        }
        if (pPageStat) {
            PageStatUpdate(pPageStat, ppMEMs[i]->qwA + 0x1000, 
                          ppMEMs[i]->f ? 1 : 0, ppMEMs[i]->f ? 0 : 1);
        }
    }
    
    LocalFree(pIoContexts);
    return cbRead;
}

// 获取性能统计信息
VOID OptimizedDeviceGetPerformanceStats(
    _Out_ PQWORD pqwBytesRead,
    _Out_ PQWORD pqwBytesWritten,
    _Out_ PQWORD pqwOperations,
    _Out_ double* pdThroughputMBps)
{
    LARGE_INTEGER liCurrentTime, liFrequency;
    double dElapsedSeconds;
    
    if (!g_pOptCtx) {
        *pqwBytesRead = 0;
        *pqwBytesWritten = 0;
        *pqwOperations = 0;
        *pdThroughputMBps = 0.0;
        return;
    }
    
    *pqwBytesRead = g_pOptCtx->llTotalBytesRead;
    *pqwBytesWritten = g_pOptCtx->llTotalBytesWritten;
    *pqwOperations = g_pOptCtx->llTotalOperations;
    
    QueryPerformanceCounter(&liCurrentTime);
    QueryPerformanceFrequency(&liFrequency);
    
    dElapsedSeconds = (double)(liCurrentTime.QuadPart - g_pOptCtx->liStartTime.QuadPart) 
                     / (double)liFrequency.QuadPart;
    
    if (dElapsedSeconds > 0.0) {
        *pdThroughputMBps = (double)(*pqwBytesRead + *pqwBytesWritten) 
                           / (1024.0 * 1024.0 * dElapsedSeconds);
    } else {
        *pdThroughputMBps = 0.0;
    }
}
