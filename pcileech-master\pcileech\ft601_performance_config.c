// ft601_performance_config.c : FT601性能优化配置实现
//
// 实现FT601通信层的性能优化配置和监控功能
//
// (c) 优化版本, 2025
//

#include "ft601_performance_config.h"
#include "pcileech.h"
#include <stdio.h>
#include <stdlib.h>

// 全局性能统计
static FT601_PERFORMANCE_STATS g_perfStats;
static BOOL g_fPerfMonitoringActive = FALSE;
static HANDLE g_hPerfMonitorThread = NULL;
static BOOL g_fShutdownPerfMonitor = FALSE;

// 初始化默认配置
VOID FT601_InitDefaultConfig(_Out_ PFT601_PERFORMANCE_CONFIG pConfig)
{
    if (!pConfig) return;

    ZeroMemory(pConfig, sizeof(FT601_PERFORMANCE_CONFIG));

    // FPGA配置
    pConfig->dwQueueDepth = 16;
    pConfig->dwBurstSize = 8;
    pConfig->dwCooldownCycles = 4;
    pConfig->fDualBufferEnabled = TRUE;
    pConfig->fPipelineEnabled = TRUE;

    // 上位机配置
    pConfig->dwBufferSize = 0x02000000;     // 32MB
    pConfig->dwBufferCount = 8;
    pConfig->dwWorkerThreads = 4;
    pConfig->dwBatchSize = 64;
    pConfig->fAsyncIOEnabled = TRUE;
    pConfig->fCompressionEnabled = FALSE;

    // 自适应配置
    pConfig->fAdaptiveMode = TRUE;
    pConfig->dwAdaptiveInterval = 5000;     // 5秒
    pConfig->dTargetThroughput = 200.0;     // 200MB/s
    pConfig->dMinThroughput = 50.0;         // 50MB/s

    // 调试配置
    pConfig->fPerformanceLogging = FALSE;
    pConfig->fDetailedStats = FALSE;
    strcpy_s(pConfig->szLogFile, sizeof(pConfig->szLogFile), "ft601_performance.log");
}

// 高性能配置
VOID FT601_ConfigHighPerformance(_Inout_ PFT601_PERFORMANCE_CONFIG pConfig)
{
    if (!pConfig) return;

    pConfig->dwQueueDepth = 32;
    pConfig->dwBurstSize = 16;
    pConfig->dwCooldownCycles = 2;
    pConfig->dwBufferSize = 0x04000000;
    pConfig->dwBufferCount = 16;
    pConfig->dwWorkerThreads = 8;
    pConfig->fAsyncIOEnabled = TRUE;
    pConfig->fAdaptiveMode = TRUE;
}

// 低延迟配置
VOID FT601_ConfigLowLatency(_Inout_ PFT601_PERFORMANCE_CONFIG pConfig)
{
    if (!pConfig) return;

    pConfig->dwQueueDepth = 8;
    pConfig->dwBurstSize = 4;
    pConfig->dwCooldownCycles = 2;
    pConfig->dwBufferSize = 0x00400000;
    pConfig->dwBufferCount = 4;
    pConfig->dwWorkerThreads = 2;
    pConfig->fAsyncIOEnabled = TRUE;
}

// 平衡配置
VOID FT601_ConfigBalanced(_Inout_ PFT601_PERFORMANCE_CONFIG pConfig)
{
    if (!pConfig) return;

    FT601_InitDefaultConfig(pConfig);
}

// 性能监控线程
DWORD WINAPI FT601_PerformanceMonitorThread(LPVOID lpParameter)
{
    PFT601_PERFORMANCE_CONFIG pConfig = (PFT601_PERFORMANCE_CONFIG)lpParameter;
    LARGE_INTEGER liFrequency, liLastTime, liCurrentTime;
    QWORD qwLastBytesRead = 0, qwLastBytesWritten = 0;
    
    QueryPerformanceFrequency(&liFrequency);
    QueryPerformanceCounter(&liLastTime);
    
    while (!g_fShutdownPerfMonitor) {
        Sleep(pConfig->dwAdaptiveInterval);
        
        QueryPerformanceCounter(&liCurrentTime);
        
        // 计算时间差
        double dElapsedSeconds = (double)(liCurrentTime.QuadPart - liLastTime.QuadPart) 
                               / (double)liFrequency.QuadPart;
        
        if (dElapsedSeconds > 0.0) {
            // 计算当前吞吐量
            QWORD qwCurrentBytesRead = g_perfStats.qwTotalBytesRead;
            QWORD qwCurrentBytesWritten = g_perfStats.qwTotalBytesWritten;
            
            QWORD qwDeltaBytes = (qwCurrentBytesRead - qwLastBytesRead) + 
                               (qwCurrentBytesWritten - qwLastBytesWritten);
            
            g_perfStats.dCurrentThroughputMBps = (double)qwDeltaBytes / 
                                               (1024.0 * 1024.0 * dElapsedSeconds);
            
            // 更新历史数据
            g_perfStats.dThroughputHistory[g_perfStats.dwHistoryIndex] = 
                g_perfStats.dCurrentThroughputMBps;
            g_perfStats.dwHistoryIndex = (g_perfStats.dwHistoryIndex + 1) % 
                                       FT601_OPT_PERF_HISTORY_SIZE;
            if (!g_perfStats.fHistoryFull && 
                g_perfStats.dwHistoryIndex == 0) {
                g_perfStats.fHistoryFull = TRUE;
            }
            
            // 更新峰值
            if (g_perfStats.dCurrentThroughputMBps > g_perfStats.dPeakThroughputMBps) {
                g_perfStats.dPeakThroughputMBps = g_perfStats.dCurrentThroughputMBps;
            }
            
            // 计算平均值
            if (g_perfStats.fHistoryFull) {
                double dSum = 0.0;
                for (DWORD i = 0; i < FT601_OPT_PERF_HISTORY_SIZE; i++) {
                    dSum += g_perfStats.dThroughputHistory[i];
                }
                g_perfStats.dAverageThroughputMBps = dSum / FT601_OPT_PERF_HISTORY_SIZE;
            }
            
            // 自适应优化
            if (pConfig->fAdaptiveMode) {
                FT601_UpdateAdaptiveParameters(ctxMain->hLC, &g_perfStats, pConfig);
            }
            
            // 性能日志
            if (pConfig->fPerformanceLogging) {
                FT601_LogPerformanceData(&g_perfStats, pConfig->szLogFile);
            }
            
            qwLastBytesRead = qwCurrentBytesRead;
            qwLastBytesWritten = qwCurrentBytesWritten;
            liLastTime = liCurrentTime;
        }
    }
    
    return 0;
}

// 加载性能配置
_Success_(return)
BOOL FT601_LoadPerformanceConfig(
    _Out_ PFT601_PERFORMANCE_CONFIG pConfig,
    _In_opt_ LPCSTR szConfigFile)
{
    if (!pConfig) return FALSE;
    
    // 设置默认配置
    FT601_InitDefaultConfig(pConfig);
    
    // 如果指定了配置文件，尝试加载
    if (szConfigFile && strlen(szConfigFile) > 0) {
        FILE *hFile;
        if (0 == fopen_s(&hFile, szConfigFile, "r")) {
            // 简化的配置文件解析
            char szLine[256];
            while (fgets(szLine, sizeof(szLine), hFile)) {
                if (strstr(szLine, "QueueDepth=")) {
                    sscanf_s(szLine, "QueueDepth=%u", &pConfig->dwQueueDepth);
                } else if (strstr(szLine, "BurstSize=")) {
                    sscanf_s(szLine, "BurstSize=%u", &pConfig->dwBurstSize);
                } else if (strstr(szLine, "BufferSize=")) {
                    sscanf_s(szLine, "BufferSize=%u", &pConfig->dwBufferSize);
                } else if (strstr(szLine, "WorkerThreads=")) {
                    sscanf_s(szLine, "WorkerThreads=%u", &pConfig->dwWorkerThreads);
                } else if (strstr(szLine, "AdaptiveMode=")) {
                    int fAdaptive;
                    sscanf_s(szLine, "AdaptiveMode=%d", &fAdaptive);
                    pConfig->fAdaptiveMode = fAdaptive ? TRUE : FALSE;
                }
            }
            fclose(hFile);
        }
    }
    
    return TRUE;
}

// 应用性能配置到设备
_Success_(return)
BOOL FT601_ApplyPerformanceConfig(
    _In_ HANDLE hLC,
    _In_ PFT601_PERFORMANCE_CONFIG pConfig)
{
    if (!hLC || !pConfig) return FALSE;
    
    BOOL fResult = TRUE;
    
    // 应用FPGA参数
    if (!FT601_SetFPGAQueueDepth(hLC, pConfig->dwQueueDepth)) {
        fResult = FALSE;
    }
    
    if (!FT601_SetFPGABurstSize(hLC, pConfig->dwBurstSize)) {
        fResult = FALSE;
    }
    
    if (!FT601_SetFPGACooldownCycles(hLC, pConfig->dwCooldownCycles)) {
        fResult = FALSE;
    }
    
    // 设置缓冲区大小
    if (!LcSetOption(hLC, LC_OPT_FPGA_MAX_SIZE_RX, pConfig->dwBufferSize)) {
        fResult = FALSE;
    }
    
    if (!LcSetOption(hLC, LC_OPT_FPGA_MAX_SIZE_TX, pConfig->dwBufferSize)) {
        fResult = FALSE;
    }
    
    return fResult;
}

// 初始化性能监控
_Success_(return)
BOOL FT601_InitializePerformanceMonitoring(
    _In_ PFT601_PERFORMANCE_CONFIG pConfig)
{
    if (g_fPerfMonitoringActive) return TRUE;
    
    // 初始化统计结构
    ZeroMemory(&g_perfStats, sizeof(g_perfStats));
    QueryPerformanceCounter(&g_perfStats.liStartTime);
    g_perfStats.liLastUpdateTime = g_perfStats.liStartTime;
    
    // 创建监控线程
    g_fShutdownPerfMonitor = FALSE;
    g_hPerfMonitorThread = CreateThread(
        NULL, 0, FT601_PerformanceMonitorThread, pConfig, 0, NULL);
    
    if (g_hPerfMonitorThread) {
        g_fPerfMonitoringActive = TRUE;
        return TRUE;
    }
    
    return FALSE;
}

// 更新性能统计
VOID FT601_UpdatePerformanceStats(
    _Inout_ PFT601_PERFORMANCE_STATS pStats,
    _In_ QWORD qwBytesTransferred,
    _In_ DWORD dwLatencyUs)
{
    if (!pStats) return;
    
    InterlockedAdd64(&pStats->qwTotalBytesRead, qwBytesTransferred);
    InterlockedIncrement64(&pStats->qwTotalOperations);
    
    // 更新延迟统计
    pStats->dwCurrentLatencyUs = dwLatencyUs;
    if (pStats->qwTotalOperations > 0) {
        pStats->dwAverageLatencyUs = (DWORD)(
            (pStats->dwAverageLatencyUs * (pStats->qwTotalOperations - 1) + dwLatencyUs) /
            pStats->qwTotalOperations);
    }
    
    QueryPerformanceCounter(&pStats->liLastUpdateTime);
}

// 设置FPGA队列深度
_Success_(return)
BOOL FT601_SetFPGAQueueDepth(
    _In_ HANDLE hLC,
    _In_ DWORD dwQueueDepth)
{
    if (!hLC) return FALSE;
    
    // 通过自定义命令设置队列深度
    // 这需要FPGA固件支持相应的配置寄存器
    DWORD dwRegAddr = 0x8060;  // 假设的队列深度配置寄存器
    return LcCommand(hLC, LC_CMD_FPGA_CFGREGPCIE | dwRegAddr, 
                    sizeof(DWORD), (PBYTE)&dwQueueDepth, NULL, NULL);
}

// 设置FPGA突发大小
_Success_(return)
BOOL FT601_SetFPGABurstSize(
    _In_ HANDLE hLC,
    _In_ DWORD dwBurstSize)
{
    if (!hLC) return FALSE;
    
    DWORD dwRegAddr = 0x8064;  // 假设的突发大小配置寄存器
    return LcCommand(hLC, LC_CMD_FPGA_CFGREGPCIE | dwRegAddr, 
                    sizeof(DWORD), (PBYTE)&dwBurstSize, NULL, NULL);
}

// 设置FPGA冷却周期
_Success_(return)
BOOL FT601_SetFPGACooldownCycles(
    _In_ HANDLE hLC,
    _In_ DWORD dwCooldownCycles)
{
    if (!hLC) return FALSE;
    
    DWORD dwRegAddr = 0x8068;  // 假设的冷却周期配置寄存器
    return LcCommand(hLC, LC_CMD_FPGA_CFGREGPCIE | dwRegAddr, 
                    sizeof(DWORD), (PBYTE)&dwCooldownCycles, NULL, NULL);
}

// 自适应参数更新
VOID FT601_UpdateAdaptiveParameters(
    _In_ HANDLE hLC,
    _In_ PFT601_PERFORMANCE_STATS pStats,
    _Inout_ PFT601_PERFORMANCE_CONFIG pConfig)
{
    if (!hLC || !pStats || !pConfig) return;
    
    // 如果性能低于目标，尝试优化参数
    if (pStats->dCurrentThroughputMBps < pConfig->dTargetThroughput) {
        // 增加队列深度
        if (pConfig->dwQueueDepth < FT601_OPT_QUEUE_DEPTH_MAX) {
            pConfig->dwQueueDepth = min(pConfig->dwQueueDepth + 2, 
                                      FT601_OPT_QUEUE_DEPTH_MAX);
            FT601_SetFPGAQueueDepth(hLC, pConfig->dwQueueDepth);
        }
        
        // 增加突发大小
        if (pConfig->dwBurstSize < FT601_OPT_BURST_SIZE_MAX) {
            pConfig->dwBurstSize = min(pConfig->dwBurstSize + 1, 
                                     FT601_OPT_BURST_SIZE_MAX);
            FT601_SetFPGABurstSize(hLC, pConfig->dwBurstSize);
        }
    }
    
    // 如果错误率过高，降低激进程度
    if (FT601_HAS_HIGH_ERROR_RATE(pStats)) {
        if (pConfig->dwCooldownCycles < FT601_OPT_COOLDOWN_CYCLES_MAX) {
            pConfig->dwCooldownCycles++;
            FT601_SetFPGACooldownCycles(hLC, pConfig->dwCooldownCycles);
        }
    }
}

// 获取性能统计
VOID FT601_GetPerformanceStats(
    _Out_ PFT601_PERFORMANCE_STATS pStats)
{
    if (pStats) {
        *pStats = g_perfStats;
    }
}

// 清理性能监控
VOID FT601_CleanupPerformanceMonitoring(VOID)
{
    if (g_fPerfMonitoringActive) {
        g_fShutdownPerfMonitor = TRUE;
        
        if (g_hPerfMonitorThread) {
            WaitForSingleObject(g_hPerfMonitorThread, 5000);
            CloseHandle(g_hPerfMonitorThread);
            g_hPerfMonitorThread = NULL;
        }
        
        g_fPerfMonitoringActive = FALSE;
    }
}

// 性能日志记录
_Success_(return)
BOOL FT601_LogPerformanceData(
    _In_ PFT601_PERFORMANCE_STATS pStats,
    _In_opt_ LPCSTR szLogFile)
{
    if (!pStats) return FALSE;
    
    FILE *hFile;
    LPCSTR szFileName = szLogFile ? szLogFile : "ft601_performance.log";
    
    if (0 == fopen_s(&hFile, szFileName, "a")) {
        SYSTEMTIME st;
        GetLocalTime(&st);
        
        fprintf(hFile, "%04d-%02d-%02d %02d:%02d:%02d,%.2f,%.2f,%.2f,%u,%u,%u\n",
                st.wYear, st.wMonth, st.wDay, st.wHour, st.wMinute, st.wSecond,
                pStats->dCurrentThroughputMBps,
                pStats->dAverageThroughputMBps,
                pStats->dPeakThroughputMBps,
                pStats->dwCurrentLatencyUs,
                pStats->dwErrorCount,
                (DWORD)pStats->qwTotalOperations);
        
        fclose(hFile);
        return TRUE;
    }
    
    return FALSE;
}

// 保存性能配置
_Success_(return)
BOOL FT601_SavePerformanceConfig(
    _In_ PFT601_PERFORMANCE_CONFIG pConfig,
    _In_opt_ LPCSTR szConfigFile)
{
    if (!pConfig) return FALSE;

    FILE *hFile;
    LPCSTR szFileName = szConfigFile ? szConfigFile : "ft601_config.ini";

    if (0 == fopen_s(&hFile, szFileName, "w")) {
        fprintf(hFile, "# FT601 Performance Configuration\n");
        fprintf(hFile, "QueueDepth=%u\n", pConfig->dwQueueDepth);
        fprintf(hFile, "BurstSize=%u\n", pConfig->dwBurstSize);
        fprintf(hFile, "CooldownCycles=%u\n", pConfig->dwCooldownCycles);
        fprintf(hFile, "BufferSize=%u\n", pConfig->dwBufferSize);
        fprintf(hFile, "BufferCount=%u\n", pConfig->dwBufferCount);
        fprintf(hFile, "WorkerThreads=%u\n", pConfig->dwWorkerThreads);
        fprintf(hFile, "AdaptiveMode=%d\n", pConfig->fAdaptiveMode ? 1 : 0);
        fprintf(hFile, "TargetThroughput=%.2f\n", pConfig->dTargetThroughput);

        fclose(hFile);
        return TRUE;
    }

    return FALSE;
}

// 优化FPGA参数
_Success_(return)
BOOL FT601_OptimizeFPGAParameters(
    _In_ HANDLE hLC,
    _In_ double dTargetThroughputMBps)
{
    if (!hLC) return FALSE;

    // 根据目标吞吐量调整参数
    DWORD dwQueueDepth = 16;
    DWORD dwBurstSize = 8;

    if (dTargetThroughputMBps > 200.0) {
        dwQueueDepth = 32;
        dwBurstSize = 16;
    } else if (dTargetThroughputMBps > 100.0) {
        dwQueueDepth = 24;
        dwBurstSize = 12;
    }

    BOOL fResult = TRUE;
    fResult &= FT601_SetFPGAQueueDepth(hLC, dwQueueDepth);
    fResult &= FT601_SetFPGABurstSize(hLC, dwBurstSize);

    return fResult;
}

// 优化上位机参数
_Success_(return)
BOOL FT601_OptimizeHostParameters(
    _Inout_ PFT601_PERFORMANCE_CONFIG pConfig,
    _In_ double dTargetThroughputMBps)
{
    if (!pConfig) return FALSE;

    // 根据目标吞吐量调整缓冲区参数
    if (dTargetThroughputMBps > 200.0) {
        pConfig->dwBufferSize = 0x04000000;  // 64MB
        pConfig->dwBufferCount = 16;
        pConfig->dwWorkerThreads = 8;
    } else if (dTargetThroughputMBps > 100.0) {
        pConfig->dwBufferSize = 0x02000000;  // 32MB
        pConfig->dwBufferCount = 8;
        pConfig->dwWorkerThreads = 4;
    } else {
        pConfig->dwBufferSize = 0x01000000;  // 16MB
        pConfig->dwBufferCount = 4;
        pConfig->dwWorkerThreads = 2;
    }

    return TRUE;
}

// 设置上位机缓冲区大小
_Success_(return)
BOOL FT601_SetHostBufferSize(
    _Inout_ PFT601_PERFORMANCE_CONFIG pConfig,
    _In_ DWORD dwBufferSize)
{
    if (!pConfig) return FALSE;

    if (dwBufferSize >= FT601_OPT_BUFFER_SIZE_MIN &&
        dwBufferSize <= FT601_OPT_BUFFER_SIZE_MAX) {
        pConfig->dwBufferSize = dwBufferSize;
        return TRUE;
    }

    return FALSE;
}

// 设置工作线程数
_Success_(return)
BOOL FT601_SetHostWorkerThreads(
    _Inout_ PFT601_PERFORMANCE_CONFIG pConfig,
    _In_ DWORD dwWorkerThreads)
{
    if (!pConfig) return FALSE;

    if (dwWorkerThreads >= FT601_OPT_WORKER_THREADS_MIN &&
        dwWorkerThreads <= FT601_OPT_WORKER_THREADS_MAX) {
        pConfig->dwWorkerThreads = dwWorkerThreads;
        return TRUE;
    }

    return FALSE;
}

// 性能基准测试
_Success_(return)
BOOL FT601_RunPerformanceBenchmark(
    _In_ HANDLE hLC,
    _In_ QWORD qwTestSize,
    _Out_ PFT601_PERFORMANCE_STATS pResults)
{
    if (!hLC || !pResults) return FALSE;

    ZeroMemory(pResults, sizeof(FT601_PERFORMANCE_STATS));

    LARGE_INTEGER liStart, liEnd, liFreq;
    QueryPerformanceFrequency(&liFreq);
    QueryPerformanceCounter(&liStart);

    // 执行测试读取
    PBYTE pbBuffer = VirtualAlloc(NULL, (SIZE_T)qwTestSize, MEM_COMMIT, PAGE_READWRITE);
    if (!pbBuffer) return FALSE;

    BOOL fResult = LcRead(hLC, 0x1000, (DWORD)qwTestSize, pbBuffer);

    QueryPerformanceCounter(&liEnd);

    if (fResult) {
        double dElapsed = (double)(liEnd.QuadPart - liStart.QuadPart) / (double)liFreq.QuadPart;
        pResults->qwTotalBytesRead = qwTestSize;
        pResults->qwTotalOperations = 1;
        pResults->dCurrentThroughputMBps = (double)qwTestSize / (1024.0 * 1024.0 * dElapsed);
        pResults->dwCurrentLatencyUs = (DWORD)(dElapsed * 1000000.0);
    }

    VirtualFree(pbBuffer, 0, MEM_RELEASE);
    return fResult;
}

// 延迟测试
_Success_(return)
BOOL FT601_RunLatencyTest(
    _In_ HANDLE hLC,
    _In_ DWORD dwTestCount,
    _Out_ PDWORD pdwAverageLatencyUs,
    _Out_ PDWORD pdwMinLatencyUs,
    _Out_ PDWORD pdwMaxLatencyUs)
{
    if (!hLC || !pdwAverageLatencyUs || !pdwMinLatencyUs || !pdwMaxLatencyUs) return FALSE;

    LARGE_INTEGER liFreq;
    QueryPerformanceFrequency(&liFreq);

    DWORD dwMinLatency = MAXDWORD;
    DWORD dwMaxLatency = 0;
    QWORD qwTotalLatency = 0;

    PBYTE pbBuffer = VirtualAlloc(NULL, 0x1000, MEM_COMMIT, PAGE_READWRITE);
    if (!pbBuffer) return FALSE;

    for (DWORD i = 0; i < dwTestCount; i++) {
        LARGE_INTEGER liStart, liEnd;
        QueryPerformanceCounter(&liStart);

        LcRead(hLC, 0x1000, 0x1000, pbBuffer);

        QueryPerformanceCounter(&liEnd);

        DWORD dwLatency = (DWORD)((liEnd.QuadPart - liStart.QuadPart) * 1000000 / liFreq.QuadPart);

        qwTotalLatency += dwLatency;
        if (dwLatency < dwMinLatency) dwMinLatency = dwLatency;
        if (dwLatency > dwMaxLatency) dwMaxLatency = dwLatency;
    }

    VirtualFree(pbBuffer, 0, MEM_RELEASE);

    *pdwAverageLatencyUs = (DWORD)(qwTotalLatency / dwTestCount);
    *pdwMinLatencyUs = dwMinLatency;
    *pdwMaxLatencyUs = dwMaxLatency;

    return TRUE;
}

// 吞吐量测试
_Success_(return)
BOOL FT601_RunThroughputTest(
    _In_ HANDLE hLC,
    _In_ QWORD qwTestSize,
    _In_ DWORD dwTestDurationMs,
    _Out_ double* pdThroughputMBps)
{
    if (!hLC || !pdThroughputMBps) return FALSE;

    *pdThroughputMBps = 0.0;

    LARGE_INTEGER liStart, liEnd, liFreq;
    QueryPerformanceFrequency(&liFreq);
    QueryPerformanceCounter(&liStart);

    QWORD qwTotalBytes = 0;
    PBYTE pbBuffer = VirtualAlloc(NULL, (SIZE_T)qwTestSize, MEM_COMMIT, PAGE_READWRITE);
    if (!pbBuffer) return FALSE;

    do {
        if (LcRead(hLC, 0x1000, (DWORD)qwTestSize, pbBuffer)) {
            qwTotalBytes += qwTestSize;
        }

        QueryPerformanceCounter(&liEnd);
    } while (((liEnd.QuadPart - liStart.QuadPart) * 1000 / liFreq.QuadPart) < dwTestDurationMs);

    double dElapsed = (double)(liEnd.QuadPart - liStart.QuadPart) / (double)liFreq.QuadPart;
    *pdThroughputMBps = (double)qwTotalBytes / (1024.0 * 1024.0 * dElapsed);

    VirtualFree(pbBuffer, 0, MEM_RELEASE);
    return TRUE;
}

// 打印性能报告
VOID FT601_PrintPerformanceReport(
    _In_ PFT601_PERFORMANCE_STATS pStats,
    _In_ PFT601_PERFORMANCE_CONFIG pConfig)
{
    if (!pStats || !pConfig) return;

    printf("\n=== FT601 Performance Report ===\n");
    printf("Total Bytes Read:     %llu\n", pStats->qwTotalBytesRead);
    printf("Total Bytes Written:  %llu\n", pStats->qwTotalBytesWritten);
    printf("Total Operations:     %llu\n", pStats->qwTotalOperations);
    printf("Current Throughput:   %.2f MB/s\n", pStats->dCurrentThroughputMBps);
    printf("Average Throughput:   %.2f MB/s\n", pStats->dAverageThroughputMBps);
    printf("Peak Throughput:      %.2f MB/s\n", pStats->dPeakThroughputMBps);
    printf("Current Latency:      %u us\n", pStats->dwCurrentLatencyUs);
    printf("Average Latency:      %u us\n", pStats->dwAverageLatencyUs);
    printf("Error Count:          %u\n", pStats->dwErrorCount);
    printf("\nConfiguration:\n");
    printf("Queue Depth:          %u\n", pConfig->dwQueueDepth);
    printf("Burst Size:           %u\n", pConfig->dwBurstSize);
    printf("Buffer Size:          %u MB\n", pConfig->dwBufferSize / (1024*1024));
    printf("Worker Threads:       %u\n", pConfig->dwWorkerThreads);
    printf("Adaptive Mode:        %s\n", pConfig->fAdaptiveMode ? "Enabled" : "Disabled");
    printf("================================\n\n");
}

// 诊断性能问题
_Success_(return)
BOOL FT601_DiagnosePerformanceIssues(
    _In_ PFT601_PERFORMANCE_STATS pStats,
    _Out_writes_(256) LPSTR szDiagnosis)
{
    if (!pStats || !szDiagnosis) return FALSE;

    szDiagnosis[0] = '\0';

    if (pStats->dCurrentThroughputMBps < 50.0) {
        strcat_s(szDiagnosis, 256, "Low throughput detected. ");
        if (pStats->dwErrorCount > 0) {
            strcat_s(szDiagnosis, 256, "High error rate may be causing issues. ");
        }
        strcat_s(szDiagnosis, 256, "Consider increasing queue depth or buffer size. ");
    }

    if (pStats->dwCurrentLatencyUs > 10000) {  // > 10ms
        strcat_s(szDiagnosis, 256, "High latency detected. Consider reducing queue depth. ");
    }

    if (FT601_HAS_HIGH_ERROR_RATE(pStats)) {
        strcat_s(szDiagnosis, 256, "High error rate detected. Check hardware connections. ");
    }

    if (strlen(szDiagnosis) == 0) {
        strcpy_s(szDiagnosis, 256, "Performance appears normal.");
    }

    return TRUE;
}

// 启用自适应优化
_Success_(return)
BOOL FT601_EnableAdaptiveOptimization(
    _In_ HANDLE hLC,
    _In_ PFT601_PERFORMANCE_CONFIG pConfig)
{
    if (!hLC || !pConfig) return FALSE;

    pConfig->fAdaptiveMode = TRUE;
    return TRUE;
}

// 重置性能统计
VOID FT601_ResetPerformanceStats(VOID)
{
    ZeroMemory(&g_perfStats, sizeof(g_perfStats));
    QueryPerformanceCounter(&g_perfStats.liStartTime);
    g_perfStats.liLastUpdateTime = g_perfStats.liStartTime;
}
