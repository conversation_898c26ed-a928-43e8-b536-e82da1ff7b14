// ft601_performance_config.c : FT601性能优化配置实现
//
// 实现FT601通信层的性能优化配置和监控功能
//
// (c) 优化版本, 2025
//

#include "ft601_performance_config.h"
#include "pcileech.h"
#include <stdio.h>
#include <stdlib.h>

// 全局性能统计
static FT601_PERFORMANCE_STATS g_perfStats;
static BOOL g_fPerfMonitoringActive = FALSE;
static HANDLE g_hPerfMonitorThread = NULL;
static BOOL g_fShutdownPerfMonitor = FALSE;

// 性能监控线程
DWORD WINAPI FT601_PerformanceMonitorThread(LPVOID lpParameter)
{
    PFT601_PERFORMANCE_CONFIG pConfig = (PFT601_PERFORMANCE_CONFIG)lpParameter;
    LARGE_INTEGER liFrequency, liLastTime, liCurrentTime;
    QWORD qwLastBytesRead = 0, qwLastBytesWritten = 0;
    
    QueryPerformanceFrequency(&liFrequency);
    QueryPerformanceCounter(&liLastTime);
    
    while (!g_fShutdownPerfMonitor) {
        Sleep(pConfig->dwAdaptiveInterval);
        
        QueryPerformanceCounter(&liCurrentTime);
        
        // 计算时间差
        double dElapsedSeconds = (double)(liCurrentTime.QuadPart - liLastTime.QuadPart) 
                               / (double)liFrequency.QuadPart;
        
        if (dElapsedSeconds > 0.0) {
            // 计算当前吞吐量
            QWORD qwCurrentBytesRead = g_perfStats.qwTotalBytesRead;
            QWORD qwCurrentBytesWritten = g_perfStats.qwTotalBytesWritten;
            
            QWORD qwDeltaBytes = (qwCurrentBytesRead - qwLastBytesRead) + 
                               (qwCurrentBytesWritten - qwLastBytesWritten);
            
            g_perfStats.dCurrentThroughputMBps = (double)qwDeltaBytes / 
                                               (1024.0 * 1024.0 * dElapsedSeconds);
            
            // 更新历史数据
            g_perfStats.dThroughputHistory[g_perfStats.dwHistoryIndex] = 
                g_perfStats.dCurrentThroughputMBps;
            g_perfStats.dwHistoryIndex = (g_perfStats.dwHistoryIndex + 1) % 
                                       FT601_OPT_PERF_HISTORY_SIZE;
            if (!g_perfStats.fHistoryFull && 
                g_perfStats.dwHistoryIndex == 0) {
                g_perfStats.fHistoryFull = TRUE;
            }
            
            // 更新峰值
            if (g_perfStats.dCurrentThroughputMBps > g_perfStats.dPeakThroughputMBps) {
                g_perfStats.dPeakThroughputMBps = g_perfStats.dCurrentThroughputMBps;
            }
            
            // 计算平均值
            if (g_perfStats.fHistoryFull) {
                double dSum = 0.0;
                for (DWORD i = 0; i < FT601_OPT_PERF_HISTORY_SIZE; i++) {
                    dSum += g_perfStats.dThroughputHistory[i];
                }
                g_perfStats.dAverageThroughputMBps = dSum / FT601_OPT_PERF_HISTORY_SIZE;
            }
            
            // 自适应优化
            if (pConfig->fAdaptiveMode) {
                FT601_UpdateAdaptiveParameters(ctxMain->hLC, &g_perfStats, pConfig);
            }
            
            // 性能日志
            if (pConfig->fPerformanceLogging) {
                FT601_LogPerformanceData(&g_perfStats, pConfig->szLogFile);
            }
            
            qwLastBytesRead = qwCurrentBytesRead;
            qwLastBytesWritten = qwCurrentBytesWritten;
            liLastTime = liCurrentTime;
        }
    }
    
    return 0;
}

// 加载性能配置
_Success_(return)
BOOL FT601_LoadPerformanceConfig(
    _Out_ PFT601_PERFORMANCE_CONFIG pConfig,
    _In_opt_ LPCSTR szConfigFile)
{
    if (!pConfig) return FALSE;
    
    // 设置默认配置
    *pConfig = FT601_DEFAULT_CONFIG;
    
    // 如果指定了配置文件，尝试加载
    if (szConfigFile && strlen(szConfigFile) > 0) {
        FILE *hFile;
        if (0 == fopen_s(&hFile, szConfigFile, "r")) {
            // 简化的配置文件解析
            char szLine[256];
            while (fgets(szLine, sizeof(szLine), hFile)) {
                if (strstr(szLine, "QueueDepth=")) {
                    sscanf_s(szLine, "QueueDepth=%u", &pConfig->dwQueueDepth);
                } else if (strstr(szLine, "BurstSize=")) {
                    sscanf_s(szLine, "BurstSize=%u", &pConfig->dwBurstSize);
                } else if (strstr(szLine, "BufferSize=")) {
                    sscanf_s(szLine, "BufferSize=%u", &pConfig->dwBufferSize);
                } else if (strstr(szLine, "WorkerThreads=")) {
                    sscanf_s(szLine, "WorkerThreads=%u", &pConfig->dwWorkerThreads);
                } else if (strstr(szLine, "AdaptiveMode=")) {
                    int fAdaptive;
                    sscanf_s(szLine, "AdaptiveMode=%d", &fAdaptive);
                    pConfig->fAdaptiveMode = fAdaptive ? TRUE : FALSE;
                }
            }
            fclose(hFile);
        }
    }
    
    return TRUE;
}

// 应用性能配置到设备
_Success_(return)
BOOL FT601_ApplyPerformanceConfig(
    _In_ HANDLE hLC,
    _In_ PFT601_PERFORMANCE_CONFIG pConfig)
{
    if (!hLC || !pConfig) return FALSE;
    
    BOOL fResult = TRUE;
    
    // 应用FPGA参数
    if (!FT601_SetFPGAQueueDepth(hLC, pConfig->dwQueueDepth)) {
        fResult = FALSE;
    }
    
    if (!FT601_SetFPGABurstSize(hLC, pConfig->dwBurstSize)) {
        fResult = FALSE;
    }
    
    if (!FT601_SetFPGACooldownCycles(hLC, pConfig->dwCooldownCycles)) {
        fResult = FALSE;
    }
    
    // 设置缓冲区大小
    if (!LcSetOption(hLC, LC_OPT_FPGA_MAX_SIZE_RX, pConfig->dwBufferSize)) {
        fResult = FALSE;
    }
    
    if (!LcSetOption(hLC, LC_OPT_FPGA_MAX_SIZE_TX, pConfig->dwBufferSize)) {
        fResult = FALSE;
    }
    
    return fResult;
}

// 初始化性能监控
_Success_(return)
BOOL FT601_InitializePerformanceMonitoring(
    _In_ PFT601_PERFORMANCE_CONFIG pConfig)
{
    if (g_fPerfMonitoringActive) return TRUE;
    
    // 初始化统计结构
    ZeroMemory(&g_perfStats, sizeof(g_perfStats));
    QueryPerformanceCounter(&g_perfStats.liStartTime);
    g_perfStats.liLastUpdateTime = g_perfStats.liStartTime;
    
    // 创建监控线程
    g_fShutdownPerfMonitor = FALSE;
    g_hPerfMonitorThread = CreateThread(
        NULL, 0, FT601_PerformanceMonitorThread, pConfig, 0, NULL);
    
    if (g_hPerfMonitorThread) {
        g_fPerfMonitoringActive = TRUE;
        return TRUE;
    }
    
    return FALSE;
}

// 更新性能统计
VOID FT601_UpdatePerformanceStats(
    _Inout_ PFT601_PERFORMANCE_STATS pStats,
    _In_ QWORD qwBytesTransferred,
    _In_ DWORD dwLatencyUs)
{
    if (!pStats) return;
    
    InterlockedAdd64(&pStats->qwTotalBytesRead, qwBytesTransferred);
    InterlockedIncrement64(&pStats->qwTotalOperations);
    
    // 更新延迟统计
    pStats->dwCurrentLatencyUs = dwLatencyUs;
    if (pStats->qwTotalOperations > 0) {
        pStats->dwAverageLatencyUs = (DWORD)(
            (pStats->dwAverageLatencyUs * (pStats->qwTotalOperations - 1) + dwLatencyUs) /
            pStats->qwTotalOperations);
    }
    
    QueryPerformanceCounter(&pStats->liLastUpdateTime);
}

// 设置FPGA队列深度
_Success_(return)
BOOL FT601_SetFPGAQueueDepth(
    _In_ HANDLE hLC,
    _In_ DWORD dwQueueDepth)
{
    if (!hLC) return FALSE;
    
    // 通过自定义命令设置队列深度
    // 这需要FPGA固件支持相应的配置寄存器
    DWORD dwRegAddr = 0x8060;  // 假设的队列深度配置寄存器
    return LcCommand(hLC, LC_CMD_FPGA_CFGREGPCIE | dwRegAddr, 
                    sizeof(DWORD), (PBYTE)&dwQueueDepth, NULL, NULL);
}

// 设置FPGA突发大小
_Success_(return)
BOOL FT601_SetFPGABurstSize(
    _In_ HANDLE hLC,
    _In_ DWORD dwBurstSize)
{
    if (!hLC) return FALSE;
    
    DWORD dwRegAddr = 0x8064;  // 假设的突发大小配置寄存器
    return LcCommand(hLC, LC_CMD_FPGA_CFGREGPCIE | dwRegAddr, 
                    sizeof(DWORD), (PBYTE)&dwBurstSize, NULL, NULL);
}

// 设置FPGA冷却周期
_Success_(return)
BOOL FT601_SetFPGACooldownCycles(
    _In_ HANDLE hLC,
    _In_ DWORD dwCooldownCycles)
{
    if (!hLC) return FALSE;
    
    DWORD dwRegAddr = 0x8068;  // 假设的冷却周期配置寄存器
    return LcCommand(hLC, LC_CMD_FPGA_CFGREGPCIE | dwRegAddr, 
                    sizeof(DWORD), (PBYTE)&dwCooldownCycles, NULL, NULL);
}

// 自适应参数更新
VOID FT601_UpdateAdaptiveParameters(
    _In_ HANDLE hLC,
    _In_ PFT601_PERFORMANCE_STATS pStats,
    _Inout_ PFT601_PERFORMANCE_CONFIG pConfig)
{
    if (!hLC || !pStats || !pConfig) return;
    
    // 如果性能低于目标，尝试优化参数
    if (pStats->dCurrentThroughputMBps < pConfig->dTargetThroughput) {
        // 增加队列深度
        if (pConfig->dwQueueDepth < FT601_OPT_QUEUE_DEPTH_MAX) {
            pConfig->dwQueueDepth = min(pConfig->dwQueueDepth + 2, 
                                      FT601_OPT_QUEUE_DEPTH_MAX);
            FT601_SetFPGAQueueDepth(hLC, pConfig->dwQueueDepth);
        }
        
        // 增加突发大小
        if (pConfig->dwBurstSize < FT601_OPT_BURST_SIZE_MAX) {
            pConfig->dwBurstSize = min(pConfig->dwBurstSize + 1, 
                                     FT601_OPT_BURST_SIZE_MAX);
            FT601_SetFPGABurstSize(hLC, pConfig->dwBurstSize);
        }
    }
    
    // 如果错误率过高，降低激进程度
    if (FT601_HAS_HIGH_ERROR_RATE(pStats)) {
        if (pConfig->dwCooldownCycles < FT601_OPT_COOLDOWN_CYCLES_MAX) {
            pConfig->dwCooldownCycles++;
            FT601_SetFPGACooldownCycles(hLC, pConfig->dwCooldownCycles);
        }
    }
}

// 获取性能统计
VOID FT601_GetPerformanceStats(
    _Out_ PFT601_PERFORMANCE_STATS pStats)
{
    if (pStats) {
        *pStats = g_perfStats;
    }
}

// 清理性能监控
VOID FT601_CleanupPerformanceMonitoring(VOID)
{
    if (g_fPerfMonitoringActive) {
        g_fShutdownPerfMonitor = TRUE;
        
        if (g_hPerfMonitorThread) {
            WaitForSingleObject(g_hPerfMonitorThread, 5000);
            CloseHandle(g_hPerfMonitorThread);
            g_hPerfMonitorThread = NULL;
        }
        
        g_fPerfMonitoringActive = FALSE;
    }
}

// 性能日志记录
_Success_(return)
BOOL FT601_LogPerformanceData(
    _In_ PFT601_PERFORMANCE_STATS pStats,
    _In_opt_ LPCSTR szLogFile)
{
    if (!pStats) return FALSE;
    
    FILE *hFile;
    LPCSTR szFileName = szLogFile ? szLogFile : "ft601_performance.log";
    
    if (0 == fopen_s(&hFile, szFileName, "a")) {
        SYSTEMTIME st;
        GetLocalTime(&st);
        
        fprintf(hFile, "%04d-%02d-%02d %02d:%02d:%02d,%.2f,%.2f,%.2f,%u,%u,%u\n",
                st.wYear, st.wMonth, st.wDay, st.wHour, st.wMinute, st.wSecond,
                pStats->dCurrentThroughputMBps,
                pStats->dAverageThroughputMBps,
                pStats->dPeakThroughputMBps,
                pStats->dwCurrentLatencyUs,
                pStats->dwErrorCount,
                (DWORD)pStats->qwTotalOperations);
        
        fclose(hFile);
        return TRUE;
    }
    
    return FALSE;
}
