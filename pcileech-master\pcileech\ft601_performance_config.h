// ft601_performance_config.h : FT601性能优化配置
//
// 包含所有FT601通信层性能优化的配置参数和函数声明
//
// (c) 优化版本, 2025
//

#ifndef __FT601_PERFORMANCE_CONFIG_H__
#define __FT601_PERFORMANCE_CONFIG_H__

#include <windows.h>
#include <leechcore.h>

#ifdef __cplusplus
extern "C" {
#endif

//-----------------------------------------------------------------------------
// 性能优化配置参数
//-----------------------------------------------------------------------------

// FPGA端优化参数
#define FT601_OPT_QUEUE_DEPTH_MIN           8       // 最小队列深度
#define FT601_OPT_QUEUE_DEPTH_MAX           32      // 最大队列深度
#define FT601_OPT_BURST_SIZE_MIN            4       // 最小突发大小
#define FT601_OPT_BURST_SIZE_MAX            16      // 最大突发大小
#define FT601_OPT_COOLDOWN_CYCLES_MIN       2       // 最小冷却周期
#define FT601_OPT_COOLDOWN_CYCLES_MAX       8       // 最大冷却周期

// 上位机端优化参数
#define FT601_OPT_BUFFER_SIZE_MIN           0x00100000  // 1MB
#define FT601_OPT_BUFFER_SIZE_MAX           0x08000000  // 128MB
#define FT601_OPT_BUFFER_COUNT_MIN          4
#define FT601_OPT_BUFFER_COUNT_MAX          16
#define FT601_OPT_WORKER_THREADS_MIN        2
#define FT601_OPT_WORKER_THREADS_MAX        8

// 性能监控参数
#define FT601_OPT_PERF_SAMPLE_INTERVAL      1000    // 1秒采样间隔
#define FT601_OPT_PERF_HISTORY_SIZE         60      // 保存60个样本
#define FT601_OPT_PERF_THRESHOLD_MBPS       100.0   // 100MB/s阈值

// 前向声明
typedef struct tdFT601_PERFORMANCE_CONFIG FT601_PERFORMANCE_CONFIG;
typedef struct tdFT601_PERFORMANCE_STATS FT601_PERFORMANCE_STATS;

//-----------------------------------------------------------------------------
// 性能优化配置结构
//-----------------------------------------------------------------------------

typedef struct tdFT601_PERFORMANCE_CONFIG {
    // FPGA配置
    DWORD dwQueueDepth;
    DWORD dwBurstSize;
    DWORD dwCooldownCycles;
    BOOL fDualBufferEnabled;
    BOOL fPipelineEnabled;
    
    // 上位机配置
    DWORD dwBufferSize;
    DWORD dwBufferCount;
    DWORD dwWorkerThreads;
    DWORD dwBatchSize;
    BOOL fAsyncIOEnabled;
    BOOL fCompressionEnabled;
    
    // 自适应配置
    BOOL fAdaptiveMode;
    DWORD dwAdaptiveInterval;
    double dTargetThroughput;
    double dMinThroughput;
    
    // 调试配置
    BOOL fPerformanceLogging;
    BOOL fDetailedStats;
    CHAR szLogFile[MAX_PATH];
} FT601_PERFORMANCE_CONFIG, *PFT601_PERFORMANCE_CONFIG;

typedef struct tdFT601_PERFORMANCE_STATS {
    // 基础统计
    QWORD qwTotalBytesRead;
    QWORD qwTotalBytesWritten;
    QWORD qwTotalOperations;
    LARGE_INTEGER liStartTime;
    LARGE_INTEGER liLastUpdateTime;
    
    // 性能指标
    double dCurrentThroughputMBps;
    double dAverageThroughputMBps;
    double dPeakThroughputMBps;
    DWORD dwCurrentLatencyUs;
    DWORD dwAverageLatencyUs;
    
    // 错误统计
    DWORD dwErrorCount;
    DWORD dwRetryCount;
    DWORD dwTimeoutCount;
    
    // 历史数据
    double dThroughputHistory[60];  // 固定大小数组
    DWORD dwHistoryIndex;
    BOOL fHistoryFull;
} FT601_PERFORMANCE_STATS, *PFT601_PERFORMANCE_STATS;

//-----------------------------------------------------------------------------
// 性能优化函数声明
//-----------------------------------------------------------------------------

// 配置管理
_Success_(return)
BOOL FT601_LoadPerformanceConfig(
    _Out_ PFT601_PERFORMANCE_CONFIG pConfig,
    _In_opt_ LPCSTR szConfigFile
);

_Success_(return)
BOOL FT601_SavePerformanceConfig(
    _In_ PFT601_PERFORMANCE_CONFIG pConfig,
    _In_opt_ LPCSTR szConfigFile
);

_Success_(return)
BOOL FT601_ApplyPerformanceConfig(
    _In_ HANDLE hLC,
    _In_ PFT601_PERFORMANCE_CONFIG pConfig
);

// 性能监控
_Success_(return)
BOOL FT601_InitializePerformanceMonitoring(
    _In_ PFT601_PERFORMANCE_CONFIG pConfig
);

VOID FT601_UpdatePerformanceStats(
    _Inout_ PFT601_PERFORMANCE_STATS pStats,
    _In_ QWORD qwBytesTransferred,
    _In_ DWORD dwLatencyUs
);

VOID FT601_GetPerformanceStats(
    _Out_ PFT601_PERFORMANCE_STATS pStats
);

VOID FT601_ResetPerformanceStats(VOID);

VOID FT601_CleanupPerformanceMonitoring(VOID);

// 自适应优化
_Success_(return)
BOOL FT601_EnableAdaptiveOptimization(
    _In_ HANDLE hLC,
    _In_ PFT601_PERFORMANCE_CONFIG pConfig
);

VOID FT601_UpdateAdaptiveParameters(
    _In_ HANDLE hLC,
    _In_ PFT601_PERFORMANCE_STATS pStats,
    _Inout_ PFT601_PERFORMANCE_CONFIG pConfig
);

// FPGA参数调优
_Success_(return)
BOOL FT601_OptimizeFPGAParameters(
    _In_ HANDLE hLC,
    _In_ double dTargetThroughputMBps
);

_Success_(return)
BOOL FT601_SetFPGAQueueDepth(
    _In_ HANDLE hLC,
    _In_ DWORD dwQueueDepth
);

_Success_(return)
BOOL FT601_SetFPGABurstSize(
    _In_ HANDLE hLC,
    _In_ DWORD dwBurstSize
);

_Success_(return)
BOOL FT601_SetFPGACooldownCycles(
    _In_ HANDLE hLC,
    _In_ DWORD dwCooldownCycles
);

// 上位机参数调优
_Success_(return)
BOOL FT601_OptimizeHostParameters(
    _Inout_ PFT601_PERFORMANCE_CONFIG pConfig,
    _In_ double dTargetThroughputMBps
);

_Success_(return)
BOOL FT601_SetHostBufferSize(
    _Inout_ PFT601_PERFORMANCE_CONFIG pConfig,
    _In_ DWORD dwBufferSize
);

_Success_(return)
BOOL FT601_SetHostWorkerThreads(
    _Inout_ PFT601_PERFORMANCE_CONFIG pConfig,
    _In_ DWORD dwWorkerThreads
);

// 性能测试
_Success_(return)
BOOL FT601_RunPerformanceBenchmark(
    _In_ HANDLE hLC,
    _In_ QWORD qwTestSize,
    _Out_ PFT601_PERFORMANCE_STATS pResults
);

_Success_(return)
BOOL FT601_RunLatencyTest(
    _In_ HANDLE hLC,
    _In_ DWORD dwTestCount,
    _Out_ PDWORD pdwAverageLatencyUs,
    _Out_ PDWORD pdwMinLatencyUs,
    _Out_ PDWORD pdwMaxLatencyUs
);

_Success_(return)
BOOL FT601_RunThroughputTest(
    _In_ HANDLE hLC,
    _In_ QWORD qwTestSize,
    _In_ DWORD dwTestDurationMs,
    _Out_ double* pdThroughputMBps
);

// 诊断和调试
VOID FT601_PrintPerformanceReport(
    _In_ PFT601_PERFORMANCE_STATS pStats,
    _In_ PFT601_PERFORMANCE_CONFIG pConfig
);

_Success_(return)
BOOL FT601_LogPerformanceData(
    _In_ PFT601_PERFORMANCE_STATS pStats,
    _In_opt_ LPCSTR szLogFile
);

_Success_(return)
BOOL FT601_DiagnosePerformanceIssues(
    _In_ PFT601_PERFORMANCE_STATS pStats,
    _Out_writes_(256) LPSTR szDiagnosis
);

//-----------------------------------------------------------------------------
// 默认配置
//-----------------------------------------------------------------------------

// 默认配置初始化函数声明
VOID FT601_InitDefaultConfig(_Out_ PFT601_PERFORMANCE_CONFIG pConfig);

//-----------------------------------------------------------------------------
// 性能优化宏定义
//-----------------------------------------------------------------------------

// 快速配置函数声明
VOID FT601_ConfigHighPerformance(_Inout_ PFT601_PERFORMANCE_CONFIG pConfig);
VOID FT601_ConfigLowLatency(_Inout_ PFT601_PERFORMANCE_CONFIG pConfig);
VOID FT601_ConfigBalanced(_Inout_ PFT601_PERFORMANCE_CONFIG pConfig);

// 性能检查宏
#define FT601_IS_HIGH_PERFORMANCE(stats) \
    ((stats)->dCurrentThroughputMBps > 150.0)

#define FT601_IS_LOW_PERFORMANCE(stats) \
    ((stats)->dCurrentThroughputMBps < 50.0)

#define FT601_HAS_HIGH_ERROR_RATE(stats) \
    (((stats)->dwErrorCount * 100 / ((stats)->qwTotalOperations > 1 ? (stats)->qwTotalOperations : 1)) > 5)

#ifdef __cplusplus
}
#endif

#endif /* __FT601_PERFORMANCE_CONFIG_H__ */
