// ft601_performance_config.h : 简化的FT601性能优化配置
// 专门用于解决编译问题的简化版本

#ifndef __FT601_PERFORMANCE_CONFIG_H__
#define __FT601_PERFORMANCE_CONFIG_H__

#include <windows.h>
#include <leechcore.h>

#ifdef __cplusplus
extern "C" {
#endif

//-----------------------------------------------------------------------------
// 性能优化配置参数
//-----------------------------------------------------------------------------

// FPGA端优化参数
#define FT601_OPT_QUEUE_DEPTH_MIN           8       // 最小队列深度
#define FT601_OPT_QUEUE_DEPTH_MAX           32      // 最大队列深度
#define FT601_OPT_BURST_SIZE_MIN            4       // 最小突发大小
#define FT601_OPT_BURST_SIZE_MAX            16      // 最大突发大小
#define FT601_OPT_COOLDOWN_CYCLES_MIN       2       // 最小冷却周期
#define FT601_OPT_COOLDOWN_CYCLES_MAX       8       // 最大冷却周期

// 上位机端优化参数
#define FT601_OPT_BUFFER_SIZE_MIN           0x00100000  // 1MB
#define FT601_OPT_BUFFER_SIZE_MAX           0x08000000  // 128MB
#define FT601_OPT_BUFFER_COUNT_MIN          4
#define FT601_OPT_BUFFER_COUNT_MAX          16
#define FT601_OPT_WORKER_THREADS_MIN        2
#define FT601_OPT_WORKER_THREADS_MAX        8

// 性能监控参数
#define FT601_OPT_PERF_SAMPLE_INTERVAL      1000    // 1秒采样间隔
#define FT601_OPT_PERF_HISTORY_SIZE         60      // 保存60个样本
#define FT601_OPT_PERF_THRESHOLD_MBPS       100.0   // 100MB/s阈值

// 优化写入线程数
#define OPTIMIZED_WRITER_THREADS            4

//-----------------------------------------------------------------------------
// 结构体定义
//-----------------------------------------------------------------------------

typedef struct {
    // FPGA配置
    DWORD dwQueueDepth;
    DWORD dwBurstSize;
    DWORD dwCooldownCycles;
    BOOL fDualBufferEnabled;
    BOOL fPipelineEnabled;

    // 上位机配置
    DWORD dwBufferSize;
    DWORD dwBufferCount;
    DWORD dwWorkerThreads;
    DWORD dwBatchSize;
    BOOL fAsyncIOEnabled;
    BOOL fCompressionEnabled;

    // 自适应配置
    BOOL fAdaptiveMode;
    DWORD dwAdaptiveInterval;
    double dTargetThroughput;
    double dMinThroughput;

    // 调试配置
    BOOL fPerformanceLogging;
    BOOL fDetailedStats;
    CHAR szLogFile[MAX_PATH];
} FT601_PERFORMANCE_CONFIG, *PFT601_PERFORMANCE_CONFIG;

typedef struct {
    // 基础统计
    QWORD qwTotalBytesRead;
    QWORD qwTotalBytesWritten;
    QWORD qwTotalOperations;
    LARGE_INTEGER liStartTime;
    LARGE_INTEGER liLastUpdateTime;

    // 性能指标
    double dCurrentThroughputMBps;
    double dAverageThroughputMBps;
    double dPeakThroughputMBps;
    DWORD dwCurrentLatencyUs;
    DWORD dwAverageLatencyUs;

    // 错误统计
    DWORD dwErrorCount;
    DWORD dwRetryCount;
    DWORD dwTimeoutCount;

    // 历史数据
    double dThroughputHistory[60];
    DWORD dwHistoryIndex;
    BOOL fHistoryFull;
} FT601_PERFORMANCE_STATS, *PFT601_PERFORMANCE_STATS;

//-----------------------------------------------------------------------------
// 函数声明
//-----------------------------------------------------------------------------

// 基本配置函数
VOID FT601_InitDefaultConfig(PFT601_PERFORMANCE_CONFIG pConfig);
VOID FT601_ConfigHighPerformance(PFT601_PERFORMANCE_CONFIG pConfig);
VOID FT601_ConfigLowLatency(PFT601_PERFORMANCE_CONFIG pConfig);
VOID FT601_ConfigBalanced(PFT601_PERFORMANCE_CONFIG pConfig);

// 配置管理
BOOL FT601_LoadPerformanceConfig(PFT601_PERFORMANCE_CONFIG pConfig, LPCSTR szConfigFile);
BOOL FT601_SavePerformanceConfig(PFT601_PERFORMANCE_CONFIG pConfig, LPCSTR szConfigFile);
BOOL FT601_ApplyPerformanceConfig(HANDLE hLC, PFT601_PERFORMANCE_CONFIG pConfig);

// 性能监控
BOOL FT601_InitializePerformanceMonitoring(PFT601_PERFORMANCE_CONFIG pConfig);
VOID FT601_UpdatePerformanceStats(PFT601_PERFORMANCE_STATS pStats, QWORD qwBytesTransferred, DWORD dwLatencyUs);
VOID FT601_GetPerformanceStats(PFT601_PERFORMANCE_STATS pStats);
VOID FT601_ResetPerformanceStats(VOID);
VOID FT601_CleanupPerformanceMonitoring(VOID);

// FPGA参数调优
BOOL FT601_OptimizeFPGAParameters(HANDLE hLC, double dTargetThroughputMBps);
BOOL FT601_SetFPGAQueueDepth(HANDLE hLC, DWORD dwQueueDepth);
BOOL FT601_SetFPGABurstSize(HANDLE hLC, DWORD dwBurstSize);
BOOL FT601_SetFPGACooldownCycles(HANDLE hLC, DWORD dwCooldownCycles);

// 上位机参数调优
BOOL FT601_OptimizeHostParameters(PFT601_PERFORMANCE_CONFIG pConfig, double dTargetThroughputMBps);
BOOL FT601_SetHostBufferSize(PFT601_PERFORMANCE_CONFIG pConfig, DWORD dwBufferSize);
BOOL FT601_SetHostWorkerThreads(PFT601_PERFORMANCE_CONFIG pConfig, DWORD dwWorkerThreads);

// 自适应优化
BOOL FT601_EnableAdaptiveOptimization(HANDLE hLC, PFT601_PERFORMANCE_CONFIG pConfig);
VOID FT601_UpdateAdaptiveParameters(HANDLE hLC, PFT601_PERFORMANCE_STATS pStats, PFT601_PERFORMANCE_CONFIG pConfig);

// 性能测试
BOOL FT601_RunPerformanceBenchmark(HANDLE hLC, QWORD qwTestSize, PFT601_PERFORMANCE_STATS pResults);
BOOL FT601_RunLatencyTest(HANDLE hLC, DWORD dwTestCount, PDWORD pdwAverageLatencyUs, PDWORD pdwMinLatencyUs, PDWORD pdwMaxLatencyUs);
BOOL FT601_RunThroughputTest(HANDLE hLC, QWORD qwTestSize, DWORD dwTestDurationMs, double* pdThroughputMBps);

// 诊断和调试
VOID FT601_PrintPerformanceReport(PFT601_PERFORMANCE_STATS pStats, PFT601_PERFORMANCE_CONFIG pConfig);
BOOL FT601_LogPerformanceData(PFT601_PERFORMANCE_STATS pStats, LPCSTR szLogFile);
BOOL FT601_DiagnosePerformanceIssues(PFT601_PERFORMANCE_STATS pStats, LPSTR szDiagnosis);

//-----------------------------------------------------------------------------
// 性能优化宏定义
//-----------------------------------------------------------------------------

// 性能检查宏
#define FT601_IS_HIGH_PERFORMANCE(stats) \
    ((stats)->dCurrentThroughputMBps > 150.0)

#define FT601_IS_LOW_PERFORMANCE(stats) \
    ((stats)->dCurrentThroughputMBps < 50.0)

#define FT601_HAS_HIGH_ERROR_RATE(stats) \
    (((stats)->dwErrorCount * 100 / ((stats)->qwTotalOperations > 1 ? (stats)->qwTotalOperations : 1)) > 5)

#ifdef __cplusplus
}
#endif

#endif /* __FT601_PERFORMANCE_CONFIG_H__ */
