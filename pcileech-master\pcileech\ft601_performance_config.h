// ft601_performance_config.h : FT601性能优化配置
// 简化版本，专门解决编译问题

#ifndef __FT601_PERFORMANCE_CONFIG_H__
#define __FT601_PERFORMANCE_CONFIG_H__

#include <windows.h>
#include <leechcore.h>
#include <stdio.h>

#ifdef __cplusplus
extern "C" {
#endif

//-----------------------------------------------------------------------------
// 常量定义
//-----------------------------------------------------------------------------

#define FT601_OPT_QUEUE_DEPTH_MIN           8
#define FT601_OPT_QUEUE_DEPTH_MAX           32
#define FT601_OPT_BURST_SIZE_MIN            4
#define FT601_OPT_BURST_SIZE_MAX            16
#define FT601_OPT_COOLDOWN_CYCLES_MIN       2
#define FT601_OPT_COOLDOWN_CYCLES_MAX       8
#define FT601_OPT_BUFFER_SIZE_MIN           0x00100000
#define FT601_OPT_BUFFER_SIZE_MAX           0x08000000
#define FT601_OPT_BUFFER_COUNT_MIN          4
#define FT601_OPT_BUFFER_COUNT_MAX          16
#define FT601_OPT_WORKER_THREADS_MIN        2
#define FT601_OPT_WORKER_THREADS_MAX        8
#define FT601_OPT_PERF_SAMPLE_INTERVAL      1000
#define FT601_OPT_PERF_HISTORY_SIZE         60
#define FT601_OPT_PERF_THRESHOLD_MBPS       100.0
#define OPTIMIZED_WRITER_THREADS            4

//-----------------------------------------------------------------------------
// 结构体定义
//-----------------------------------------------------------------------------

typedef struct FT601_PERFORMANCE_CONFIG_TAG {
    DWORD dwQueueDepth;
    DWORD dwBurstSize;
    DWORD dwCooldownCycles;
    BOOL fDualBufferEnabled;
    BOOL fPipelineEnabled;
    DWORD dwBufferSize;
    DWORD dwBufferCount;
    DWORD dwWorkerThreads;
    DWORD dwBatchSize;
    BOOL fAsyncIOEnabled;
    BOOL fCompressionEnabled;
    BOOL fAdaptiveMode;
    DWORD dwAdaptiveInterval;
    double dTargetThroughput;
    double dMinThroughput;
    BOOL fPerformanceLogging;
    BOOL fDetailedStats;
    CHAR szLogFile[MAX_PATH];
} FT601_PERFORMANCE_CONFIG;

typedef FT601_PERFORMANCE_CONFIG* PFT601_PERFORMANCE_CONFIG;

typedef struct FT601_PERFORMANCE_STATS_TAG {
    QWORD qwTotalBytesRead;
    QWORD qwTotalBytesWritten;
    QWORD qwTotalOperations;
    LARGE_INTEGER liStartTime;
    LARGE_INTEGER liLastUpdateTime;
    double dCurrentThroughputMBps;
    double dAverageThroughputMBps;
    double dPeakThroughputMBps;
    DWORD dwCurrentLatencyUs;
    DWORD dwAverageLatencyUs;
    DWORD dwErrorCount;
    DWORD dwRetryCount;
    DWORD dwTimeoutCount;
    double dThroughputHistory[60];
    DWORD dwHistoryIndex;
    BOOL fHistoryFull;
} FT601_PERFORMANCE_STATS;

typedef FT601_PERFORMANCE_STATS* PFT601_PERFORMANCE_STATS;

//-----------------------------------------------------------------------------
// 函数声明
//-----------------------------------------------------------------------------

VOID FT601_InitDefaultConfig(PFT601_PERFORMANCE_CONFIG pConfig);
VOID FT601_ConfigHighPerformance(PFT601_PERFORMANCE_CONFIG pConfig);
VOID FT601_ConfigLowLatency(PFT601_PERFORMANCE_CONFIG pConfig);
VOID FT601_ConfigBalanced(PFT601_PERFORMANCE_CONFIG pConfig);
BOOL FT601_LoadPerformanceConfig(PFT601_PERFORMANCE_CONFIG pConfig, LPCSTR szConfigFile);
BOOL FT601_SavePerformanceConfig(PFT601_PERFORMANCE_CONFIG pConfig, LPCSTR szConfigFile);
BOOL FT601_ApplyPerformanceConfig(HANDLE hLC, PFT601_PERFORMANCE_CONFIG pConfig);
BOOL FT601_InitializePerformanceMonitoring(PFT601_PERFORMANCE_CONFIG pConfig);
VOID FT601_UpdatePerformanceStats(PFT601_PERFORMANCE_STATS pStats, QWORD qwBytesTransferred, DWORD dwLatencyUs);
VOID FT601_GetPerformanceStats(PFT601_PERFORMANCE_STATS pStats);
VOID FT601_ResetPerformanceStats(VOID);
VOID FT601_CleanupPerformanceMonitoring(VOID);
BOOL FT601_OptimizeFPGAParameters(HANDLE hLC, double dTargetThroughputMBps);
BOOL FT601_SetFPGAQueueDepth(HANDLE hLC, DWORD dwQueueDepth);
BOOL FT601_SetFPGABurstSize(HANDLE hLC, DWORD dwBurstSize);
BOOL FT601_SetFPGACooldownCycles(HANDLE hLC, DWORD dwCooldownCycles);
BOOL FT601_OptimizeHostParameters(PFT601_PERFORMANCE_CONFIG pConfig, double dTargetThroughputMBps);
BOOL FT601_SetHostBufferSize(PFT601_PERFORMANCE_CONFIG pConfig, DWORD dwBufferSize);
BOOL FT601_SetHostWorkerThreads(PFT601_PERFORMANCE_CONFIG pConfig, DWORD dwWorkerThreads);
BOOL FT601_EnableAdaptiveOptimization(HANDLE hLC, PFT601_PERFORMANCE_CONFIG pConfig);
VOID FT601_UpdateAdaptiveParameters(HANDLE hLC, PFT601_PERFORMANCE_STATS pStats, PFT601_PERFORMANCE_CONFIG pConfig);
BOOL FT601_RunPerformanceBenchmark(HANDLE hLC, QWORD qwTestSize, PFT601_PERFORMANCE_STATS pResults);
BOOL FT601_RunLatencyTest(HANDLE hLC, DWORD dwTestCount, PDWORD pdwAverageLatencyUs, PDWORD pdwMinLatencyUs, PDWORD pdwMaxLatencyUs);
BOOL FT601_RunThroughputTest(HANDLE hLC, QWORD qwTestSize, DWORD dwTestDurationMs, double* pdThroughputMBps);
VOID FT601_PrintPerformanceReport(PFT601_PERFORMANCE_STATS pStats, PFT601_PERFORMANCE_CONFIG pConfig);
BOOL FT601_LogPerformanceData(PFT601_PERFORMANCE_STATS pStats, LPCSTR szLogFile);
BOOL FT601_DiagnosePerformanceIssues(PFT601_PERFORMANCE_STATS pStats, LPSTR szDiagnosis);

//-----------------------------------------------------------------------------
// 性能优化宏定义
//-----------------------------------------------------------------------------

// 性能检查宏
#define FT601_IS_HIGH_PERFORMANCE(stats) \
    ((stats)->dCurrentThroughputMBps > 150.0)

#define FT601_IS_LOW_PERFORMANCE(stats) \
    ((stats)->dCurrentThroughputMBps < 50.0)

#define FT601_HAS_HIGH_ERROR_RATE(stats) \
    (((stats)->dwErrorCount * 100 / ((stats)->qwTotalOperations > 1 ? (stats)->qwTotalOperations : 1)) > 5)

#ifdef __cplusplus
}
#endif

#endif /* __FT601_PERFORMANCE_CONFIG_H__ */
