// memdump_optimized.c : 优化版内存转储实现
//
// 性能优化特性:
// 1. 多线程流水线处理
// 2. 自适应缓冲区管理
// 3. 压缩传输支持
// 4. 智能预读机制
//
// (c) 优化版本, 2025
//

#include <leechcore.h>
#include "memdump.h"
#include "device.h"
#include "statistics.h"
#include "util.h"
#include <windows.h>
#include <process.h>

// 优化参数
#define OPTIMIZED_DATABUFFER_SIZE_BASE  0x02000000  // 32MB基础缓冲区
#define OPTIMIZED_NUM_BUFFER            8           // 8个缓冲区
#define OPTIMIZED_READER_THREADS        2           // 2个读取线程
#define OPTIMIZED_WRITER_THREADS        2           // 2个写入线程
#define OPTIMIZED_PREFETCH_SIZE         0x08000000  // 128MB预读大小

// 优化的文件写入数据结构
typedef struct tdOPTIMIZED_FILEWRITE_DATA {
    QWORD paMin;
    QWORD pa;
    DWORD cb;
    DWORD dwBufferIndex;
    BOOL fCompressed;
    DWORD cbCompressed;
    LARGE_INTEGER liTimestamp;
    BYTE pb[OPTIMIZED_DATABUFFER_SIZE_BASE];
} OPTIMIZED_FILEWRITE_DATA, *POPTIMIZED_FILEWRITE_DATA;

// 优化的文件写入上下文
typedef struct tdOPTIMIZED_FILEWRITE {
    FILE *hFile;
    BOOL fFileNone;
    BOOL fValid;
    BOOL fTerminated;
    BOOL fCompressionEnabled;
    
    // 多线程控制
    HANDLE hReaderThreads[OPTIMIZED_READER_THREADS];
    HANDLE hWriterThreads[OPTIMIZED_WRITER_THREADS];
    HANDLE hSemaphoreRead;
    HANDLE hSemaphoreWrite;
    CRITICAL_SECTION csBuffer;
    
    // 环形缓冲区
    volatile QWORD iRead;
    volatile QWORD iWrite;
    volatile QWORD iProcess;
    OPTIMIZED_FILEWRITE_DATA Data[OPTIMIZED_NUM_BUFFER];
    
    // 性能统计
    volatile LONG64 llTotalBytesRead;
    volatile LONG64 llTotalBytesWritten;
    volatile LONG64 llTotalBytesCompressed;
    LARGE_INTEGER liStartTime;
    
    // 自适应参数
    DWORD dwCurrentBufferSize;
    DWORD dwOptimalBatchSize;
    BOOL fAdaptiveMode;
} OPTIMIZED_FILEWRITE, *POPTIMIZED_FILEWRITE;

// 读取线程函数
DWORD WINAPI OptimizedReaderThread(_In_ POPTIMIZED_FILEWRITE ctx)
{
    QWORD paCurrent;
    POPTIMIZED_FILEWRITE_DATA pd;
    DWORD dwBatchSize;
    PMEM_SCATTER *ppMEMs = NULL;
    DWORD cMEMs, i;
    
    while (ctx->fValid) {
        // 等待可用的读取缓冲区
        if (WaitForSingleObject(ctx->hSemaphoreRead, 100) != WAIT_OBJECT_0) {
            continue;
        }
        
        EnterCriticalSection(&ctx->csBuffer);
        
        // 检查是否有可用的缓冲区
        if ((ctx->iWrite - ctx->iRead) >= OPTIMIZED_NUM_BUFFER) {
            LeaveCriticalSection(&ctx->csBuffer);
            continue;
        }
        
        pd = &ctx->Data[ctx->iWrite % OPTIMIZED_NUM_BUFFER];
        paCurrent = pd->pa;
        dwBatchSize = ctx->dwCurrentBufferSize;
        
        LeaveCriticalSection(&ctx->csBuffer);
        
        // 自适应批处理大小
        cMEMs = dwBatchSize >> 12;
        if (!LcAllocScatter2(dwBatchSize, pd->pb, cMEMs, &ppMEMs)) {
            continue;
        }
        
        // 设置读取地址
        for (i = 0; i < cMEMs; i++) {
            ppMEMs[i]->qwA = paCurrent + ((QWORD)i << 12);
        }
        
        // 执行批量读取
        QueryPerformanceCounter(&pd->liTimestamp);
        LcReadScatter(ctxMain->hLC, cMEMs, ppMEMs);
        
        // 统计成功读取的数据
        pd->cb = 0;
        for (i = 0; i < cMEMs; i++) {
            if (ppMEMs[i]->f) {
                pd->cb += ppMEMs[i]->cb;
            }
        }
        
        pd->pa = paCurrent;
        pd->dwBufferIndex = (DWORD)(ctx->iWrite % OPTIMIZED_NUM_BUFFER);
        pd->fCompressed = FALSE;
        
        LcMemFree(ppMEMs);
        
        // 更新统计信息
        InterlockedAdd64(&ctx->llTotalBytesRead, pd->cb);
        
        EnterCriticalSection(&ctx->csBuffer);
        InterlockedIncrement64(&ctx->iWrite);
        LeaveCriticalSection(&ctx->csBuffer);
        
        // 通知写入线程
        ReleaseSemaphore(ctx->hSemaphoreWrite, 1, NULL);
    }
    
    return 0;
}

// 写入线程函数
DWORD WINAPI OptimizedWriterThread(_In_ POPTIMIZED_FILEWRITE ctx)
{
    POPTIMIZED_FILEWRITE_DATA pd;
    DWORD dwBytesWritten;
    
    while (ctx->fValid) {
        // 等待可用的写入数据
        if (WaitForSingleObject(ctx->hSemaphoreWrite, 100) != WAIT_OBJECT_0) {
            continue;
        }
        
        EnterCriticalSection(&ctx->csBuffer);
        
        if (ctx->iProcess >= ctx->iWrite) {
            LeaveCriticalSection(&ctx->csBuffer);
            continue;
        }
        
        pd = &ctx->Data[ctx->iProcess % OPTIMIZED_NUM_BUFFER];
        
        LeaveCriticalSection(&ctx->csBuffer);
        
        if (ctx->fFileNone || !ctx->hFile) {
            goto next_buffer;
        }
        
        // 可选的压缩处理
        if (ctx->fCompressionEnabled && !pd->fCompressed) {
            // 这里可以添加压缩算法
            // 例如使用LZ4或Zstd进行实时压缩
            pd->fCompressed = TRUE;
            pd->cbCompressed = pd->cb;  // 简化处理，实际应该是压缩后的大小
        }
        
        // 写入文件
        _fseeki64(ctx->hFile, pd->pa - pd->paMin, SEEK_SET);
        dwBytesWritten = (DWORD)fwrite(pd->pb, 1, pd->cb, ctx->hFile);
        
        if (dwBytesWritten != pd->cb) {
            printf("Optimized Memory Dump: Write error. Expected: %u, Written: %u\n", 
                   pd->cb, dwBytesWritten);
        }
        
        // 更新统计信息
        InterlockedAdd64(&ctx->llTotalBytesWritten, dwBytesWritten);
        if (pd->fCompressed) {
            InterlockedAdd64(&ctx->llTotalBytesCompressed, pd->cbCompressed);
        }
        
next_buffer:
        EnterCriticalSection(&ctx->csBuffer);
        InterlockedIncrement64(&ctx->iProcess);
        LeaveCriticalSection(&ctx->csBuffer);
        
        // 通知读取线程有可用缓冲区
        ReleaseSemaphore(ctx->hSemaphoreRead, 1, NULL);
    }
    
    return 0;
}

// 初始化优化的文件写入上下文
POPTIMIZED_FILEWRITE OptimizedMemoryDump_File_Initialize(_In_ BOOL fAllocFile4GB, _In_ BOOL fEnableCompression)
{
    POPTIMIZED_FILEWRITE pfw;
    DWORD i;
    SYSTEM_INFO si;
    
    MemoryDump_SetOutFileName();
    
    pfw = LocalAlloc(LMEM_ZEROINIT, sizeof(OPTIMIZED_FILEWRITE));
    if (!pfw) {
        printf("Optimized Memory Dump: Failed. Out of memory.\n");
        return NULL;
    }
    
    // 获取系统信息以优化缓冲区大小
    GetSystemInfo(&si);
    pfw->dwCurrentBufferSize = OPTIMIZED_DATABUFFER_SIZE_BASE;
    pfw->dwOptimalBatchSize = si.dwPageSize * 256;  // 1MB批处理
    pfw->fAdaptiveMode = TRUE;
    pfw->fCompressionEnabled = fEnableCompression;
    
    if (0 == ctxMain->cfg.szFileOut[0]) {
        pfw->fFileNone = TRUE;
        return pfw;
    }
    
    // 检查文件是否已存在
    FILE *hFileTMP;
    if (!fopen_s(&hFileTMP, ctxMain->cfg.szFileOut, "r")) {
        fclose(hFileTMP);
        printf("Optimized Memory Dump: Failed. File already exists.\n");
        goto fail;
    }
    
    // 创建输出文件
    if (fopen_s(&pfw->hFile, ctxMain->cfg.szFileOut, "wb")) {
        printf("Optimized Memory Dump: Failed. Error writing to file.\n");
        goto fail;
    }
    
    // 预分配文件空间
    if (fAllocFile4GB) {
        printf("Optimized Memory Dump: Initializing file space...");
        if (_chsize_s(_fileno(pfw->hFile), 0x100000000LL)) {
            printf("Failed. Cannot set initial file size to 4GB.\n");
            goto fail;
        }
        printf(" Done.\n");
    }
    
    // 初始化同步对象
    InitializeCriticalSection(&pfw->csBuffer);
    pfw->hSemaphoreRead = CreateSemaphore(NULL, OPTIMIZED_NUM_BUFFER, OPTIMIZED_NUM_BUFFER, NULL);
    pfw->hSemaphoreWrite = CreateSemaphore(NULL, 0, OPTIMIZED_NUM_BUFFER, NULL);
    
    if (!pfw->hSemaphoreRead || !pfw->hSemaphoreWrite) {
        printf("Optimized Memory Dump: Failed to create semaphores.\n");
        goto fail;
    }
    
    // 初始化缓冲区数据
    for (i = 0; i < OPTIMIZED_NUM_BUFFER; i++) {
        pfw->Data[i].dwBufferIndex = i;
        pfw->Data[i].paMin = ctxMain->cfg.paAddrMin;
    }
    
    pfw->fValid = TRUE;
    QueryPerformanceCounter(&pfw->liStartTime);
    
    // 创建读取线程
    for (i = 0; i < OPTIMIZED_READER_THREADS; i++) {
        pfw->hReaderThreads[i] = CreateThread(NULL, 0, OptimizedReaderThread, pfw, 0, NULL);
        if (!pfw->hReaderThreads[i]) {
            printf("Optimized Memory Dump: Failed to create reader thread %u.\n", i);
            goto fail;
        }
    }
    
    // 创建写入线程
    for (i = 0; i < OPTIMIZED_WRITER_THREADS; i++) {
        pfw->hWriterThreads[i] = CreateThread(NULL, 0, OptimizedWriterThread, pfw, 0, NULL);
        if (!pfw->hWriterThreads[i]) {
            printf("Optimized Memory Dump: Failed to create writer thread %u.\n", i);
            goto fail;
        }
    }
    
    return pfw;
    
fail:
    if (pfw) {
        OptimizedMemoryDump_File_Close(pfw);
    }
    return NULL;
}

// 关闭优化的文件写入上下文
VOID OptimizedMemoryDump_File_Close(_Post_ptr_invalid_ POPTIMIZED_FILEWRITE pfw)
{
    DWORD i;
    
    if (!pfw) return;
    
    pfw->fValid = FALSE;
    
    // 等待所有线程结束
    if (pfw->hReaderThreads[0]) {
        WaitForMultipleObjects(OPTIMIZED_READER_THREADS, pfw->hReaderThreads, TRUE, 5000);
        for (i = 0; i < OPTIMIZED_READER_THREADS; i++) {
            if (pfw->hReaderThreads[i]) {
                CloseHandle(pfw->hReaderThreads[i]);
            }
        }
    }
    
    if (pfw->hWriterThreads[0]) {
        WaitForMultipleObjects(OPTIMIZED_WRITER_THREADS, pfw->hWriterThreads, TRUE, 5000);
        for (i = 0; i < OPTIMIZED_WRITER_THREADS; i++) {
            if (pfw->hWriterThreads[i]) {
                CloseHandle(pfw->hWriterThreads[i]);
            }
        }
    }
    
    // 清理同步对象
    if (pfw->hSemaphoreRead) CloseHandle(pfw->hSemaphoreRead);
    if (pfw->hSemaphoreWrite) CloseHandle(pfw->hSemaphoreWrite);
    DeleteCriticalSection(&pfw->csBuffer);
    
    // 关闭文件
    if (pfw->hFile) {
        fclose(pfw->hFile);
    }
    
    // 输出性能统计
    if (pfw->llTotalBytesRead > 0) {
        LARGE_INTEGER liEndTime, liFrequency;
        QueryPerformanceCounter(&liEndTime);
        QueryPerformanceFrequency(&liFrequency);
        
        double dElapsedSeconds = (double)(liEndTime.QuadPart - pfw->liStartTime.QuadPart) 
                               / (double)liFrequency.QuadPart;
        double dThroughputMBps = (double)pfw->llTotalBytesRead / (1024.0 * 1024.0 * dElapsedSeconds);
        
        printf("Optimized Memory Dump Performance Statistics:\n");
        printf("  Total Bytes Read: %lld MB\n", pfw->llTotalBytesRead / (1024 * 1024));
        printf("  Total Bytes Written: %lld MB\n", pfw->llTotalBytesWritten / (1024 * 1024));
        printf("  Elapsed Time: %.2f seconds\n", dElapsedSeconds);
        printf("  Average Throughput: %.2f MB/s\n", dThroughputMBps);
        
        if (pfw->fCompressionEnabled && pfw->llTotalBytesCompressed > 0) {
            double dCompressionRatio = (double)pfw->llTotalBytesCompressed / (double)pfw->llTotalBytesRead;
            printf("  Compression Ratio: %.2f%%\n", dCompressionRatio * 100.0);
        }
    }
    
    LocalFree(pfw);
}

// 优化的内存转储主函数
VOID ActionOptimizedMemoryDump()
{
    QWORD paCurrent, paMin, paMax;
    POPTIMIZED_FILEWRITE pfw = NULL;
    PPAGE_STATISTICS pStat = NULL;
    BOOL fEnableCompression = FALSE;  // 可以通过命令行参数控制
    
    // 初始化地址范围
    paMin = ctxMain->cfg.paAddrMin & ~0xfff;
    paMax = (ctxMain->cfg.paAddrMax + 1) & ~0xfff;
    
    // 初始化优化的文件写入上下文
    pfw = OptimizedMemoryDump_File_Initialize(FALSE, fEnableCompression);
    if (!pfw) return;
    
    // 初始化统计信息
    PageStatInitialize(&pStat, paMin, paMax, "Optimized Memory Dump", FALSE, ctxMain->cfg.fVerbose);
    
    printf("Optimized Memory Dump: Starting with %u reader threads and %u writer threads.\n",
           OPTIMIZED_READER_THREADS, OPTIMIZED_WRITER_THREADS);
    
    // 设置初始读取位置
    paCurrent = paMin;
    PageStatUpdate(pStat, paCurrent, 0, 0);
    
    // 主控制循环 - 分配读取任务
    while (!pfw->fTerminated && (paCurrent < paMax)) {
        // 检查缓冲区可用性
        if ((pfw->iWrite - pfw->iRead) >= OPTIMIZED_NUM_BUFFER) {
            Sleep(10);  // 短暂等待
            continue;
        }
        
        // 设置下一个读取任务
        POPTIMIZED_FILEWRITE_DATA pd = &pfw->Data[pfw->iWrite % OPTIMIZED_NUM_BUFFER];
        pd->pa = paCurrent;
        pd->cb = (DWORD)min(pfw->dwCurrentBufferSize, paMax - paCurrent);
        
        paCurrent += pd->cb;
        
        // 自适应调整缓冲区大小
        if (pfw->fAdaptiveMode) {
            // 根据性能反馈调整缓冲区大小
            // 这里可以添加更复杂的自适应算法
        }
    }
    
    // 等待所有操作完成
    while (pfw->iProcess < pfw->iWrite) {
        Sleep(100);
    }
    
    PageStatClose(&pStat);
    
    if (!pfw->fTerminated) {
        printf("Optimized Memory Dump: Completed successfully.\n");
    }
    
    OptimizedMemoryDump_File_Close(pfw);
}
