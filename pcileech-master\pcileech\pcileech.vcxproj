﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|Win32">
      <Configuration>Debug</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|Win32">
      <Configuration>Release</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <ItemGroup>
    <None Include="Makefile" />
    <None Include="Makefile.macos" />
  </ItemGroup>
  <ItemGroup>
    <ResourceCompile Include="pcileech.rc" />
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="..\includes\dokan.h" />
    <ClInclude Include="..\includes\fileinfo.h" />
    <ClInclude Include="..\includes\leechcore.h" />
    <ClInclude Include="..\includes\public.h" />
    <ClInclude Include="..\includes\vmmdll.h" />
    <ClInclude Include="device.h" />
    <ClInclude Include="executor.h" />
    <ClInclude Include="extra.h" />
    <ClInclude Include="help.h" />
    <ClInclude Include="kmd.h" />
    <ClInclude Include="memdump.h" />
    <ClInclude Include="mempatch.h" />
    <ClInclude Include="ob\ob.h" />
    <ClInclude Include="oscompatibility.h" />
    <ClInclude Include="pcileech.h" />
    <ClInclude Include="shellcode.h" />
    <ClInclude Include="statistics.h" />
    <ClInclude Include="umd.h" />
    <ClInclude Include="util.h" />
    <ClInclude Include="version.h" />
    <ClInclude Include="vfs.h" />
    <ClInclude Include="vmmx.h" />
    <ClInclude Include="ft601_performance_config.h" />
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="charutil.c" />
    <ClCompile Include="device.c" />
    <ClCompile Include="executor.c" />
    <ClCompile Include="extra.c" />
    <ClCompile Include="help.c" />
    <ClCompile Include="kmd.c" />
    <ClCompile Include="memdump.c" />
    <ClCompile Include="mempatch.c" />
    <ClCompile Include="ob\ob_cachemap.c" />
    <ClCompile Include="ob\ob_core.c" />
    <ClCompile Include="ob\ob_map.c" />
    <ClCompile Include="ob\ob_set.c" />
    <ClCompile Include="oscompatibility.c" />
    <ClCompile Include="pcileech.c" />
    <ClCompile Include="statistics.c" />
    <ClCompile Include="umd.c" />
    <ClCompile Include="util.c" />
    <ClCompile Include="vfs.c" />
    <ClCompile Include="vfslist.c" />
    <ClCompile Include="vmmx.c" />
    <ClCompile Include="device_optimized.c" />
    <ClCompile Include="memdump_optimized.c" />
    <ClCompile Include="ft601_performance_config.c" />
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{DFFA1B4C-279B-4356-ADB1-08A6F4795931}</ProjectGuid>
    <RootNamespace>pcileech</RootNamespace>
    <WindowsTargetPlatformVersion>10.0</WindowsTargetPlatformVersion>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <CharacterSet>Unicode</CharacterSet>
    <SpectreMitigation>false</SpectreMitigation>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <CharacterSet>Unicode</CharacterSet>
    <SpectreMitigation>false</SpectreMitigation>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <WholeProgramOptimization>false</WholeProgramOptimization>
    <CharacterSet>Unicode</CharacterSet>
    <SpectreMitigation>false</SpectreMitigation>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <WholeProgramOptimization>false</WholeProgramOptimization>
    <CharacterSet>Unicode</CharacterSet>
    <SpectreMitigation>false</SpectreMitigation>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="Shared">
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <OutDir>$(SolutionDir)\files\</OutDir>
    <IntDir>$(SolutionDir)\files\temp\$(ProjectName)\</IntDir>
    <IncludePath>$(VC_IncludePath);$(WindowsSDK_IncludePath);$(SolutionDir)includes;</IncludePath>
    <LibraryPath>$(VC_LibraryPath_x64);$(WindowsSDK_LibraryPath_x64);$(SolutionDir)includes\lib64;</LibraryPath>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <IncludePath>$(VC_IncludePath);$(WindowsSDK_IncludePath);$(SolutionDir)includes;</IncludePath>
    <LibraryPath>$(VC_LibraryPath_x86);$(WindowsSDK_LibraryPath_x86);$(SolutionDir)includes\lib32;</LibraryPath>
    <OutDir>$(SolutionDir)files\$(PlatformShortName)\</OutDir>
    <IntDir>$(SolutionDir)files\temp\$(ProjectName)\$(PlatformShortName)\</IntDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <OutDir>$(SolutionDir)\files\</OutDir>
    <IntDir>$(SolutionDir)\files\temp\$(ProjectName)\</IntDir>
    <LinkIncremental>
    </LinkIncremental>
    <IncludePath>$(VC_IncludePath);$(WindowsSDK_IncludePath);$(SolutionDir)includes;</IncludePath>
    <LibraryPath>$(VC_LibraryPath_x64);$(WindowsSDK_LibraryPath_x64);$(SolutionDir)includes\lib64;</LibraryPath>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <LinkIncremental />
    <IncludePath>$(VC_IncludePath);$(WindowsSDK_IncludePath);$(SolutionDir)includes;</IncludePath>
    <LibraryPath>$(VC_LibraryPath_x86);$(WindowsSDK_LibraryPath_x86);$(SolutionDir)includes\lib32;</LibraryPath>
    <OutDir>$(SolutionDir)files\$(PlatformShortName)\</OutDir>
    <IntDir>$(SolutionDir)files\temp\$(ProjectName)\$(PlatformShortName)\</IntDir>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <Optimization>Disabled</Optimization>
      <SDLCheck>true</SDLCheck>
      <AdditionalOptions>/D WIN32 %(AdditionalOptions)</AdditionalOptions>
      <CompileAs>CompileAsC</CompileAs>
    </ClCompile>
    <Link>
      <FullProgramDatabaseFile>
      </FullProgramDatabaseFile>
      <GenerateDebugInformation>Debug</GenerateDebugInformation>
      <AdditionalDependencies>leechcore.lib;vmm.lib;%(AdditionalDependencies)</AdditionalDependencies>
      <ProgramDatabaseFile>$(OutDir)\lib\$(TargetName).pdb</ProgramDatabaseFile>
      <SubSystem>Console</SubSystem>
    </Link>
    <PreBuildEvent>
      <Command>
      </Command>
    </PreBuildEvent>
    <PostBuildEvent>
      <Command>
      </Command>
    </PostBuildEvent>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <Optimization>Disabled</Optimization>
      <SDLCheck>true</SDLCheck>
      <AdditionalOptions>/D WIN32 %(AdditionalOptions)</AdditionalOptions>
      <CompileAs>CompileAsC</CompileAs>
    </ClCompile>
    <Link>
      <FullProgramDatabaseFile>
      </FullProgramDatabaseFile>
      <GenerateDebugInformation>Debug</GenerateDebugInformation>
      <AdditionalDependencies>leechcore.lib;vmm.lib;%(AdditionalDependencies)</AdditionalDependencies>
      <ProgramDatabaseFile>$(OutDir)\lib\$(TargetName).pdb</ProgramDatabaseFile>
      <SubSystem>Console</SubSystem>
    </Link>
    <PreBuildEvent>
      <Command>
      </Command>
    </PreBuildEvent>
    <PostBuildEvent>
      <Command>
      </Command>
    </PostBuildEvent>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <Optimization>MaxSpeed</Optimization>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <SDLCheck>
      </SDLCheck>
      <AdditionalOptions>/D WIN32 %(AdditionalOptions)</AdditionalOptions>
      <CompileAs>CompileAsC</CompileAs>
    </ClCompile>
    <Link>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
      <FullProgramDatabaseFile>
      </FullProgramDatabaseFile>
      <GenerateDebugInformation>false</GenerateDebugInformation>
      <ModuleDefinitionFile>
      </ModuleDefinitionFile>
      <SubSystem>Console</SubSystem>
      <ProgramDatabaseFile />
      <AdditionalDependencies>leechcore.lib;vmm.lib;%(AdditionalDependencies)</AdditionalDependencies>
    </Link>
    <PreBuildEvent>
      <Command>
      </Command>
    </PreBuildEvent>
    <PostBuildEvent>
      <Command>
      </Command>
    </PostBuildEvent>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <Optimization>MaxSpeed</Optimization>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <SDLCheck>
      </SDLCheck>
      <AdditionalOptions>/D WIN32 %(AdditionalOptions)</AdditionalOptions>
      <CompileAs>CompileAsC</CompileAs>
    </ClCompile>
    <Link>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
      <FullProgramDatabaseFile>
      </FullProgramDatabaseFile>
      <GenerateDebugInformation>false</GenerateDebugInformation>
      <ModuleDefinitionFile>
      </ModuleDefinitionFile>
      <SubSystem>Console</SubSystem>
      <ProgramDatabaseFile>
      </ProgramDatabaseFile>
      <AdditionalDependencies>leechcore.lib;vmm.lib;%(AdditionalDependencies)</AdditionalDependencies>
    </Link>
    <PreBuildEvent>
      <Command>
      </Command>
    </PreBuildEvent>
    <PostBuildEvent>
      <Command>
      </Command>
    </PostBuildEvent>
  </ItemDefinitionGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>