﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{5C698F13-6E9F-46F3-95FC-55376A65D8BF}</ProjectGuid>
    <RootNamespace>pcileech_shellcode</RootNamespace>
    <WindowsTargetPlatformVersion>10.0</WindowsTargetPlatformVersion>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <WholeProgramOptimization>false</WholeProgramOptimization>
    <CharacterSet>Unicode</CharacterSet>
    <SpectreMitigation>false</SpectreMitigation>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="Shared">
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <OutDir>$(SolutionDir)\files\</OutDir>
    <IntDir>$(SolutionDir)\files\temp\$(ProjectName)\</IntDir>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <Optimization>MaxSpeed</Optimization>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <SDLCheck>true</SDLCheck>
    </ClCompile>
    <Link>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
      <GenerateDebugInformation>No</GenerateDebugInformation>
    </Link>
  </ItemDefinitionGroup>
  <ItemGroup>
    <ClCompile Include="fbsdx64_common.c" />
    <ClCompile Include="fbsdx64_filepull.c" />
    <ClCompile Include="fbsdx64_stage3_c.c" />
    <ClCompile Include="lx64_common.c" />
    <ClCompile Include="lx64_exec_root.c" />
    <ClCompile Include="lx64_filedelete.c" />
    <ClCompile Include="lx64_filepull.c" />
    <ClCompile Include="lx64_filepush.c" />
    <ClCompile Include="lx64_stage3_c.c" />
    <ClCompile Include="lx64_vfs.c" />
    <ClCompile Include="macos_common.c" />
    <ClCompile Include="macos_filepull.c" />
    <ClCompile Include="macos_filepush.c" />
    <ClCompile Include="macos_stage3_c.c" />
    <ClCompile Include="macos_unlock.c" />
    <ClCompile Include="macos_vfs.c" />
    <ClCompile Include="uefi_common.c" />
    <ClCompile Include="uefi_kmd_c.c" />
    <ClCompile Include="uefi_textout.c" />
    <ClCompile Include="uefi_winload_ntos_kmd_c.c" />
    <ClCompile Include="uefi_winload_ntos_patch.c" />
    <ClCompile Include="wx64_common.c" />
    <ClCompile Include="wx64_driverinfo.c" />
    <ClCompile Include="wx64_driverload_svc.c" />
    <ClCompile Include="wx64_driverunload.c" />
    <ClCompile Include="wx64_exec_user_c.c" />
    <ClCompile Include="wx64_filepull.c" />
    <ClCompile Include="wx64_filepush.c" />
    <ClCompile Include="wx64_pagesignature.c" />
    <ClCompile Include="wx64_pscreate.c" />
    <ClCompile Include="wx64_pskill.c" />
    <ClCompile Include="wx64_pslist.c" />
    <ClCompile Include="wx64_stage3_c.c" />
    <ClCompile Include="wx64_umd_exec_c.c" />
    <ClCompile Include="wx64_unlock.c" />
    <ClCompile Include="wx64_vfs.c" />
  </ItemGroup>
  <ItemGroup>
    <None Include="fbsdx64_common_a.asm" />
    <None Include="fbsdx64_stage2.asm" />
    <None Include="fbsdx64_stage3.asm" />
    <None Include="lx64_common_a.asm" />
    <None Include="lx64_stage2.asm" />
    <None Include="lx64_stage2_efi.asm" />
    <None Include="lx64_stage3.asm" />
    <None Include="lx64_stage3_pre.asm" />
    <None Include="macos_common_a.asm" />
    <None Include="macos_stage2.asm" />
    <None Include="macos_stage3.asm" />
    <None Include="uefi_common_a.asm" />
    <None Include="uefi_kmd.asm" />
    <None Include="uefi_winload_ntos_kmd.asm" />
    <None Include="wx64_common_a.asm" />
    <None Include="wx64_exec_user.asm" />
    <None Include="wx64_pageinfo.asm" />
    <None Include="wx64_psblue.asm" />
    <None Include="wx64_stage1.asm" />
    <None Include="wx64_stage2.asm" />
    <None Include="wx64_stage23_vmm.asm" />
    <None Include="wx64_stage23_vmm3.asm" />
    <None Include="wx64_stage2_hal.asm" />
    <None Include="wx64_stage3.asm" />
    <None Include="wx64_stage3_pre.asm" />
    <None Include="wx64_umd_exec.asm" />
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="fbsdx64_common.h" />
    <ClInclude Include="lx64_common.h" />
    <ClInclude Include="macos_common.h" />
    <ClInclude Include="statuscodes.h" />
    <ClInclude Include="uefi_common.h" />
    <ClInclude Include="wx64_common.h" />
  </ItemGroup>
  <ItemGroup>
    <Text Include="info_kmd_core.txt" />
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>