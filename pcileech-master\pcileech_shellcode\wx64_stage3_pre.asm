; wx64_stage3_pre.asm : assembly wait loop to wait for continue when executable code exists after
;
; (c) Ulf <PERSON>isk, 2016
; Author: U<PERSON>, <EMAIL>
;

; -------------------------------------
; Prototypes
; -------------------------------------
main PROTO 

; -----------------------------------------------------------------------------
; Code
; -----------------------------------------------------------------------------
.CODE

main PROC
	label_main_base:
	LEA rax, label_main_base-8h
	MOV rax, [rax]
	C<PERSON> rax, 0
	JZ label_main_base
main ENDP

; -----------------------------------------------------------------------------
; Compiles into:
; 48 8D 05 F1 FF FF FF 48  8B 00 48 83 F8 00 74 F0
; -----------------------------------------------------------------------------

END
