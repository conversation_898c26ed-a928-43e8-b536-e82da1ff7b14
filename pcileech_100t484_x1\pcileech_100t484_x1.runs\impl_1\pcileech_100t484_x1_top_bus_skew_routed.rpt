Copyright 1986-2022 Xilinx, Inc. All Rights Reserved. Copyright 2022-2025 Advanced Micro Devices, Inc. All Rights Reserved.
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
| Tool Version : Vivado v.2024.2.2 (win64) Build 6060944 Thu Mar 06 19:10:01 MST 2025
| Date         : Wed Jun 18 17:38:28 2025
| Host         : DESKTOP-7T6N58Q running 64-bit major release  (build 9200)
| Command      : report_bus_skew -warn_on_violation -file pcileech_100t484_x1_top_bus_skew_routed.rpt -pb pcileech_100t484_x1_top_bus_skew_routed.pb -rpx pcileech_100t484_x1_top_bus_skew_routed.rpx
| Design       : pcileech_100t484_x1_top
| Device       : 7a100t-fgg484
| Speed File   : -2  PRODUCTION 1.23 2018-06-13
| Design State : Routed
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Bus Skew Report

Table of Contents
-----------------
1. Bus Skew Report Summary
2. Bus Skew Report Per Constraint

1. Bus Skew Report Summary
--------------------------

Id  Position  From                            To                              Corner  Requirement(ns)  Actual(ns)  Slack(ns)
--  --------  ------------------------------  ------------------------------  ------  ---------------  ----------  ---------
1   64        [get_cells [list {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_src_fifo/i_fifo_134_134_clk2_rxfifo/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/src_gray_ff_reg[0]} {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_src_fifo/i_fifo_134_134_clk2_rxfifo/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/src_gray_ff_reg[1]} {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_src_fifo/i_fifo_134_134_clk2_rxfifo/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/src_gray_ff_reg[2]} {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_src_fifo/i_fifo_134_134_clk2_rxfifo/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/src_gray_ff_reg[3]} {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_src_fifo/i_fifo_134_134_clk2_rxfifo/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/src_gray_ff_reg[4]} {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_src_fifo/i_fifo_134_134_clk2_rxfifo/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/src_gray_ff_reg[5]} {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_src_fifo/i_fifo_134_134_clk2_rxfifo/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/src_gray_ff_reg[6]} {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_src_fifo/i_fifo_134_134_clk2_rxfifo/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/src_gray_ff_reg[7]} {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_src_fifo/i_fifo_134_134_clk2_rxfifo/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/src_gray_ff_reg[8]} {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_src_fifo/i_fifo_134_134_clk2_rxfifo/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/src_gray_ff_reg[9]}]]
                                              [get_cells [list {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_src_fifo/i_fifo_134_134_clk2_rxfifo/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/dest_graysync_ff_reg[0][0]} {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_src_fifo/i_fifo_134_134_clk2_rxfifo/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/dest_graysync_ff_reg[0][1]} {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_src_fifo/i_fifo_134_134_clk2_rxfifo/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/dest_graysync_ff_reg[0][2]} {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_src_fifo/i_fifo_134_134_clk2_rxfifo/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/dest_graysync_ff_reg[0][3]} {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_src_fifo/i_fifo_134_134_clk2_rxfifo/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/dest_graysync_ff_reg[0][4]} {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_src_fifo/i_fifo_134_134_clk2_rxfifo/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/dest_graysync_ff_reg[0][5]} {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_src_fifo/i_fifo_134_134_clk2_rxfifo/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/dest_graysync_ff_reg[0][6]} {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_src_fifo/i_fifo_134_134_clk2_rxfifo/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/dest_graysync_ff_reg[0][7]} {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_src_fifo/i_fifo_134_134_clk2_rxfifo/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/dest_graysync_ff_reg[0][8]} {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_src_fifo/i_fifo_134_134_clk2_rxfifo/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/dest_graysync_ff_reg[0][9]}]]
                                                                              Slow             10.000       0.821      9.179
2   66        [get_cells [list {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_src_fifo/i_fifo_134_134_clk2_rxfifo/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/src_gray_ff_reg[0]} {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_src_fifo/i_fifo_134_134_clk2_rxfifo/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/src_gray_ff_reg[1]} {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_src_fifo/i_fifo_134_134_clk2_rxfifo/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/src_gray_ff_reg[2]} {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_src_fifo/i_fifo_134_134_clk2_rxfifo/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/src_gray_ff_reg[3]} {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_src_fifo/i_fifo_134_134_clk2_rxfifo/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/src_gray_ff_reg[4]} {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_src_fifo/i_fifo_134_134_clk2_rxfifo/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/src_gray_ff_reg[5]} {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_src_fifo/i_fifo_134_134_clk2_rxfifo/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/src_gray_ff_reg[6]} {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_src_fifo/i_fifo_134_134_clk2_rxfifo/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/src_gray_ff_reg[7]} {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_src_fifo/i_fifo_134_134_clk2_rxfifo/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/src_gray_ff_reg[8]} {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_src_fifo/i_fifo_134_134_clk2_rxfifo/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/src_gray_ff_reg[9]}]]
                                              [get_cells [list {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_src_fifo/i_fifo_134_134_clk2_rxfifo/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/dest_graysync_ff_reg[0][0]} {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_src_fifo/i_fifo_134_134_clk2_rxfifo/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/dest_graysync_ff_reg[0][1]} {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_src_fifo/i_fifo_134_134_clk2_rxfifo/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/dest_graysync_ff_reg[0][2]} {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_src_fifo/i_fifo_134_134_clk2_rxfifo/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/dest_graysync_ff_reg[0][3]} {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_src_fifo/i_fifo_134_134_clk2_rxfifo/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/dest_graysync_ff_reg[0][4]} {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_src_fifo/i_fifo_134_134_clk2_rxfifo/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/dest_graysync_ff_reg[0][5]} {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_src_fifo/i_fifo_134_134_clk2_rxfifo/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/dest_graysync_ff_reg[0][6]} {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_src_fifo/i_fifo_134_134_clk2_rxfifo/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/dest_graysync_ff_reg[0][7]} {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_src_fifo/i_fifo_134_134_clk2_rxfifo/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/dest_graysync_ff_reg[0][8]} {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_src_fifo/i_fifo_134_134_clk2_rxfifo/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/dest_graysync_ff_reg[0][9]}]]
                                                                              Slow             10.000       0.844      9.156
3   68        [get_cells [list {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_dst_fifo/i_fifo_134_134_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/src_gray_ff_reg[0]} \
          {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_dst_fifo/i_fifo_134_134_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/src_gray_ff_reg[10]} \
          {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_dst_fifo/i_fifo_134_134_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/src_gray_ff_reg[1]} \
          {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_dst_fifo/i_fifo_134_134_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/src_gray_ff_reg[2]} \
          {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_dst_fifo/i_fifo_134_134_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/src_gray_ff_reg[3]} \
          {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_dst_fifo/i_fifo_134_134_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/src_gray_ff_reg[4]} \
          {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_dst_fifo/i_fifo_134_134_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/src_gray_ff_reg[5]} \
          {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_dst_fifo/i_fifo_134_134_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/src_gray_ff_reg[6]} \
          {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_dst_fifo/i_fifo_134_134_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/src_gray_ff_reg[7]} \
          {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_dst_fifo/i_fifo_134_134_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/src_gray_ff_reg[8]} \
          {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_dst_fifo/i_fifo_134_134_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/src_gray_ff_reg[9]}]]
                                              [get_cells [list {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_dst_fifo/i_fifo_134_134_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/dest_graysync_ff_reg[0][0]} \
          {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_dst_fifo/i_fifo_134_134_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/dest_graysync_ff_reg[0][10]} \
          {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_dst_fifo/i_fifo_134_134_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/dest_graysync_ff_reg[0][1]} \
          {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_dst_fifo/i_fifo_134_134_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/dest_graysync_ff_reg[0][2]} \
          {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_dst_fifo/i_fifo_134_134_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/dest_graysync_ff_reg[0][3]} \
          {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_dst_fifo/i_fifo_134_134_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/dest_graysync_ff_reg[0][4]} \
          {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_dst_fifo/i_fifo_134_134_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/dest_graysync_ff_reg[0][5]} \
          {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_dst_fifo/i_fifo_134_134_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/dest_graysync_ff_reg[0][6]} \
          {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_dst_fifo/i_fifo_134_134_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/dest_graysync_ff_reg[0][7]} \
          {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_dst_fifo/i_fifo_134_134_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/dest_graysync_ff_reg[0][8]} \
          {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_dst_fifo/i_fifo_134_134_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/dest_graysync_ff_reg[0][9]}]]
                                                                              Slow             10.000       0.606      9.394
4   70        [get_cells [list {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_dst_fifo/i_fifo_134_134_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/src_gray_ff_reg[0]} \
          {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_dst_fifo/i_fifo_134_134_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/src_gray_ff_reg[10]} \
          {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_dst_fifo/i_fifo_134_134_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/src_gray_ff_reg[1]} \
          {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_dst_fifo/i_fifo_134_134_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/src_gray_ff_reg[2]} \
          {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_dst_fifo/i_fifo_134_134_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/src_gray_ff_reg[3]} \
          {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_dst_fifo/i_fifo_134_134_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/src_gray_ff_reg[4]} \
          {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_dst_fifo/i_fifo_134_134_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/src_gray_ff_reg[5]} \
          {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_dst_fifo/i_fifo_134_134_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/src_gray_ff_reg[6]} \
          {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_dst_fifo/i_fifo_134_134_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/src_gray_ff_reg[7]} \
          {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_dst_fifo/i_fifo_134_134_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/src_gray_ff_reg[8]} \
          {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_dst_fifo/i_fifo_134_134_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/src_gray_ff_reg[9]}]]
                                              [get_cells [list {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_dst_fifo/i_fifo_134_134_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/dest_graysync_ff_reg[0][0]} \
          {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_dst_fifo/i_fifo_134_134_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/dest_graysync_ff_reg[0][10]} \
          {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_dst_fifo/i_fifo_134_134_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/dest_graysync_ff_reg[0][1]} \
          {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_dst_fifo/i_fifo_134_134_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/dest_graysync_ff_reg[0][2]} \
          {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_dst_fifo/i_fifo_134_134_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/dest_graysync_ff_reg[0][3]} \
          {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_dst_fifo/i_fifo_134_134_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/dest_graysync_ff_reg[0][4]} \
          {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_dst_fifo/i_fifo_134_134_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/dest_graysync_ff_reg[0][5]} \
          {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_dst_fifo/i_fifo_134_134_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/dest_graysync_ff_reg[0][6]} \
          {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_dst_fifo/i_fifo_134_134_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/dest_graysync_ff_reg[0][7]} \
          {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_dst_fifo/i_fifo_134_134_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/dest_graysync_ff_reg[0][8]} \
          {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_dst_fifo/i_fifo_134_134_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/dest_graysync_ff_reg[0][9]}]]
                                                                              Slow             10.000       0.683      9.317
5   72        [get_cells [list {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_src_fifo/i_fifo_1_1_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/src_gray_ff_reg[0]} {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_src_fifo/i_fifo_1_1_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/src_gray_ff_reg[1]} {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_src_fifo/i_fifo_1_1_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/src_gray_ff_reg[2]} {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_src_fifo/i_fifo_1_1_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/src_gray_ff_reg[3]}]]
                                              [get_cells [list {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_src_fifo/i_fifo_1_1_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/dest_graysync_ff_reg[0][0]} {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_src_fifo/i_fifo_1_1_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/dest_graysync_ff_reg[0][1]} {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_src_fifo/i_fifo_1_1_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/dest_graysync_ff_reg[0][2]} {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_src_fifo/i_fifo_1_1_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/dest_graysync_ff_reg[0][3]}]]
                                                                              Slow             10.000       0.516      9.484
6   74        [get_cells [list {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_src_fifo/i_fifo_1_1_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/src_gray_ff_reg[0]} {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_src_fifo/i_fifo_1_1_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/src_gray_ff_reg[1]} {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_src_fifo/i_fifo_1_1_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/src_gray_ff_reg[2]} {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_src_fifo/i_fifo_1_1_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/src_gray_ff_reg[3]}]]
                                              [get_cells [list {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_src_fifo/i_fifo_1_1_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/dest_graysync_ff_reg[0][0]} {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_src_fifo/i_fifo_1_1_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/dest_graysync_ff_reg[0][1]} {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_src_fifo/i_fifo_1_1_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/dest_graysync_ff_reg[0][2]} {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_src_fifo/i_fifo_1_1_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/dest_graysync_ff_reg[0][3]}]]
                                                                              Slow             10.000       0.680      9.320
7   76        [get_cells [list {i_pcileech_com/i_fifo_256_32_clk2_comtx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/src_gray_ff_reg[0]} \
          {i_pcileech_com/i_fifo_256_32_clk2_comtx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/src_gray_ff_reg[10]} \
          {i_pcileech_com/i_fifo_256_32_clk2_comtx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/src_gray_ff_reg[11]} \
          {i_pcileech_com/i_fifo_256_32_clk2_comtx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/src_gray_ff_reg[12]} \
          {i_pcileech_com/i_fifo_256_32_clk2_comtx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/src_gray_ff_reg[1]} \
          {i_pcileech_com/i_fifo_256_32_clk2_comtx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/src_gray_ff_reg[2]} \
          {i_pcileech_com/i_fifo_256_32_clk2_comtx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/src_gray_ff_reg[3]} \
          {i_pcileech_com/i_fifo_256_32_clk2_comtx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/src_gray_ff_reg[4]} \
          {i_pcileech_com/i_fifo_256_32_clk2_comtx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/src_gray_ff_reg[5]} \
          {i_pcileech_com/i_fifo_256_32_clk2_comtx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/src_gray_ff_reg[6]} \
          {i_pcileech_com/i_fifo_256_32_clk2_comtx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/src_gray_ff_reg[7]} \
          {i_pcileech_com/i_fifo_256_32_clk2_comtx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/src_gray_ff_reg[8]} \
          {i_pcileech_com/i_fifo_256_32_clk2_comtx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/src_gray_ff_reg[9]}]]
                                              [get_cells [list {i_pcileech_com/i_fifo_256_32_clk2_comtx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/dest_graysync_ff_reg[0][0]} \
          {i_pcileech_com/i_fifo_256_32_clk2_comtx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/dest_graysync_ff_reg[0][10]} \
          {i_pcileech_com/i_fifo_256_32_clk2_comtx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/dest_graysync_ff_reg[0][11]} \
          {i_pcileech_com/i_fifo_256_32_clk2_comtx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/dest_graysync_ff_reg[0][12]} \
          {i_pcileech_com/i_fifo_256_32_clk2_comtx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/dest_graysync_ff_reg[0][1]} \
          {i_pcileech_com/i_fifo_256_32_clk2_comtx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/dest_graysync_ff_reg[0][2]} \
          {i_pcileech_com/i_fifo_256_32_clk2_comtx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/dest_graysync_ff_reg[0][3]} \
          {i_pcileech_com/i_fifo_256_32_clk2_comtx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/dest_graysync_ff_reg[0][4]} \
          {i_pcileech_com/i_fifo_256_32_clk2_comtx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/dest_graysync_ff_reg[0][5]} \
          {i_pcileech_com/i_fifo_256_32_clk2_comtx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/dest_graysync_ff_reg[0][6]} \
          {i_pcileech_com/i_fifo_256_32_clk2_comtx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/dest_graysync_ff_reg[0][7]} \
          {i_pcileech_com/i_fifo_256_32_clk2_comtx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/dest_graysync_ff_reg[0][8]} \
          {i_pcileech_com/i_fifo_256_32_clk2_comtx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/dest_graysync_ff_reg[0][9]}]]
                                                                              Slow             10.000       0.553      9.447
8   78        [get_cells [list {i_pcileech_com/i_fifo_256_32_clk2_comtx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/src_gray_ff_reg[9]} \
          {i_pcileech_com/i_fifo_256_32_clk2_comtx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/src_gray_ff_reg[10]} \
          {i_pcileech_com/i_fifo_256_32_clk2_comtx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/src_gray_ff_reg[11]} \
          {i_pcileech_com/i_fifo_256_32_clk2_comtx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/src_gray_ff_reg[12]} \
          {i_pcileech_com/i_fifo_256_32_clk2_comtx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/src_gray_ff_reg[13]} \
          {i_pcileech_com/i_fifo_256_32_clk2_comtx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/src_gray_ff_reg[14]} \
          {i_pcileech_com/i_fifo_256_32_clk2_comtx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/src_gray_ff_reg[15]} \
          {i_pcileech_com/i_fifo_256_32_clk2_comtx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/src_gray_ff_reg[8]} \
          {i_pcileech_com/i_fifo_256_32_clk2_comtx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/src_gray_ff_reg[7]} \
          {i_pcileech_com/i_fifo_256_32_clk2_comtx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/src_gray_ff_reg[3]} \
          {i_pcileech_com/i_fifo_256_32_clk2_comtx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/src_gray_ff_reg[4]} \
          {i_pcileech_com/i_fifo_256_32_clk2_comtx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/src_gray_ff_reg[5]} \
          {i_pcileech_com/i_fifo_256_32_clk2_comtx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/src_gray_ff_reg[6]}]]
                                              [get_cells [list {i_pcileech_com/i_fifo_256_32_clk2_comtx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/dest_graysync_ff_reg[0][9]} \
          {i_pcileech_com/i_fifo_256_32_clk2_comtx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/dest_graysync_ff_reg[0][10]} \
          {i_pcileech_com/i_fifo_256_32_clk2_comtx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/dest_graysync_ff_reg[0][11]} \
          {i_pcileech_com/i_fifo_256_32_clk2_comtx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/dest_graysync_ff_reg[0][12]} \
          {i_pcileech_com/i_fifo_256_32_clk2_comtx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/dest_graysync_ff_reg[0][13]} \
          {i_pcileech_com/i_fifo_256_32_clk2_comtx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/dest_graysync_ff_reg[0][14]} \
          {i_pcileech_com/i_fifo_256_32_clk2_comtx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/dest_graysync_ff_reg[0][15]} \
          {i_pcileech_com/i_fifo_256_32_clk2_comtx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/dest_graysync_ff_reg[0][8]} \
          {i_pcileech_com/i_fifo_256_32_clk2_comtx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/dest_graysync_ff_reg[0][7]} \
          {i_pcileech_com/i_fifo_256_32_clk2_comtx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/dest_graysync_ff_reg[0][3]} \
          {i_pcileech_com/i_fifo_256_32_clk2_comtx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/dest_graysync_ff_reg[0][4]} \
          {i_pcileech_com/i_fifo_256_32_clk2_comtx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/dest_graysync_ff_reg[0][5]} \
          {i_pcileech_com/i_fifo_256_32_clk2_comtx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/dest_graysync_ff_reg[0][6]}]]
                                                                              Slow             10.000       0.612      9.388
9   80        [get_cells [list {i_pcileech_pcie_a7/i_pcileech_pcie_cfg_a7/i_fifo_pcie_cfg_rx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/src_gray_ff_reg[0]} {i_pcileech_pcie_a7/i_pcileech_pcie_cfg_a7/i_fifo_pcie_cfg_rx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/src_gray_ff_reg[1]} {i_pcileech_pcie_a7/i_pcileech_pcie_cfg_a7/i_fifo_pcie_cfg_rx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/src_gray_ff_reg[2]} {i_pcileech_pcie_a7/i_pcileech_pcie_cfg_a7/i_fifo_pcie_cfg_rx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/src_gray_ff_reg[3]} {i_pcileech_pcie_a7/i_pcileech_pcie_cfg_a7/i_fifo_pcie_cfg_rx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/src_gray_ff_reg[4]} {i_pcileech_pcie_a7/i_pcileech_pcie_cfg_a7/i_fifo_pcie_cfg_rx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/src_gray_ff_reg[5]} {i_pcileech_pcie_a7/i_pcileech_pcie_cfg_a7/i_fifo_pcie_cfg_rx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/src_gray_ff_reg[6]} {i_pcileech_pcie_a7/i_pcileech_pcie_cfg_a7/i_fifo_pcie_cfg_rx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/src_gray_ff_reg[7]} {i_pcileech_pcie_a7/i_pcileech_pcie_cfg_a7/i_fifo_pcie_cfg_rx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/src_gray_ff_reg[8]}]]
                                              [get_cells [list {i_pcileech_pcie_a7/i_pcileech_pcie_cfg_a7/i_fifo_pcie_cfg_rx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/dest_graysync_ff_reg[0][0]} {i_pcileech_pcie_a7/i_pcileech_pcie_cfg_a7/i_fifo_pcie_cfg_rx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/dest_graysync_ff_reg[0][1]} {i_pcileech_pcie_a7/i_pcileech_pcie_cfg_a7/i_fifo_pcie_cfg_rx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/dest_graysync_ff_reg[0][2]} {i_pcileech_pcie_a7/i_pcileech_pcie_cfg_a7/i_fifo_pcie_cfg_rx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/dest_graysync_ff_reg[0][3]} {i_pcileech_pcie_a7/i_pcileech_pcie_cfg_a7/i_fifo_pcie_cfg_rx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/dest_graysync_ff_reg[0][4]} {i_pcileech_pcie_a7/i_pcileech_pcie_cfg_a7/i_fifo_pcie_cfg_rx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/dest_graysync_ff_reg[0][5]} {i_pcileech_pcie_a7/i_pcileech_pcie_cfg_a7/i_fifo_pcie_cfg_rx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/dest_graysync_ff_reg[0][6]} {i_pcileech_pcie_a7/i_pcileech_pcie_cfg_a7/i_fifo_pcie_cfg_rx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/dest_graysync_ff_reg[0][7]} {i_pcileech_pcie_a7/i_pcileech_pcie_cfg_a7/i_fifo_pcie_cfg_rx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/dest_graysync_ff_reg[0][8]}]]
                                                                              Slow             10.000       0.625      9.375
10  82        [get_cells [list {i_pcileech_pcie_a7/i_pcileech_pcie_cfg_a7/i_fifo_pcie_cfg_rx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/src_gray_ff_reg[0]} {i_pcileech_pcie_a7/i_pcileech_pcie_cfg_a7/i_fifo_pcie_cfg_rx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/src_gray_ff_reg[1]} {i_pcileech_pcie_a7/i_pcileech_pcie_cfg_a7/i_fifo_pcie_cfg_rx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/src_gray_ff_reg[2]} {i_pcileech_pcie_a7/i_pcileech_pcie_cfg_a7/i_fifo_pcie_cfg_rx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/src_gray_ff_reg[3]} {i_pcileech_pcie_a7/i_pcileech_pcie_cfg_a7/i_fifo_pcie_cfg_rx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/src_gray_ff_reg[4]} {i_pcileech_pcie_a7/i_pcileech_pcie_cfg_a7/i_fifo_pcie_cfg_rx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/src_gray_ff_reg[5]} {i_pcileech_pcie_a7/i_pcileech_pcie_cfg_a7/i_fifo_pcie_cfg_rx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/src_gray_ff_reg[6]} {i_pcileech_pcie_a7/i_pcileech_pcie_cfg_a7/i_fifo_pcie_cfg_rx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/src_gray_ff_reg[7]} {i_pcileech_pcie_a7/i_pcileech_pcie_cfg_a7/i_fifo_pcie_cfg_rx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/src_gray_ff_reg[8]}]]
                                              [get_cells [list {i_pcileech_pcie_a7/i_pcileech_pcie_cfg_a7/i_fifo_pcie_cfg_rx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/dest_graysync_ff_reg[0][0]} {i_pcileech_pcie_a7/i_pcileech_pcie_cfg_a7/i_fifo_pcie_cfg_rx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/dest_graysync_ff_reg[0][1]} {i_pcileech_pcie_a7/i_pcileech_pcie_cfg_a7/i_fifo_pcie_cfg_rx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/dest_graysync_ff_reg[0][2]} {i_pcileech_pcie_a7/i_pcileech_pcie_cfg_a7/i_fifo_pcie_cfg_rx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/dest_graysync_ff_reg[0][3]} {i_pcileech_pcie_a7/i_pcileech_pcie_cfg_a7/i_fifo_pcie_cfg_rx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/dest_graysync_ff_reg[0][4]} {i_pcileech_pcie_a7/i_pcileech_pcie_cfg_a7/i_fifo_pcie_cfg_rx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/dest_graysync_ff_reg[0][5]} {i_pcileech_pcie_a7/i_pcileech_pcie_cfg_a7/i_fifo_pcie_cfg_rx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/dest_graysync_ff_reg[0][6]} {i_pcileech_pcie_a7/i_pcileech_pcie_cfg_a7/i_fifo_pcie_cfg_rx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/dest_graysync_ff_reg[0][7]} {i_pcileech_pcie_a7/i_pcileech_pcie_cfg_a7/i_fifo_pcie_cfg_rx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/dest_graysync_ff_reg[0][8]}]]
                                                                              Slow             10.000       0.752      9.248
11  84        [get_cells [list {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_cfgspace_shadow/i_fifo_43_43_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/src_gray_ff_reg[0]} {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_cfgspace_shadow/i_fifo_43_43_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/src_gray_ff_reg[1]} {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_cfgspace_shadow/i_fifo_43_43_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/src_gray_ff_reg[2]} {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_cfgspace_shadow/i_fifo_43_43_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/src_gray_ff_reg[3]}]]
                                              [get_cells [list {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_cfgspace_shadow/i_fifo_43_43_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/dest_graysync_ff_reg[0][0]} {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_cfgspace_shadow/i_fifo_43_43_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/dest_graysync_ff_reg[0][1]} {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_cfgspace_shadow/i_fifo_43_43_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/dest_graysync_ff_reg[0][2]} {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_cfgspace_shadow/i_fifo_43_43_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/dest_graysync_ff_reg[0][3]}]]
                                                                              Slow             10.000       0.648      9.352
12  86        [get_cells [list {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_cfgspace_shadow/i_fifo_43_43_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/src_gray_ff_reg[0]} {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_cfgspace_shadow/i_fifo_43_43_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/src_gray_ff_reg[1]} {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_cfgspace_shadow/i_fifo_43_43_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/src_gray_ff_reg[2]} {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_cfgspace_shadow/i_fifo_43_43_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/src_gray_ff_reg[3]}]]
                                              [get_cells [list {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_cfgspace_shadow/i_fifo_43_43_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/dest_graysync_ff_reg[0][0]} {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_cfgspace_shadow/i_fifo_43_43_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/dest_graysync_ff_reg[0][1]} {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_cfgspace_shadow/i_fifo_43_43_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/dest_graysync_ff_reg[0][2]} {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_cfgspace_shadow/i_fifo_43_43_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/dest_graysync_ff_reg[0][3]}]]
                                                                              Slow             10.000       0.703      9.297
13  88        [get_cells [list {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_cfgspace_shadow/i_fifo_49_49_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/src_gray_ff_reg[0]} {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_cfgspace_shadow/i_fifo_49_49_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/src_gray_ff_reg[1]} {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_cfgspace_shadow/i_fifo_49_49_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/src_gray_ff_reg[2]} {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_cfgspace_shadow/i_fifo_49_49_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/src_gray_ff_reg[3]}]]
                                              [get_cells [list {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_cfgspace_shadow/i_fifo_49_49_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/dest_graysync_ff_reg[0][0]} {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_cfgspace_shadow/i_fifo_49_49_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/dest_graysync_ff_reg[0][1]} {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_cfgspace_shadow/i_fifo_49_49_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/dest_graysync_ff_reg[0][2]} {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_cfgspace_shadow/i_fifo_49_49_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/dest_graysync_ff_reg[0][3]}]]
                                                                              Slow             10.000       0.512      9.488
14  90        [get_cells [list {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_cfgspace_shadow/i_fifo_49_49_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/src_gray_ff_reg[0]} {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_cfgspace_shadow/i_fifo_49_49_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/src_gray_ff_reg[1]} {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_cfgspace_shadow/i_fifo_49_49_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/src_gray_ff_reg[2]} {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_cfgspace_shadow/i_fifo_49_49_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/src_gray_ff_reg[3]}]]
                                              [get_cells [list {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_cfgspace_shadow/i_fifo_49_49_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/dest_graysync_ff_reg[0][0]} {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_cfgspace_shadow/i_fifo_49_49_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/dest_graysync_ff_reg[0][1]} {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_cfgspace_shadow/i_fifo_49_49_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/dest_graysync_ff_reg[0][2]} {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_cfgspace_shadow/i_fifo_49_49_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/dest_graysync_ff_reg[0][3]}]]
                                                                              Slow             10.000       0.665      9.335
15  92        [get_cells [list {i_pcileech_com/i_fifo_64_64_clk2_comrx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/src_gray_ff_reg[0]} {i_pcileech_com/i_fifo_64_64_clk2_comrx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/src_gray_ff_reg[1]} {i_pcileech_com/i_fifo_64_64_clk2_comrx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/src_gray_ff_reg[2]} {i_pcileech_com/i_fifo_64_64_clk2_comrx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/src_gray_ff_reg[3]}]]
                                              [get_cells [list {i_pcileech_com/i_fifo_64_64_clk2_comrx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/dest_graysync_ff_reg[0][0]} {i_pcileech_com/i_fifo_64_64_clk2_comrx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/dest_graysync_ff_reg[0][1]} {i_pcileech_com/i_fifo_64_64_clk2_comrx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/dest_graysync_ff_reg[0][2]} {i_pcileech_com/i_fifo_64_64_clk2_comrx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/dest_graysync_ff_reg[0][3]}]]
                                                                              Slow             10.000       0.715      9.285
16  94        [get_cells [list {i_pcileech_com/i_fifo_64_64_clk2_comrx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/src_gray_ff_reg[0]} {i_pcileech_com/i_fifo_64_64_clk2_comrx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/src_gray_ff_reg[1]} {i_pcileech_com/i_fifo_64_64_clk2_comrx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/src_gray_ff_reg[2]} {i_pcileech_com/i_fifo_64_64_clk2_comrx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/src_gray_ff_reg[3]}]]
                                              [get_cells [list {i_pcileech_com/i_fifo_64_64_clk2_comrx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/dest_graysync_ff_reg[0][0]} {i_pcileech_com/i_fifo_64_64_clk2_comrx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/dest_graysync_ff_reg[0][1]} {i_pcileech_com/i_fifo_64_64_clk2_comrx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/dest_graysync_ff_reg[0][2]} {i_pcileech_com/i_fifo_64_64_clk2_comrx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/dest_graysync_ff_reg[0][3]}]]
                                                                              Slow             10.000       0.651      9.349
17  96        [get_cells [list {i_pcileech_pcie_a7/i_pcileech_pcie_cfg_a7/i_fifo_pcie_cfg_tx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/src_gray_ff_reg[0]} {i_pcileech_pcie_a7/i_pcileech_pcie_cfg_a7/i_fifo_pcie_cfg_tx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/src_gray_ff_reg[1]} {i_pcileech_pcie_a7/i_pcileech_pcie_cfg_a7/i_fifo_pcie_cfg_tx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/src_gray_ff_reg[2]} {i_pcileech_pcie_a7/i_pcileech_pcie_cfg_a7/i_fifo_pcie_cfg_tx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/src_gray_ff_reg[3]} {i_pcileech_pcie_a7/i_pcileech_pcie_cfg_a7/i_fifo_pcie_cfg_tx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/src_gray_ff_reg[4]} {i_pcileech_pcie_a7/i_pcileech_pcie_cfg_a7/i_fifo_pcie_cfg_tx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/src_gray_ff_reg[5]} {i_pcileech_pcie_a7/i_pcileech_pcie_cfg_a7/i_fifo_pcie_cfg_tx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/src_gray_ff_reg[6]} {i_pcileech_pcie_a7/i_pcileech_pcie_cfg_a7/i_fifo_pcie_cfg_tx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/src_gray_ff_reg[7]}]]
                                              [get_cells [list {i_pcileech_pcie_a7/i_pcileech_pcie_cfg_a7/i_fifo_pcie_cfg_tx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/dest_graysync_ff_reg[0][0]} {i_pcileech_pcie_a7/i_pcileech_pcie_cfg_a7/i_fifo_pcie_cfg_tx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/dest_graysync_ff_reg[0][1]} {i_pcileech_pcie_a7/i_pcileech_pcie_cfg_a7/i_fifo_pcie_cfg_tx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/dest_graysync_ff_reg[0][2]} {i_pcileech_pcie_a7/i_pcileech_pcie_cfg_a7/i_fifo_pcie_cfg_tx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/dest_graysync_ff_reg[0][3]} {i_pcileech_pcie_a7/i_pcileech_pcie_cfg_a7/i_fifo_pcie_cfg_tx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/dest_graysync_ff_reg[0][4]} {i_pcileech_pcie_a7/i_pcileech_pcie_cfg_a7/i_fifo_pcie_cfg_tx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/dest_graysync_ff_reg[0][5]} {i_pcileech_pcie_a7/i_pcileech_pcie_cfg_a7/i_fifo_pcie_cfg_tx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/dest_graysync_ff_reg[0][6]} {i_pcileech_pcie_a7/i_pcileech_pcie_cfg_a7/i_fifo_pcie_cfg_tx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/dest_graysync_ff_reg[0][7]}]]
                                                                              Slow             10.000       0.990      9.010
18  98        [get_cells [list {i_pcileech_pcie_a7/i_pcileech_pcie_cfg_a7/i_fifo_pcie_cfg_tx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/src_gray_ff_reg[0]} {i_pcileech_pcie_a7/i_pcileech_pcie_cfg_a7/i_fifo_pcie_cfg_tx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/src_gray_ff_reg[1]} {i_pcileech_pcie_a7/i_pcileech_pcie_cfg_a7/i_fifo_pcie_cfg_tx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/src_gray_ff_reg[2]} {i_pcileech_pcie_a7/i_pcileech_pcie_cfg_a7/i_fifo_pcie_cfg_tx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/src_gray_ff_reg[3]} {i_pcileech_pcie_a7/i_pcileech_pcie_cfg_a7/i_fifo_pcie_cfg_tx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/src_gray_ff_reg[4]} {i_pcileech_pcie_a7/i_pcileech_pcie_cfg_a7/i_fifo_pcie_cfg_tx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/src_gray_ff_reg[5]} {i_pcileech_pcie_a7/i_pcileech_pcie_cfg_a7/i_fifo_pcie_cfg_tx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/src_gray_ff_reg[6]} {i_pcileech_pcie_a7/i_pcileech_pcie_cfg_a7/i_fifo_pcie_cfg_tx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/src_gray_ff_reg[7]}]]
                                              [get_cells [list {i_pcileech_pcie_a7/i_pcileech_pcie_cfg_a7/i_fifo_pcie_cfg_tx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/dest_graysync_ff_reg[0][0]} {i_pcileech_pcie_a7/i_pcileech_pcie_cfg_a7/i_fifo_pcie_cfg_tx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/dest_graysync_ff_reg[0][1]} {i_pcileech_pcie_a7/i_pcileech_pcie_cfg_a7/i_fifo_pcie_cfg_tx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/dest_graysync_ff_reg[0][2]} {i_pcileech_pcie_a7/i_pcileech_pcie_cfg_a7/i_fifo_pcie_cfg_tx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/dest_graysync_ff_reg[0][3]} {i_pcileech_pcie_a7/i_pcileech_pcie_cfg_a7/i_fifo_pcie_cfg_tx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/dest_graysync_ff_reg[0][4]} {i_pcileech_pcie_a7/i_pcileech_pcie_cfg_a7/i_fifo_pcie_cfg_tx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/dest_graysync_ff_reg[0][5]} {i_pcileech_pcie_a7/i_pcileech_pcie_cfg_a7/i_fifo_pcie_cfg_tx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/dest_graysync_ff_reg[0][6]} {i_pcileech_pcie_a7/i_pcileech_pcie_cfg_a7/i_fifo_pcie_cfg_tx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/dest_graysync_ff_reg[0][7]}]]
                                                                              Slow             10.000       0.705      9.295


2. Bus Skew Report Per Constraint
---------------------------------

Id: 1
set_bus_skew -from [get_cells [list {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_src_fifo/i_fifo_134_134_clk2_rxfifo/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/src_gray_ff_reg[0]} {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_src_fifo/i_fifo_134_134_clk2_rxfifo/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/src_gray_ff_reg[1]} {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_src_fifo/i_fifo_134_134_clk2_rxfifo/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/src_gray_ff_reg[2]} {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_src_fifo/i_fifo_134_134_clk2_rxfifo/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/src_gray_ff_reg[3]} {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_src_fifo/i_fifo_134_134_clk2_rxfifo/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/src_gray_ff_reg[4]} {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_src_fifo/i_fifo_134_134_clk2_rxfifo/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/src_gray_ff_reg[5]} {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_src_fifo/i_fifo_134_134_clk2_rxfifo/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/src_gray_ff_reg[6]} {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_src_fifo/i_fifo_134_134_clk2_rxfifo/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/src_gray_ff_reg[7]} {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_src_fifo/i_fifo_134_134_clk2_rxfifo/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/src_gray_ff_reg[8]} {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_src_fifo/i_fifo_134_134_clk2_rxfifo/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/src_gray_ff_reg[9]}]] -to [get_cells [list {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_src_fifo/i_fifo_134_134_clk2_rxfifo/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/dest_graysync_ff_reg[0][0]} {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_src_fifo/i_fifo_134_134_clk2_rxfifo/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/dest_graysync_ff_reg[0][1]} {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_src_fifo/i_fifo_134_134_clk2_rxfifo/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/dest_graysync_ff_reg[0][2]} {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_src_fifo/i_fifo_134_134_clk2_rxfifo/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/dest_graysync_ff_reg[0][3]} {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_src_fifo/i_fifo_134_134_clk2_rxfifo/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/dest_graysync_ff_reg[0][4]} {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_src_fifo/i_fifo_134_134_clk2_rxfifo/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/dest_graysync_ff_reg[0][5]} {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_src_fifo/i_fifo_134_134_clk2_rxfifo/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/dest_graysync_ff_reg[0][6]} {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_src_fifo/i_fifo_134_134_clk2_rxfifo/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/dest_graysync_ff_reg[0][7]} {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_src_fifo/i_fifo_134_134_clk2_rxfifo/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/dest_graysync_ff_reg[0][8]} {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_src_fifo/i_fifo_134_134_clk2_rxfifo/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/dest_graysync_ff_reg[0][9]}]] 10.000
Requirement: 10.000ns
Endpoints: 10

From Clock            To Clock              Endpoint Pin                    Reference Pin                   Corner  Actual(ns)  Slack(ns)
--------------------  --------------------  ------------------------------  ------------------------------  ------  ----------  ---------
userclk1              net_clk               i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_src_fifo/i_fifo_134_134_clk2_rxfifo/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/dest_graysync_ff_reg[0][9]/D
                                                                            i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_src_fifo/i_fifo_134_134_clk2_rxfifo/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/dest_graysync_ff_reg[0][0]/D
                                                                                                            Slow         0.821      9.179


Slack (MET) :             9.179ns  (requirement - actual skew)
  Endpoint Source:        <hidden>
                            (rising edge-triggered cell FDRE clocked by userclk1)
  Endpoint Destination:   <hidden>
                            (rising edge-triggered cell FDRE clocked by net_clk)
  Reference Source:       <hidden>
                            (rising edge-triggered cell FDRE clocked by userclk1)
  Reference Destination:  <hidden>
                            (rising edge-triggered cell FDRE clocked by net_clk)
  Path Type:              Bus Skew (Max at Slow Process Corner)
  Requirement:            10.000ns
  Endpoint Relative Delay:    2.531ns
  Reference Relative Delay:   1.177ns
  Relative CRPR:              0.533ns
  Actual Bus Skew:            0.821ns  (Endpoint Relative Delay - Reference Relative Delay - Relative CRPR)

Endpoint path:
    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock userclk1 rise edge)
                                                      0.000     0.000 r  
    GTPE2_CHANNEL_X0Y7   GTPE2_CHANNEL                0.000     0.000 r  i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_lane[0].gt_wrapper_i/gtp_channel.gtpe2_channel_i/TXOUTCLK
                         net (fo=1, routed)           1.257     1.257    i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/txoutclk_i.txoutclk_i_0
    BUFGCTRL_X0Y19       BUFG (Prop_bufg_I_O)         0.081     1.338 r  i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/txoutclk_i.txoutclk_i/O
                         net (fo=1, routed)           1.408     2.745    i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/refclk
    MMCME2_ADV_X0Y1      MMCME2_ADV (Prop_mmcme2_adv_CLKIN1_CLKOUT2)
                                                      0.077     2.822 r  i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/mmcm_i/CLKOUT2
                         net (fo=1, routed)           1.441     4.263    i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/userclk1
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.081     4.344 r  i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/userclk1_i1.usrclk1_i1/O
                         net (fo=3403, routed)        1.369     5.713    <hidden>
    SLICE_X33Y146        FDRE                                         r  <hidden>
  -------------------------------------------------------------------    -------------------
    SLICE_X33Y146        FDRE (Prop_fdre_C_Q)         0.348     6.061 r  <hidden>
                         net (fo=1, routed)           0.565     6.627    <hidden>
    SLICE_X32Y146        FDRE                                         r  <hidden>
  -------------------------------------------------------------------    -------------------

                         (clock net_clk rise edge)    0.000     0.000 r  
    J19                                               0.000     0.000 r  clk (IN)
                         net (fo=0)                   0.000     0.000    clk
    J19                  IBUF (Prop_ibuf_I_O)         1.350     1.350 r  clk_IBUF_inst/O
                         net (fo=1, routed)           1.620     2.970    clk_IBUF
    BUFGCTRL_X0Y16       BUFG (Prop_bufg_I_O)         0.077     3.047 r  clk_IBUF_BUFG_inst/O
                         net (fo=1816, routed)        1.256     4.303    <hidden>
    SLICE_X32Y146        FDRE                                         r  <hidden>
                         clock pessimism              0.000     4.303    
    SLICE_X32Y146        FDRE (Setup_fdre_C_D)       -0.207     4.096    <hidden>
  -------------------------------------------------------------------
                         data arrival                           6.627    
                         clock arrival                          4.096    
  -------------------------------------------------------------------
                         relative delay                         2.531    

Reference path:
    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock userclk1 rise edge)
                                                      0.000     0.000 r  
    GTPE2_CHANNEL_X0Y7   GTPE2_CHANNEL                0.000     0.000 r  i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_lane[0].gt_wrapper_i/gtp_channel.gtpe2_channel_i/TXOUTCLK
                         net (fo=1, routed)           1.199     1.199    i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/txoutclk_i.txoutclk_i_0
    BUFGCTRL_X0Y19       BUFG (Prop_bufg_I_O)         0.077     1.276 r  i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/txoutclk_i.txoutclk_i/O
                         net (fo=1, routed)           1.302     2.578    i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/refclk
    MMCME2_ADV_X0Y1      MMCME2_ADV (Prop_mmcme2_adv_CLKIN1_CLKOUT2)
                                                      0.073     2.651 r  i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/mmcm_i/CLKOUT2
                         net (fo=1, routed)           1.374     4.024    i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/userclk1
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.077     4.102 r  i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/userclk1_i1.usrclk1_i1/O
                         net (fo=3403, routed)        1.259     5.361    <hidden>
    SLICE_X29Y144        FDRE                                         r  <hidden>
  -------------------------------------------------------------------    -------------------
    SLICE_X29Y144        FDRE (Prop_fdre_C_Q)         0.304     5.665 r  <hidden>
                         net (fo=1, routed)           0.222     5.887    <hidden>
    SLICE_X28Y144        FDRE                                         r  <hidden>
  -------------------------------------------------------------------    -------------------

                         (clock net_clk rise edge)    0.000     0.000 r  
    J19                                               0.000     0.000 r  clk (IN)
                         net (fo=0)                   0.000     0.000    clk
    J19                  IBUF (Prop_ibuf_I_O)         1.416     1.416 r  clk_IBUF_inst/O
                         net (fo=1, routed)           1.709     3.124    clk_IBUF
    BUFGCTRL_X0Y16       BUFG (Prop_bufg_I_O)         0.081     3.205 r  clk_IBUF_BUFG_inst/O
                         net (fo=1816, routed)        1.368     4.574    <hidden>
    SLICE_X28Y144        FDRE                                         r  <hidden>
                         clock pessimism              0.000     4.574    
    SLICE_X28Y144        FDRE (Hold_fdre_C_D)         0.136     4.710    <hidden>
  -------------------------------------------------------------------
                         data arrival                           5.887    
                         clock arrival                          4.710    
  -------------------------------------------------------------------
                         relative delay                         1.177    



Id: 2
set_bus_skew -from [get_cells [list {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_src_fifo/i_fifo_134_134_clk2_rxfifo/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/src_gray_ff_reg[0]} {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_src_fifo/i_fifo_134_134_clk2_rxfifo/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/src_gray_ff_reg[1]} {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_src_fifo/i_fifo_134_134_clk2_rxfifo/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/src_gray_ff_reg[2]} {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_src_fifo/i_fifo_134_134_clk2_rxfifo/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/src_gray_ff_reg[3]} {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_src_fifo/i_fifo_134_134_clk2_rxfifo/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/src_gray_ff_reg[4]} {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_src_fifo/i_fifo_134_134_clk2_rxfifo/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/src_gray_ff_reg[5]} {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_src_fifo/i_fifo_134_134_clk2_rxfifo/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/src_gray_ff_reg[6]} {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_src_fifo/i_fifo_134_134_clk2_rxfifo/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/src_gray_ff_reg[7]} {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_src_fifo/i_fifo_134_134_clk2_rxfifo/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/src_gray_ff_reg[8]} {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_src_fifo/i_fifo_134_134_clk2_rxfifo/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/src_gray_ff_reg[9]}]] -to [get_cells [list {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_src_fifo/i_fifo_134_134_clk2_rxfifo/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/dest_graysync_ff_reg[0][0]} {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_src_fifo/i_fifo_134_134_clk2_rxfifo/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/dest_graysync_ff_reg[0][1]} {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_src_fifo/i_fifo_134_134_clk2_rxfifo/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/dest_graysync_ff_reg[0][2]} {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_src_fifo/i_fifo_134_134_clk2_rxfifo/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/dest_graysync_ff_reg[0][3]} {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_src_fifo/i_fifo_134_134_clk2_rxfifo/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/dest_graysync_ff_reg[0][4]} {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_src_fifo/i_fifo_134_134_clk2_rxfifo/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/dest_graysync_ff_reg[0][5]} {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_src_fifo/i_fifo_134_134_clk2_rxfifo/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/dest_graysync_ff_reg[0][6]} {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_src_fifo/i_fifo_134_134_clk2_rxfifo/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/dest_graysync_ff_reg[0][7]} {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_src_fifo/i_fifo_134_134_clk2_rxfifo/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/dest_graysync_ff_reg[0][8]} {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_src_fifo/i_fifo_134_134_clk2_rxfifo/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/dest_graysync_ff_reg[0][9]}]] 10.000
Requirement: 10.000ns
Endpoints: 10

From Clock            To Clock              Endpoint Pin                    Reference Pin                   Corner  Actual(ns)  Slack(ns)
--------------------  --------------------  ------------------------------  ------------------------------  ------  ----------  ---------
net_clk               userclk1              i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_src_fifo/i_fifo_134_134_clk2_rxfifo/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/dest_graysync_ff_reg[0][5]/D
                                                                            i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_src_fifo/i_fifo_134_134_clk2_rxfifo/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/dest_graysync_ff_reg[0][6]/D
                                                                                                            Slow         0.844      9.156


Slack (MET) :             9.156ns  (requirement - actual skew)
  Endpoint Source:        <hidden>
                            (rising edge-triggered cell FDRE clocked by net_clk)
  Endpoint Destination:   <hidden>
                            (rising edge-triggered cell FDRE clocked by userclk1)
  Reference Source:       <hidden>
                            (rising edge-triggered cell FDRE clocked by net_clk)
  Reference Destination:  <hidden>
                            (rising edge-triggered cell FDRE clocked by userclk1)
  Path Type:              Bus Skew (Max at Slow Process Corner)
  Requirement:            10.000ns
  Endpoint Relative Delay:    0.379ns
  Reference Relative Delay:  -1.043ns
  Relative CRPR:              0.578ns
  Actual Bus Skew:            0.844ns  (Endpoint Relative Delay - Reference Relative Delay - Relative CRPR)

Endpoint path:
    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock net_clk rise edge)    0.000     0.000 r  
    J19                                               0.000     0.000 r  clk (IN)
                         net (fo=0)                   0.000     0.000    clk
    J19                  IBUF (Prop_ibuf_I_O)         1.416     1.416 r  clk_IBUF_inst/O
                         net (fo=1, routed)           1.709     3.124    clk_IBUF
    BUFGCTRL_X0Y16       BUFG (Prop_bufg_I_O)         0.081     3.205 r  clk_IBUF_BUFG_inst/O
                         net (fo=1816, routed)        1.366     4.572    <hidden>
    SLICE_X31Y144        FDRE                                         r  <hidden>
  -------------------------------------------------------------------    -------------------
    SLICE_X31Y144        FDRE (Prop_fdre_C_Q)         0.348     4.920 r  <hidden>
                         net (fo=1, routed)           0.612     5.532    <hidden>
    SLICE_X31Y142        FDRE                                         r  <hidden>
  -------------------------------------------------------------------    -------------------

                         (clock userclk1 rise edge)
                                                      0.000     0.000 r  
    GTPE2_CHANNEL_X0Y7   GTPE2_CHANNEL                0.000     0.000 r  i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_lane[0].gt_wrapper_i/gtp_channel.gtpe2_channel_i/TXOUTCLK
                         net (fo=1, routed)           1.199     1.199    i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/txoutclk_i.txoutclk_i_0
    BUFGCTRL_X0Y19       BUFG (Prop_bufg_I_O)         0.077     1.276 r  i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/txoutclk_i.txoutclk_i/O
                         net (fo=1, routed)           1.302     2.578    i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/refclk
    MMCME2_ADV_X0Y1      MMCME2_ADV (Prop_mmcme2_adv_CLKIN1_CLKOUT2)
                                                      0.073     2.651 r  i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/mmcm_i/CLKOUT2
                         net (fo=1, routed)           1.374     4.024    i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/userclk1
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.077     4.102 r  i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/userclk1_i1.usrclk1_i1/O
                         net (fo=3403, routed)        1.258     5.360    <hidden>
    SLICE_X31Y142        FDRE                                         r  <hidden>
                         clock pessimism              0.000     5.360    
    SLICE_X31Y142        FDRE (Setup_fdre_C_D)       -0.207     5.153    <hidden>
  -------------------------------------------------------------------
                         data arrival                           5.532    
                         clock arrival                          5.153    
  -------------------------------------------------------------------
                         relative delay                         0.379    

Reference path:
    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock net_clk rise edge)    0.000     0.000 r  
    J19                                               0.000     0.000 r  clk (IN)
                         net (fo=0)                   0.000     0.000    clk
    J19                  IBUF (Prop_ibuf_I_O)         1.350     1.350 r  clk_IBUF_inst/O
                         net (fo=1, routed)           1.620     2.970    clk_IBUF
    BUFGCTRL_X0Y16       BUFG (Prop_bufg_I_O)         0.077     3.047 r  clk_IBUF_BUFG_inst/O
                         net (fo=1816, routed)        1.256     4.303    <hidden>
    SLICE_X31Y144        FDRE                                         r  <hidden>
  -------------------------------------------------------------------    -------------------
    SLICE_X31Y144        FDRE (Prop_fdre_C_Q)         0.304     4.607 r  <hidden>
                         net (fo=1, routed)           0.204     4.810    <hidden>
    SLICE_X29Y144        FDRE                                         r  <hidden>
  -------------------------------------------------------------------    -------------------

                         (clock userclk1 rise edge)
                                                      0.000     0.000 r  
    GTPE2_CHANNEL_X0Y7   GTPE2_CHANNEL                0.000     0.000 r  i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_lane[0].gt_wrapper_i/gtp_channel.gtpe2_channel_i/TXOUTCLK
                         net (fo=1, routed)           1.257     1.257    i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/txoutclk_i.txoutclk_i_0
    BUFGCTRL_X0Y19       BUFG (Prop_bufg_I_O)         0.081     1.338 r  i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/txoutclk_i.txoutclk_i/O
                         net (fo=1, routed)           1.408     2.745    i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/refclk
    MMCME2_ADV_X0Y1      MMCME2_ADV (Prop_mmcme2_adv_CLKIN1_CLKOUT2)
                                                      0.077     2.822 r  i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/mmcm_i/CLKOUT2
                         net (fo=1, routed)           1.441     4.263    i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/userclk1
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.081     4.344 r  i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/userclk1_i1.usrclk1_i1/O
                         net (fo=3403, routed)        1.371     5.715    <hidden>
    SLICE_X29Y144        FDRE                                         r  <hidden>
                         clock pessimism              0.000     5.715    
    SLICE_X29Y144        FDRE (Hold_fdre_C_D)         0.138     5.853    <hidden>
  -------------------------------------------------------------------
                         data arrival                           4.810    
                         clock arrival                          5.853    
  -------------------------------------------------------------------
                         relative delay                        -1.043    



Id: 3
set_bus_skew -from [get_cells [list {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_dst_fifo/i_fifo_134_134_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/src_gray_ff_reg[0]} \
          {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_dst_fifo/i_fifo_134_134_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/src_gray_ff_reg[10]} \
          {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_dst_fifo/i_fifo_134_134_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/src_gray_ff_reg[1]} \
          {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_dst_fifo/i_fifo_134_134_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/src_gray_ff_reg[2]} \
          {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_dst_fifo/i_fifo_134_134_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/src_gray_ff_reg[3]} \
          {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_dst_fifo/i_fifo_134_134_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/src_gray_ff_reg[4]} \
          {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_dst_fifo/i_fifo_134_134_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/src_gray_ff_reg[5]} \
          {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_dst_fifo/i_fifo_134_134_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/src_gray_ff_reg[6]} \
          {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_dst_fifo/i_fifo_134_134_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/src_gray_ff_reg[7]} \
          {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_dst_fifo/i_fifo_134_134_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/src_gray_ff_reg[8]} \
          {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_dst_fifo/i_fifo_134_134_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/src_gray_ff_reg[9]}]] -to [get_cells [list {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_dst_fifo/i_fifo_134_134_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/dest_graysync_ff_reg[0][0]} \
          {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_dst_fifo/i_fifo_134_134_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/dest_graysync_ff_reg[0][10]} \
          {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_dst_fifo/i_fifo_134_134_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/dest_graysync_ff_reg[0][1]} \
          {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_dst_fifo/i_fifo_134_134_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/dest_graysync_ff_reg[0][2]} \
          {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_dst_fifo/i_fifo_134_134_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/dest_graysync_ff_reg[0][3]} \
          {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_dst_fifo/i_fifo_134_134_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/dest_graysync_ff_reg[0][4]} \
          {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_dst_fifo/i_fifo_134_134_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/dest_graysync_ff_reg[0][5]} \
          {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_dst_fifo/i_fifo_134_134_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/dest_graysync_ff_reg[0][6]} \
          {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_dst_fifo/i_fifo_134_134_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/dest_graysync_ff_reg[0][7]} \
          {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_dst_fifo/i_fifo_134_134_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/dest_graysync_ff_reg[0][8]} \
          {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_dst_fifo/i_fifo_134_134_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/dest_graysync_ff_reg[0][9]}]] 10.000
Requirement: 10.000ns
Endpoints: 11

From Clock            To Clock              Endpoint Pin                    Reference Pin                   Corner  Actual(ns)  Slack(ns)
--------------------  --------------------  ------------------------------  ------------------------------  ------  ----------  ---------
net_clk               userclk1              i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_dst_fifo/i_fifo_134_134_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/dest_graysync_ff_reg[0][1]/D
                                                                            i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_dst_fifo/i_fifo_134_134_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/dest_graysync_ff_reg[0][8]/D
                                                                                                            Slow         0.606      9.394


Slack (MET) :             9.394ns  (requirement - actual skew)
  Endpoint Source:        <hidden>
                            (rising edge-triggered cell FDRE clocked by net_clk)
  Endpoint Destination:   <hidden>
                            (rising edge-triggered cell FDRE clocked by userclk1)
  Reference Source:       <hidden>
                            (rising edge-triggered cell FDRE clocked by net_clk)
  Reference Destination:  <hidden>
                            (rising edge-triggered cell FDRE clocked by userclk1)
  Path Type:              Bus Skew (Max at Slow Process Corner)
  Requirement:            10.000ns
  Endpoint Relative Delay:    0.226ns
  Reference Relative Delay:  -0.912ns
  Relative CRPR:              0.533ns
  Actual Bus Skew:            0.606ns  (Endpoint Relative Delay - Reference Relative Delay - Relative CRPR)

Endpoint path:
    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock net_clk rise edge)    0.000     0.000 r  
    J19                                               0.000     0.000 r  clk (IN)
                         net (fo=0)                   0.000     0.000    clk
    J19                  IBUF (Prop_ibuf_I_O)         1.416     1.416 r  clk_IBUF_inst/O
                         net (fo=1, routed)           1.709     3.124    clk_IBUF
    BUFGCTRL_X0Y16       BUFG (Prop_bufg_I_O)         0.081     3.205 r  clk_IBUF_BUFG_inst/O
                         net (fo=1816, routed)        1.348     4.554    <hidden>
    SLICE_X41Y121        FDRE                                         r  <hidden>
  -------------------------------------------------------------------    -------------------
    SLICE_X41Y121        FDRE (Prop_fdre_C_Q)         0.348     4.902 r  <hidden>
                         net (fo=1, routed)           0.494     5.396    <hidden>
    SLICE_X46Y121        FDRE                                         r  <hidden>
  -------------------------------------------------------------------    -------------------

                         (clock userclk1 rise edge)
                                                      0.000     0.000 r  
    GTPE2_CHANNEL_X0Y7   GTPE2_CHANNEL                0.000     0.000 r  i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_lane[0].gt_wrapper_i/gtp_channel.gtpe2_channel_i/TXOUTCLK
                         net (fo=1, routed)           1.199     1.199    i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/txoutclk_i.txoutclk_i_0
    BUFGCTRL_X0Y19       BUFG (Prop_bufg_I_O)         0.077     1.276 r  i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/txoutclk_i.txoutclk_i/O
                         net (fo=1, routed)           1.302     2.578    i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/refclk
    MMCME2_ADV_X0Y1      MMCME2_ADV (Prop_mmcme2_adv_CLKIN1_CLKOUT2)
                                                      0.073     2.651 r  i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/mmcm_i/CLKOUT2
                         net (fo=1, routed)           1.374     4.024    i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/userclk1
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.077     4.102 r  i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/userclk1_i1.usrclk1_i1/O
                         net (fo=3403, routed)        1.238     5.340    <hidden>
    SLICE_X46Y121        FDRE                                         r  <hidden>
                         clock pessimism              0.000     5.340    
    SLICE_X46Y121        FDRE (Setup_fdre_C_D)       -0.170     5.170    <hidden>
  -------------------------------------------------------------------
                         data arrival                           5.396    
                         clock arrival                          5.170    
  -------------------------------------------------------------------
                         relative delay                         0.226    

Reference path:
    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock net_clk rise edge)    0.000     0.000 r  
    J19                                               0.000     0.000 r  clk (IN)
                         net (fo=0)                   0.000     0.000    clk
    J19                  IBUF (Prop_ibuf_I_O)         1.350     1.350 r  clk_IBUF_inst/O
                         net (fo=1, routed)           1.620     2.970    clk_IBUF
    BUFGCTRL_X0Y16       BUFG (Prop_bufg_I_O)         0.077     3.047 r  clk_IBUF_BUFG_inst/O
                         net (fo=1816, routed)        1.238     4.285    <hidden>
    SLICE_X42Y123        FDRE                                         r  <hidden>
  -------------------------------------------------------------------    -------------------
    SLICE_X42Y123        FDRE (Prop_fdre_C_Q)         0.347     4.632 r  <hidden>
                         net (fo=1, routed)           0.287     4.919    <hidden>
    SLICE_X44Y122        FDRE                                         r  <hidden>
  -------------------------------------------------------------------    -------------------

                         (clock userclk1 rise edge)
                                                      0.000     0.000 r  
    GTPE2_CHANNEL_X0Y7   GTPE2_CHANNEL                0.000     0.000 r  i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_lane[0].gt_wrapper_i/gtp_channel.gtpe2_channel_i/TXOUTCLK
                         net (fo=1, routed)           1.257     1.257    i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/txoutclk_i.txoutclk_i_0
    BUFGCTRL_X0Y19       BUFG (Prop_bufg_I_O)         0.081     1.338 r  i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/txoutclk_i.txoutclk_i/O
                         net (fo=1, routed)           1.408     2.745    i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/refclk
    MMCME2_ADV_X0Y1      MMCME2_ADV (Prop_mmcme2_adv_CLKIN1_CLKOUT2)
                                                      0.077     2.822 r  i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/mmcm_i/CLKOUT2
                         net (fo=1, routed)           1.441     4.263    i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/userclk1
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.081     4.344 r  i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/userclk1_i1.usrclk1_i1/O
                         net (fo=3403, routed)        1.349     5.693    <hidden>
    SLICE_X44Y122        FDRE                                         r  <hidden>
                         clock pessimism              0.000     5.693    
    SLICE_X44Y122        FDRE (Hold_fdre_C_D)         0.138     5.831    <hidden>
  -------------------------------------------------------------------
                         data arrival                           4.919    
                         clock arrival                          5.831    
  -------------------------------------------------------------------
                         relative delay                        -0.912    



Id: 4
set_bus_skew -from [get_cells [list {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_dst_fifo/i_fifo_134_134_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/src_gray_ff_reg[0]} \
          {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_dst_fifo/i_fifo_134_134_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/src_gray_ff_reg[10]} \
          {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_dst_fifo/i_fifo_134_134_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/src_gray_ff_reg[1]} \
          {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_dst_fifo/i_fifo_134_134_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/src_gray_ff_reg[2]} \
          {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_dst_fifo/i_fifo_134_134_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/src_gray_ff_reg[3]} \
          {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_dst_fifo/i_fifo_134_134_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/src_gray_ff_reg[4]} \
          {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_dst_fifo/i_fifo_134_134_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/src_gray_ff_reg[5]} \
          {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_dst_fifo/i_fifo_134_134_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/src_gray_ff_reg[6]} \
          {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_dst_fifo/i_fifo_134_134_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/src_gray_ff_reg[7]} \
          {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_dst_fifo/i_fifo_134_134_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/src_gray_ff_reg[8]} \
          {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_dst_fifo/i_fifo_134_134_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/src_gray_ff_reg[9]}]] -to [get_cells [list {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_dst_fifo/i_fifo_134_134_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/dest_graysync_ff_reg[0][0]} \
          {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_dst_fifo/i_fifo_134_134_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/dest_graysync_ff_reg[0][10]} \
          {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_dst_fifo/i_fifo_134_134_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/dest_graysync_ff_reg[0][1]} \
          {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_dst_fifo/i_fifo_134_134_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/dest_graysync_ff_reg[0][2]} \
          {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_dst_fifo/i_fifo_134_134_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/dest_graysync_ff_reg[0][3]} \
          {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_dst_fifo/i_fifo_134_134_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/dest_graysync_ff_reg[0][4]} \
          {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_dst_fifo/i_fifo_134_134_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/dest_graysync_ff_reg[0][5]} \
          {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_dst_fifo/i_fifo_134_134_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/dest_graysync_ff_reg[0][6]} \
          {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_dst_fifo/i_fifo_134_134_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/dest_graysync_ff_reg[0][7]} \
          {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_dst_fifo/i_fifo_134_134_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/dest_graysync_ff_reg[0][8]} \
          {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_dst_fifo/i_fifo_134_134_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/dest_graysync_ff_reg[0][9]}]] 10.000
Requirement: 10.000ns
Endpoints: 11

From Clock            To Clock              Endpoint Pin                    Reference Pin                   Corner  Actual(ns)  Slack(ns)
--------------------  --------------------  ------------------------------  ------------------------------  ------  ----------  ---------
userclk1              net_clk               i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_dst_fifo/i_fifo_134_134_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/dest_graysync_ff_reg[0][7]/D
                                                                            i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_dst_fifo/i_fifo_134_134_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/dest_graysync_ff_reg[0][2]/D
                                                                                                            Slow         0.683      9.317


Slack (MET) :             9.317ns  (requirement - actual skew)
  Endpoint Source:        <hidden>
                            (rising edge-triggered cell FDRE clocked by userclk1)
  Endpoint Destination:   <hidden>
                            (rising edge-triggered cell FDRE clocked by net_clk)
  Reference Source:       <hidden>
                            (rising edge-triggered cell FDRE clocked by userclk1)
  Reference Destination:  <hidden>
                            (rising edge-triggered cell FDRE clocked by net_clk)
  Path Type:              Bus Skew (Max at Slow Process Corner)
  Requirement:            10.000ns
  Endpoint Relative Delay:    2.344ns
  Reference Relative Delay:   1.128ns
  Relative CRPR:              0.533ns
  Actual Bus Skew:            0.683ns  (Endpoint Relative Delay - Reference Relative Delay - Relative CRPR)

Endpoint path:
    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock userclk1 rise edge)
                                                      0.000     0.000 r  
    GTPE2_CHANNEL_X0Y7   GTPE2_CHANNEL                0.000     0.000 r  i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_lane[0].gt_wrapper_i/gtp_channel.gtpe2_channel_i/TXOUTCLK
                         net (fo=1, routed)           1.257     1.257    i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/txoutclk_i.txoutclk_i_0
    BUFGCTRL_X0Y19       BUFG (Prop_bufg_I_O)         0.081     1.338 r  i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/txoutclk_i.txoutclk_i/O
                         net (fo=1, routed)           1.408     2.745    i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/refclk
    MMCME2_ADV_X0Y1      MMCME2_ADV (Prop_mmcme2_adv_CLKIN1_CLKOUT2)
                                                      0.077     2.822 r  i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/mmcm_i/CLKOUT2
                         net (fo=1, routed)           1.441     4.263    i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/userclk1
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.081     4.344 r  i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/userclk1_i1.usrclk1_i1/O
                         net (fo=3403, routed)        1.350     5.694    <hidden>
    SLICE_X43Y121        FDRE                                         r  <hidden>
  -------------------------------------------------------------------    -------------------
    SLICE_X43Y121        FDRE (Prop_fdre_C_Q)         0.348     6.042 r  <hidden>
                         net (fo=1, routed)           0.381     6.424    <hidden>
    SLICE_X43Y122        FDRE                                         r  <hidden>
  -------------------------------------------------------------------    -------------------

                         (clock net_clk rise edge)    0.000     0.000 r  
    J19                                               0.000     0.000 r  clk (IN)
                         net (fo=0)                   0.000     0.000    clk
    J19                  IBUF (Prop_ibuf_I_O)         1.350     1.350 r  clk_IBUF_inst/O
                         net (fo=1, routed)           1.620     2.970    clk_IBUF
    BUFGCTRL_X0Y16       BUFG (Prop_bufg_I_O)         0.077     3.047 r  clk_IBUF_BUFG_inst/O
                         net (fo=1816, routed)        1.240     4.287    <hidden>
    SLICE_X43Y122        FDRE                                         r  <hidden>
                         clock pessimism              0.000     4.287    
    SLICE_X43Y122        FDRE (Setup_fdre_C_D)       -0.207     4.080    <hidden>
  -------------------------------------------------------------------
                         data arrival                           6.424    
                         clock arrival                          4.080    
  -------------------------------------------------------------------
                         relative delay                         2.344    

Reference path:
    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock userclk1 rise edge)
                                                      0.000     0.000 r  
    GTPE2_CHANNEL_X0Y7   GTPE2_CHANNEL                0.000     0.000 r  i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_lane[0].gt_wrapper_i/gtp_channel.gtpe2_channel_i/TXOUTCLK
                         net (fo=1, routed)           1.199     1.199    i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/txoutclk_i.txoutclk_i_0
    BUFGCTRL_X0Y19       BUFG (Prop_bufg_I_O)         0.077     1.276 r  i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/txoutclk_i.txoutclk_i/O
                         net (fo=1, routed)           1.302     2.578    i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/refclk
    MMCME2_ADV_X0Y1      MMCME2_ADV (Prop_mmcme2_adv_CLKIN1_CLKOUT2)
                                                      0.073     2.651 r  i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/mmcm_i/CLKOUT2
                         net (fo=1, routed)           1.374     4.024    i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/userclk1
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.077     4.102 r  i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/userclk1_i1.usrclk1_i1/O
                         net (fo=3403, routed)        1.245     5.347    <hidden>
    SLICE_X37Y121        FDRE                                         r  <hidden>
  -------------------------------------------------------------------    -------------------
    SLICE_X37Y121        FDRE (Prop_fdre_C_Q)         0.304     5.651 r  <hidden>
                         net (fo=1, routed)           0.218     5.869    <hidden>
    SLICE_X38Y121        FDRE                                         r  <hidden>
  -------------------------------------------------------------------    -------------------

                         (clock net_clk rise edge)    0.000     0.000 r  
    J19                                               0.000     0.000 r  clk (IN)
                         net (fo=0)                   0.000     0.000    clk
    J19                  IBUF (Prop_ibuf_I_O)         1.416     1.416 r  clk_IBUF_inst/O
                         net (fo=1, routed)           1.709     3.124    clk_IBUF
    BUFGCTRL_X0Y16       BUFG (Prop_bufg_I_O)         0.081     3.205 r  clk_IBUF_BUFG_inst/O
                         net (fo=1816, routed)        1.348     4.554    <hidden>
    SLICE_X38Y121        FDRE                                         r  <hidden>
                         clock pessimism              0.000     4.554    
    SLICE_X38Y121        FDRE (Hold_fdre_C_D)         0.187     4.741    <hidden>
  -------------------------------------------------------------------
                         data arrival                           5.869    
                         clock arrival                          4.741    
  -------------------------------------------------------------------
                         relative delay                         1.128    



Id: 5
set_bus_skew -from [get_cells [list {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_src_fifo/i_fifo_1_1_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/src_gray_ff_reg[0]} {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_src_fifo/i_fifo_1_1_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/src_gray_ff_reg[1]} {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_src_fifo/i_fifo_1_1_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/src_gray_ff_reg[2]} {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_src_fifo/i_fifo_1_1_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/src_gray_ff_reg[3]}]] -to [get_cells [list {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_src_fifo/i_fifo_1_1_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/dest_graysync_ff_reg[0][0]} {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_src_fifo/i_fifo_1_1_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/dest_graysync_ff_reg[0][1]} {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_src_fifo/i_fifo_1_1_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/dest_graysync_ff_reg[0][2]} {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_src_fifo/i_fifo_1_1_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/dest_graysync_ff_reg[0][3]}]] 10.000
Requirement: 10.000ns
Endpoints: 4

From Clock            To Clock              Endpoint Pin                    Reference Pin                   Corner  Actual(ns)  Slack(ns)
--------------------  --------------------  ------------------------------  ------------------------------  ------  ----------  ---------
userclk1              net_clk               i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_src_fifo/i_fifo_1_1_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/dest_graysync_ff_reg[0][3]/D
                                                                            i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_src_fifo/i_fifo_1_1_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/dest_graysync_ff_reg[0][0]/D
                                                                                                            Slow         0.516      9.484


Slack (MET) :             9.484ns  (requirement - actual skew)
  Endpoint Source:        <hidden>
                            (rising edge-triggered cell FDRE clocked by userclk1)
  Endpoint Destination:   <hidden>
                            (rising edge-triggered cell FDRE clocked by net_clk)
  Reference Source:       <hidden>
                            (rising edge-triggered cell FDRE clocked by userclk1)
  Reference Destination:  <hidden>
                            (rising edge-triggered cell FDRE clocked by net_clk)
  Path Type:              Bus Skew (Max at Slow Process Corner)
  Requirement:            10.000ns
  Endpoint Relative Delay:    2.404ns
  Reference Relative Delay:   1.294ns
  Relative CRPR:              0.594ns
  Actual Bus Skew:            0.516ns  (Endpoint Relative Delay - Reference Relative Delay - Relative CRPR)

Endpoint path:
    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock userclk1 rise edge)
                                                      0.000     0.000 r  
    GTPE2_CHANNEL_X0Y7   GTPE2_CHANNEL                0.000     0.000 r  i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_lane[0].gt_wrapper_i/gtp_channel.gtpe2_channel_i/TXOUTCLK
                         net (fo=1, routed)           1.257     1.257    i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/txoutclk_i.txoutclk_i_0
    BUFGCTRL_X0Y19       BUFG (Prop_bufg_I_O)         0.081     1.338 r  i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/txoutclk_i.txoutclk_i/O
                         net (fo=1, routed)           1.408     2.745    i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/refclk
    MMCME2_ADV_X0Y1      MMCME2_ADV (Prop_mmcme2_adv_CLKIN1_CLKOUT2)
                                                      0.077     2.822 r  i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/mmcm_i/CLKOUT2
                         net (fo=1, routed)           1.441     4.263    i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/userclk1
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.081     4.344 r  i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/userclk1_i1.usrclk1_i1/O
                         net (fo=3403, routed)        1.538     5.882    <hidden>
    SLICE_X14Y152        FDRE                                         r  <hidden>
  -------------------------------------------------------------------    -------------------
    SLICE_X14Y152        FDRE (Prop_fdre_C_Q)         0.398     6.280 r  <hidden>
                         net (fo=1, routed)           0.384     6.665    <hidden>
    SLICE_X15Y151        FDRE                                         r  <hidden>
  -------------------------------------------------------------------    -------------------

                         (clock net_clk rise edge)    0.000     0.000 r  
    J19                                               0.000     0.000 r  clk (IN)
                         net (fo=0)                   0.000     0.000    clk
    J19                  IBUF (Prop_ibuf_I_O)         1.350     1.350 r  clk_IBUF_inst/O
                         net (fo=1, routed)           1.620     2.970    clk_IBUF
    BUFGCTRL_X0Y16       BUFG (Prop_bufg_I_O)         0.077     3.047 r  clk_IBUF_BUFG_inst/O
                         net (fo=1816, routed)        1.417     4.464    <hidden>
    SLICE_X15Y151        FDRE                                         r  <hidden>
                         clock pessimism              0.000     4.464    
    SLICE_X15Y151        FDRE (Setup_fdre_C_D)       -0.203     4.261    <hidden>
  -------------------------------------------------------------------
                         data arrival                           6.665    
                         clock arrival                          4.261    
  -------------------------------------------------------------------
                         relative delay                         2.404    

Reference path:
    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock userclk1 rise edge)
                                                      0.000     0.000 r  
    GTPE2_CHANNEL_X0Y7   GTPE2_CHANNEL                0.000     0.000 r  i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_lane[0].gt_wrapper_i/gtp_channel.gtpe2_channel_i/TXOUTCLK
                         net (fo=1, routed)           1.199     1.199    i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/txoutclk_i.txoutclk_i_0
    BUFGCTRL_X0Y19       BUFG (Prop_bufg_I_O)         0.077     1.276 r  i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/txoutclk_i.txoutclk_i/O
                         net (fo=1, routed)           1.302     2.578    i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/refclk
    MMCME2_ADV_X0Y1      MMCME2_ADV (Prop_mmcme2_adv_CLKIN1_CLKOUT2)
                                                      0.073     2.651 r  i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/mmcm_i/CLKOUT2
                         net (fo=1, routed)           1.374     4.024    i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/userclk1
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.077     4.102 r  i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/userclk1_i1.usrclk1_i1/O
                         net (fo=3403, routed)        1.419     5.520    <hidden>
    SLICE_X14Y152        FDRE                                         r  <hidden>
  -------------------------------------------------------------------    -------------------
    SLICE_X14Y152        FDRE (Prop_fdre_C_Q)         0.347     5.867 r  <hidden>
                         net (fo=1, routed)           0.303     6.170    <hidden>
    SLICE_X17Y151        FDRE                                         r  <hidden>
  -------------------------------------------------------------------    -------------------

                         (clock net_clk rise edge)    0.000     0.000 r  
    J19                                               0.000     0.000 r  clk (IN)
                         net (fo=0)                   0.000     0.000    clk
    J19                  IBUF (Prop_ibuf_I_O)         1.416     1.416 r  clk_IBUF_inst/O
                         net (fo=1, routed)           1.709     3.124    clk_IBUF
    BUFGCTRL_X0Y16       BUFG (Prop_bufg_I_O)         0.081     3.205 r  clk_IBUF_BUFG_inst/O
                         net (fo=1816, routed)        1.535     4.741    <hidden>
    SLICE_X17Y151        FDRE                                         r  <hidden>
                         clock pessimism              0.000     4.741    
    SLICE_X17Y151        FDRE (Hold_fdre_C_D)         0.136     4.877    <hidden>
  -------------------------------------------------------------------
                         data arrival                           6.170    
                         clock arrival                          4.877    
  -------------------------------------------------------------------
                         relative delay                         1.294    



Id: 6
set_bus_skew -from [get_cells [list {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_src_fifo/i_fifo_1_1_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/src_gray_ff_reg[0]} {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_src_fifo/i_fifo_1_1_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/src_gray_ff_reg[1]} {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_src_fifo/i_fifo_1_1_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/src_gray_ff_reg[2]} {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_src_fifo/i_fifo_1_1_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/src_gray_ff_reg[3]}]] -to [get_cells [list {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_src_fifo/i_fifo_1_1_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/dest_graysync_ff_reg[0][0]} {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_src_fifo/i_fifo_1_1_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/dest_graysync_ff_reg[0][1]} {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_src_fifo/i_fifo_1_1_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/dest_graysync_ff_reg[0][2]} {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_src_fifo/i_fifo_1_1_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/dest_graysync_ff_reg[0][3]}]] 10.000
Requirement: 10.000ns
Endpoints: 4

From Clock            To Clock              Endpoint Pin                    Reference Pin                   Corner  Actual(ns)  Slack(ns)
--------------------  --------------------  ------------------------------  ------------------------------  ------  ----------  ---------
net_clk               userclk1              i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_src_fifo/i_fifo_1_1_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/dest_graysync_ff_reg[0][1]/D
                                                                            i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_src_fifo/i_fifo_1_1_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/dest_graysync_ff_reg[0][0]/D
                                                                                                            Slow         0.680      9.320


Slack (MET) :             9.320ns  (requirement - actual skew)
  Endpoint Source:        <hidden>
                            (rising edge-triggered cell FDRE clocked by net_clk)
  Endpoint Destination:   <hidden>
                            (rising edge-triggered cell FDRE clocked by userclk1)
  Reference Source:       <hidden>
                            (rising edge-triggered cell FDRE clocked by net_clk)
  Reference Destination:  <hidden>
                            (rising edge-triggered cell FDRE clocked by userclk1)
  Path Type:              Bus Skew (Max at Slow Process Corner)
  Requirement:            10.000ns
  Endpoint Relative Delay:    0.241ns
  Reference Relative Delay:  -1.077ns
  Relative CRPR:              0.639ns
  Actual Bus Skew:            0.680ns  (Endpoint Relative Delay - Reference Relative Delay - Relative CRPR)

Endpoint path:
    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock net_clk rise edge)    0.000     0.000 r  
    J19                                               0.000     0.000 r  clk (IN)
                         net (fo=0)                   0.000     0.000    clk
    J19                  IBUF (Prop_ibuf_I_O)         1.416     1.416 r  clk_IBUF_inst/O
                         net (fo=1, routed)           1.709     3.124    clk_IBUF
    BUFGCTRL_X0Y16       BUFG (Prop_bufg_I_O)         0.081     3.205 r  clk_IBUF_BUFG_inst/O
                         net (fo=1816, routed)        1.535     4.741    <hidden>
    SLICE_X15Y150        FDRE                                         r  <hidden>
  -------------------------------------------------------------------    -------------------
    SLICE_X15Y150        FDRE (Prop_fdre_C_Q)         0.348     5.089 r  <hidden>
                         net (fo=1, routed)           0.507     5.595    <hidden>
    SLICE_X14Y150        FDRE                                         r  <hidden>
  -------------------------------------------------------------------    -------------------

                         (clock userclk1 rise edge)
                                                      0.000     0.000 r  
    GTPE2_CHANNEL_X0Y7   GTPE2_CHANNEL                0.000     0.000 r  i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_lane[0].gt_wrapper_i/gtp_channel.gtpe2_channel_i/TXOUTCLK
                         net (fo=1, routed)           1.199     1.199    i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/txoutclk_i.txoutclk_i_0
    BUFGCTRL_X0Y19       BUFG (Prop_bufg_I_O)         0.077     1.276 r  i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/txoutclk_i.txoutclk_i/O
                         net (fo=1, routed)           1.302     2.578    i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/refclk
    MMCME2_ADV_X0Y1      MMCME2_ADV (Prop_mmcme2_adv_CLKIN1_CLKOUT2)
                                                      0.073     2.651 r  i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/mmcm_i/CLKOUT2
                         net (fo=1, routed)           1.374     4.024    i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/userclk1
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.077     4.102 r  i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/userclk1_i1.usrclk1_i1/O
                         net (fo=3403, routed)        1.419     5.520    <hidden>
    SLICE_X14Y150        FDRE                                         r  <hidden>
                         clock pessimism              0.000     5.520    
    SLICE_X14Y150        FDRE (Setup_fdre_C_D)       -0.166     5.354    <hidden>
  -------------------------------------------------------------------
                         data arrival                           5.595    
                         clock arrival                          5.354    
  -------------------------------------------------------------------
                         relative delay                         0.241    

Reference path:
    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock net_clk rise edge)    0.000     0.000 r  
    J19                                               0.000     0.000 r  clk (IN)
                         net (fo=0)                   0.000     0.000    clk
    J19                  IBUF (Prop_ibuf_I_O)         1.350     1.350 r  clk_IBUF_inst/O
                         net (fo=1, routed)           1.620     2.970    clk_IBUF
    BUFGCTRL_X0Y16       BUFG (Prop_bufg_I_O)         0.077     3.047 r  clk_IBUF_BUFG_inst/O
                         net (fo=1816, routed)        1.417     4.464    <hidden>
    SLICE_X15Y150        FDRE                                         r  <hidden>
  -------------------------------------------------------------------    -------------------
    SLICE_X15Y150        FDRE (Prop_fdre_C_Q)         0.304     4.768 r  <hidden>
                         net (fo=1, routed)           0.224     4.992    <hidden>
    SLICE_X14Y150        FDRE                                         r  <hidden>
  -------------------------------------------------------------------    -------------------

                         (clock userclk1 rise edge)
                                                      0.000     0.000 r  
    GTPE2_CHANNEL_X0Y7   GTPE2_CHANNEL                0.000     0.000 r  i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_lane[0].gt_wrapper_i/gtp_channel.gtpe2_channel_i/TXOUTCLK
                         net (fo=1, routed)           1.257     1.257    i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/txoutclk_i.txoutclk_i_0
    BUFGCTRL_X0Y19       BUFG (Prop_bufg_I_O)         0.081     1.338 r  i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/txoutclk_i.txoutclk_i/O
                         net (fo=1, routed)           1.408     2.745    i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/refclk
    MMCME2_ADV_X0Y1      MMCME2_ADV (Prop_mmcme2_adv_CLKIN1_CLKOUT2)
                                                      0.077     2.822 r  i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/mmcm_i/CLKOUT2
                         net (fo=1, routed)           1.441     4.263    i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/userclk1
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.081     4.344 r  i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/userclk1_i1.usrclk1_i1/O
                         net (fo=3403, routed)        1.538     5.882    <hidden>
    SLICE_X14Y150        FDRE                                         r  <hidden>
                         clock pessimism              0.000     5.882    
    SLICE_X14Y150        FDRE (Hold_fdre_C_D)         0.187     6.069    <hidden>
  -------------------------------------------------------------------
                         data arrival                           4.992    
                         clock arrival                          6.069    
  -------------------------------------------------------------------
                         relative delay                        -1.077    



Id: 7
set_bus_skew -from [get_cells [list {i_pcileech_com/i_fifo_256_32_clk2_comtx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/src_gray_ff_reg[0]} \
          {i_pcileech_com/i_fifo_256_32_clk2_comtx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/src_gray_ff_reg[10]} \
          {i_pcileech_com/i_fifo_256_32_clk2_comtx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/src_gray_ff_reg[11]} \
          {i_pcileech_com/i_fifo_256_32_clk2_comtx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/src_gray_ff_reg[12]} \
          {i_pcileech_com/i_fifo_256_32_clk2_comtx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/src_gray_ff_reg[1]} \
          {i_pcileech_com/i_fifo_256_32_clk2_comtx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/src_gray_ff_reg[2]} \
          {i_pcileech_com/i_fifo_256_32_clk2_comtx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/src_gray_ff_reg[3]} \
          {i_pcileech_com/i_fifo_256_32_clk2_comtx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/src_gray_ff_reg[4]} \
          {i_pcileech_com/i_fifo_256_32_clk2_comtx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/src_gray_ff_reg[5]} \
          {i_pcileech_com/i_fifo_256_32_clk2_comtx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/src_gray_ff_reg[6]} \
          {i_pcileech_com/i_fifo_256_32_clk2_comtx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/src_gray_ff_reg[7]} \
          {i_pcileech_com/i_fifo_256_32_clk2_comtx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/src_gray_ff_reg[8]} \
          {i_pcileech_com/i_fifo_256_32_clk2_comtx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/src_gray_ff_reg[9]}]] -to [get_cells [list {i_pcileech_com/i_fifo_256_32_clk2_comtx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/dest_graysync_ff_reg[0][0]} \
          {i_pcileech_com/i_fifo_256_32_clk2_comtx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/dest_graysync_ff_reg[0][10]} \
          {i_pcileech_com/i_fifo_256_32_clk2_comtx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/dest_graysync_ff_reg[0][11]} \
          {i_pcileech_com/i_fifo_256_32_clk2_comtx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/dest_graysync_ff_reg[0][12]} \
          {i_pcileech_com/i_fifo_256_32_clk2_comtx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/dest_graysync_ff_reg[0][1]} \
          {i_pcileech_com/i_fifo_256_32_clk2_comtx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/dest_graysync_ff_reg[0][2]} \
          {i_pcileech_com/i_fifo_256_32_clk2_comtx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/dest_graysync_ff_reg[0][3]} \
          {i_pcileech_com/i_fifo_256_32_clk2_comtx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/dest_graysync_ff_reg[0][4]} \
          {i_pcileech_com/i_fifo_256_32_clk2_comtx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/dest_graysync_ff_reg[0][5]} \
          {i_pcileech_com/i_fifo_256_32_clk2_comtx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/dest_graysync_ff_reg[0][6]} \
          {i_pcileech_com/i_fifo_256_32_clk2_comtx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/dest_graysync_ff_reg[0][7]} \
          {i_pcileech_com/i_fifo_256_32_clk2_comtx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/dest_graysync_ff_reg[0][8]} \
          {i_pcileech_com/i_fifo_256_32_clk2_comtx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/dest_graysync_ff_reg[0][9]}]] 10.000
Requirement: 10.000ns
Endpoints: 13

From Clock            To Clock              Endpoint Pin                    Reference Pin                   Corner  Actual(ns)  Slack(ns)
--------------------  --------------------  ------------------------------  ------------------------------  ------  ----------  ---------
net_clk               net_ft601_clk         i_pcileech_com/i_fifo_256_32_clk2_comtx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/dest_graysync_ff_reg[0][0]/D
                                                                            i_pcileech_com/i_fifo_256_32_clk2_comtx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/dest_graysync_ff_reg[0][7]/D
                                                                                                            Slow         0.553      9.447


Slack (MET) :             9.447ns  (requirement - actual skew)
  Endpoint Source:        <hidden>
                            (rising edge-triggered cell FDRE clocked by net_clk)
  Endpoint Destination:   <hidden>
                            (rising edge-triggered cell FDRE clocked by net_ft601_clk)
  Reference Source:       <hidden>
                            (rising edge-triggered cell FDRE clocked by net_clk)
  Reference Destination:  <hidden>
                            (rising edge-triggered cell FDRE clocked by net_ft601_clk)
  Path Type:              Bus Skew (Max at Slow Process Corner)
  Requirement:            10.000ns
  Endpoint Relative Delay:    1.174ns
  Reference Relative Delay:   0.140ns
  Relative CRPR:              0.481ns
  Actual Bus Skew:            0.553ns  (Endpoint Relative Delay - Reference Relative Delay - Relative CRPR)

Endpoint path:
    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock net_clk rise edge)    0.000     0.000 r  
    J19                                               0.000     0.000 r  clk (IN)
                         net (fo=0)                   0.000     0.000    clk
    J19                  IBUF (Prop_ibuf_I_O)         1.416     1.416 r  clk_IBUF_inst/O
                         net (fo=1, routed)           1.709     3.124    clk_IBUF
    BUFGCTRL_X0Y16       BUFG (Prop_bufg_I_O)         0.081     3.205 r  clk_IBUF_BUFG_inst/O
                         net (fo=1816, routed)        1.354     4.560    <hidden>
    SLICE_X31Y130        FDRE                                         r  <hidden>
  -------------------------------------------------------------------    -------------------
    SLICE_X31Y130        FDRE (Prop_fdre_C_Q)         0.379     4.939 r  <hidden>
                         net (fo=1, routed)           0.458     5.396    <hidden>
    SLICE_X35Y131        FDRE                                         r  <hidden>
  -------------------------------------------------------------------    -------------------

                         (clock net_ft601_clk rise edge)
                                                      0.000     0.000 r  
    K18                                               0.000     0.000 r  ft601_clk (IN)
                         net (fo=0)                   0.000     0.000    ft601_clk
    K18                  IBUF (Prop_ibuf_I_O)         1.353     1.353 r  ft601_clk_IBUF_inst/O
                         net (fo=1, routed)           1.620     2.974    ft601_clk_IBUF
    BUFGCTRL_X0Y17       BUFG (Prop_bufg_I_O)         0.077     3.051 r  ft601_clk_IBUF_BUFG_inst/O
                         net (fo=411, routed)         1.247     4.298    <hidden>
    SLICE_X35Y131        FDRE                                         r  <hidden>
                         clock pessimism              0.000     4.298    
    SLICE_X35Y131        FDRE (Setup_fdre_C_D)       -0.075     4.223    <hidden>
  -------------------------------------------------------------------
                         data arrival                           5.396    
                         clock arrival                          4.223    
  -------------------------------------------------------------------
                         relative delay                         1.174    

Reference path:
    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock net_clk rise edge)    0.000     0.000 r  
    J19                                               0.000     0.000 r  clk (IN)
                         net (fo=0)                   0.000     0.000    clk
    J19                  IBUF (Prop_ibuf_I_O)         1.350     1.350 r  clk_IBUF_inst/O
                         net (fo=1, routed)           1.620     2.970    clk_IBUF
    BUFGCTRL_X0Y16       BUFG (Prop_bufg_I_O)         0.077     3.047 r  clk_IBUF_BUFG_inst/O
                         net (fo=1816, routed)        1.250     4.297    <hidden>
    SLICE_X33Y133        FDRE                                         r  <hidden>
  -------------------------------------------------------------------    -------------------
    SLICE_X33Y133        FDRE (Prop_fdre_C_Q)         0.279     4.576 r  <hidden>
                         net (fo=1, routed)           0.211     4.787    <hidden>
    SLICE_X34Y133        FDRE                                         r  <hidden>
  -------------------------------------------------------------------    -------------------

                         (clock net_ft601_clk rise edge)
                                                      0.000     0.000 r  
    K18                                               0.000     0.000 r  ft601_clk (IN)
                         net (fo=0)                   0.000     0.000    ft601_clk
    K18                  IBUF (Prop_ibuf_I_O)         1.419     1.419 r  ft601_clk_IBUF_inst/O
                         net (fo=1, routed)           1.709     3.128    ft601_clk_IBUF
    BUFGCTRL_X0Y17       BUFG (Prop_bufg_I_O)         0.081     3.209 r  ft601_clk_IBUF_BUFG_inst/O
                         net (fo=411, routed)         1.355     4.565    <hidden>
    SLICE_X34Y133        FDRE                                         r  <hidden>
                         clock pessimism              0.000     4.565    
    SLICE_X34Y133        FDRE (Hold_fdre_C_D)         0.083     4.648    <hidden>
  -------------------------------------------------------------------
                         data arrival                           4.787    
                         clock arrival                          4.648    
  -------------------------------------------------------------------
                         relative delay                         0.140    



Id: 8
set_bus_skew -from [get_cells [list {i_pcileech_com/i_fifo_256_32_clk2_comtx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/src_gray_ff_reg[9]} \
          {i_pcileech_com/i_fifo_256_32_clk2_comtx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/src_gray_ff_reg[10]} \
          {i_pcileech_com/i_fifo_256_32_clk2_comtx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/src_gray_ff_reg[11]} \
          {i_pcileech_com/i_fifo_256_32_clk2_comtx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/src_gray_ff_reg[12]} \
          {i_pcileech_com/i_fifo_256_32_clk2_comtx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/src_gray_ff_reg[13]} \
          {i_pcileech_com/i_fifo_256_32_clk2_comtx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/src_gray_ff_reg[14]} \
          {i_pcileech_com/i_fifo_256_32_clk2_comtx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/src_gray_ff_reg[15]} \
          {i_pcileech_com/i_fifo_256_32_clk2_comtx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/src_gray_ff_reg[8]} \
          {i_pcileech_com/i_fifo_256_32_clk2_comtx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/src_gray_ff_reg[7]} \
          {i_pcileech_com/i_fifo_256_32_clk2_comtx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/src_gray_ff_reg[3]} \
          {i_pcileech_com/i_fifo_256_32_clk2_comtx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/src_gray_ff_reg[4]} \
          {i_pcileech_com/i_fifo_256_32_clk2_comtx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/src_gray_ff_reg[5]} \
          {i_pcileech_com/i_fifo_256_32_clk2_comtx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/src_gray_ff_reg[6]}]] -to [get_cells [list {i_pcileech_com/i_fifo_256_32_clk2_comtx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/dest_graysync_ff_reg[0][9]} \
          {i_pcileech_com/i_fifo_256_32_clk2_comtx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/dest_graysync_ff_reg[0][10]} \
          {i_pcileech_com/i_fifo_256_32_clk2_comtx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/dest_graysync_ff_reg[0][11]} \
          {i_pcileech_com/i_fifo_256_32_clk2_comtx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/dest_graysync_ff_reg[0][12]} \
          {i_pcileech_com/i_fifo_256_32_clk2_comtx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/dest_graysync_ff_reg[0][13]} \
          {i_pcileech_com/i_fifo_256_32_clk2_comtx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/dest_graysync_ff_reg[0][14]} \
          {i_pcileech_com/i_fifo_256_32_clk2_comtx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/dest_graysync_ff_reg[0][15]} \
          {i_pcileech_com/i_fifo_256_32_clk2_comtx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/dest_graysync_ff_reg[0][8]} \
          {i_pcileech_com/i_fifo_256_32_clk2_comtx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/dest_graysync_ff_reg[0][7]} \
          {i_pcileech_com/i_fifo_256_32_clk2_comtx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/dest_graysync_ff_reg[0][3]} \
          {i_pcileech_com/i_fifo_256_32_clk2_comtx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/dest_graysync_ff_reg[0][4]} \
          {i_pcileech_com/i_fifo_256_32_clk2_comtx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/dest_graysync_ff_reg[0][5]} \
          {i_pcileech_com/i_fifo_256_32_clk2_comtx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/dest_graysync_ff_reg[0][6]}]] 10.000
Requirement: 10.000ns
Endpoints: 13

From Clock            To Clock              Endpoint Pin                    Reference Pin                   Corner  Actual(ns)  Slack(ns)
--------------------  --------------------  ------------------------------  ------------------------------  ------  ----------  ---------
net_ft601_clk         net_clk               i_pcileech_com/i_fifo_256_32_clk2_comtx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/dest_graysync_ff_reg[0][11]/D
                                                                            i_pcileech_com/i_fifo_256_32_clk2_comtx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/dest_graysync_ff_reg[0][8]/D
                                                                                                            Slow         0.612      9.388


Slack (MET) :             9.388ns  (requirement - actual skew)
  Endpoint Source:        <hidden>
                            (rising edge-triggered cell FDRE clocked by net_ft601_clk)
  Endpoint Destination:   <hidden>
                            (rising edge-triggered cell FDRE clocked by net_clk)
  Reference Source:       <hidden>
                            (rising edge-triggered cell FDRE clocked by net_ft601_clk)
  Reference Destination:  <hidden>
                            (rising edge-triggered cell FDRE clocked by net_clk)
  Path Type:              Bus Skew (Max at Slow Process Corner)
  Requirement:            10.000ns
  Endpoint Relative Delay:    1.244ns
  Reference Relative Delay:   0.151ns
  Relative CRPR:              0.481ns
  Actual Bus Skew:            0.612ns  (Endpoint Relative Delay - Reference Relative Delay - Relative CRPR)

Endpoint path:
    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock net_ft601_clk rise edge)
                                                      0.000     0.000 r  
    K18                                               0.000     0.000 r  ft601_clk (IN)
                         net (fo=0)                   0.000     0.000    ft601_clk
    K18                  IBUF (Prop_ibuf_I_O)         1.419     1.419 r  ft601_clk_IBUF_inst/O
                         net (fo=1, routed)           1.709     3.128    ft601_clk_IBUF
    BUFGCTRL_X0Y17       BUFG (Prop_bufg_I_O)         0.081     3.209 r  ft601_clk_IBUF_BUFG_inst/O
                         net (fo=411, routed)         1.357     4.567    <hidden>
    SLICE_X34Y135        FDRE                                         r  <hidden>
  -------------------------------------------------------------------    -------------------
    SLICE_X34Y135        FDRE (Prop_fdre_C_Q)         0.398     4.965 r  <hidden>
                         net (fo=1, routed)           0.376     5.340    <hidden>
    SLICE_X33Y135        FDRE                                         r  <hidden>
  -------------------------------------------------------------------    -------------------

                         (clock net_clk rise edge)    0.000     0.000 r  
    J19                                               0.000     0.000 r  clk (IN)
                         net (fo=0)                   0.000     0.000    clk
    J19                  IBUF (Prop_ibuf_I_O)         1.350     1.350 r  clk_IBUF_inst/O
                         net (fo=1, routed)           1.620     2.970    clk_IBUF
    BUFGCTRL_X0Y16       BUFG (Prop_bufg_I_O)         0.077     3.047 r  clk_IBUF_BUFG_inst/O
                         net (fo=1816, routed)        1.251     4.298    <hidden>
    SLICE_X33Y135        FDRE                                         r  <hidden>
                         clock pessimism              0.000     4.298    
    SLICE_X33Y135        FDRE (Setup_fdre_C_D)       -0.202     4.096    <hidden>
  -------------------------------------------------------------------
                         data arrival                           5.340    
                         clock arrival                          4.096    
  -------------------------------------------------------------------
                         relative delay                         1.244    

Reference path:
    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock net_ft601_clk rise edge)
                                                      0.000     0.000 r  
    K18                                               0.000     0.000 r  ft601_clk (IN)
                         net (fo=0)                   0.000     0.000    ft601_clk
    K18                  IBUF (Prop_ibuf_I_O)         1.353     1.353 r  ft601_clk_IBUF_inst/O
                         net (fo=1, routed)           1.620     2.974    ft601_clk_IBUF
    BUFGCTRL_X0Y17       BUFG (Prop_bufg_I_O)         0.077     3.051 r  ft601_clk_IBUF_BUFG_inst/O
                         net (fo=411, routed)         1.248     4.299    <hidden>
    SLICE_X34Y132        FDRE                                         r  <hidden>
  -------------------------------------------------------------------    -------------------
    SLICE_X34Y132        FDRE (Prop_fdre_C_Q)         0.347     4.646 r  <hidden>
                         net (fo=1, routed)           0.207     4.852    <hidden>
    SLICE_X33Y132        FDRE                                         r  <hidden>
  -------------------------------------------------------------------    -------------------

                         (clock net_clk rise edge)    0.000     0.000 r  
    J19                                               0.000     0.000 r  clk (IN)
                         net (fo=0)                   0.000     0.000    clk
    J19                  IBUF (Prop_ibuf_I_O)         1.416     1.416 r  clk_IBUF_inst/O
                         net (fo=1, routed)           1.709     3.124    clk_IBUF
    BUFGCTRL_X0Y16       BUFG (Prop_bufg_I_O)         0.081     3.205 r  clk_IBUF_BUFG_inst/O
                         net (fo=1816, routed)        1.356     4.562    <hidden>
    SLICE_X33Y132        FDRE                                         r  <hidden>
                         clock pessimism              0.000     4.562    
    SLICE_X33Y132        FDRE (Hold_fdre_C_D)         0.139     4.701    <hidden>
  -------------------------------------------------------------------
                         data arrival                           4.852    
                         clock arrival                          4.701    
  -------------------------------------------------------------------
                         relative delay                         0.151    



Id: 9
set_bus_skew -from [get_cells [list {i_pcileech_pcie_a7/i_pcileech_pcie_cfg_a7/i_fifo_pcie_cfg_rx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/src_gray_ff_reg[0]} {i_pcileech_pcie_a7/i_pcileech_pcie_cfg_a7/i_fifo_pcie_cfg_rx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/src_gray_ff_reg[1]} {i_pcileech_pcie_a7/i_pcileech_pcie_cfg_a7/i_fifo_pcie_cfg_rx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/src_gray_ff_reg[2]} {i_pcileech_pcie_a7/i_pcileech_pcie_cfg_a7/i_fifo_pcie_cfg_rx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/src_gray_ff_reg[3]} {i_pcileech_pcie_a7/i_pcileech_pcie_cfg_a7/i_fifo_pcie_cfg_rx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/src_gray_ff_reg[4]} {i_pcileech_pcie_a7/i_pcileech_pcie_cfg_a7/i_fifo_pcie_cfg_rx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/src_gray_ff_reg[5]} {i_pcileech_pcie_a7/i_pcileech_pcie_cfg_a7/i_fifo_pcie_cfg_rx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/src_gray_ff_reg[6]} {i_pcileech_pcie_a7/i_pcileech_pcie_cfg_a7/i_fifo_pcie_cfg_rx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/src_gray_ff_reg[7]} {i_pcileech_pcie_a7/i_pcileech_pcie_cfg_a7/i_fifo_pcie_cfg_rx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/src_gray_ff_reg[8]}]] -to [get_cells [list {i_pcileech_pcie_a7/i_pcileech_pcie_cfg_a7/i_fifo_pcie_cfg_rx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/dest_graysync_ff_reg[0][0]} {i_pcileech_pcie_a7/i_pcileech_pcie_cfg_a7/i_fifo_pcie_cfg_rx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/dest_graysync_ff_reg[0][1]} {i_pcileech_pcie_a7/i_pcileech_pcie_cfg_a7/i_fifo_pcie_cfg_rx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/dest_graysync_ff_reg[0][2]} {i_pcileech_pcie_a7/i_pcileech_pcie_cfg_a7/i_fifo_pcie_cfg_rx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/dest_graysync_ff_reg[0][3]} {i_pcileech_pcie_a7/i_pcileech_pcie_cfg_a7/i_fifo_pcie_cfg_rx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/dest_graysync_ff_reg[0][4]} {i_pcileech_pcie_a7/i_pcileech_pcie_cfg_a7/i_fifo_pcie_cfg_rx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/dest_graysync_ff_reg[0][5]} {i_pcileech_pcie_a7/i_pcileech_pcie_cfg_a7/i_fifo_pcie_cfg_rx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/dest_graysync_ff_reg[0][6]} {i_pcileech_pcie_a7/i_pcileech_pcie_cfg_a7/i_fifo_pcie_cfg_rx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/dest_graysync_ff_reg[0][7]} {i_pcileech_pcie_a7/i_pcileech_pcie_cfg_a7/i_fifo_pcie_cfg_rx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/dest_graysync_ff_reg[0][8]}]] 10.000
Requirement: 10.000ns
Endpoints: 9

From Clock            To Clock              Endpoint Pin                    Reference Pin                   Corner  Actual(ns)  Slack(ns)
--------------------  --------------------  ------------------------------  ------------------------------  ------  ----------  ---------
net_clk               userclk1              i_pcileech_pcie_a7/i_pcileech_pcie_cfg_a7/i_fifo_pcie_cfg_rx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/dest_graysync_ff_reg[0][3]/D
                                                                            i_pcileech_pcie_a7/i_pcileech_pcie_cfg_a7/i_fifo_pcie_cfg_rx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/dest_graysync_ff_reg[0][8]/D
                                                                                                            Slow         0.625      9.375


Slack (MET) :             9.375ns  (requirement - actual skew)
  Endpoint Source:        <hidden>
                            (rising edge-triggered cell FDRE clocked by net_clk)
  Endpoint Destination:   <hidden>
                            (rising edge-triggered cell FDRE clocked by userclk1)
  Reference Source:       <hidden>
                            (rising edge-triggered cell FDRE clocked by net_clk)
  Reference Destination:  <hidden>
                            (rising edge-triggered cell FDRE clocked by userclk1)
  Path Type:              Bus Skew (Max at Slow Process Corner)
  Requirement:            10.000ns
  Endpoint Relative Delay:    0.150ns
  Reference Relative Delay:  -1.043ns
  Relative CRPR:              0.568ns
  Actual Bus Skew:            0.625ns  (Endpoint Relative Delay - Reference Relative Delay - Relative CRPR)

Endpoint path:
    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock net_clk rise edge)    0.000     0.000 r  
    J19                                               0.000     0.000 r  clk (IN)
                         net (fo=0)                   0.000     0.000    clk
    J19                  IBUF (Prop_ibuf_I_O)         1.416     1.416 r  clk_IBUF_inst/O
                         net (fo=1, routed)           1.709     3.124    clk_IBUF
    BUFGCTRL_X0Y16       BUFG (Prop_bufg_I_O)         0.081     3.205 r  clk_IBUF_BUFG_inst/O
                         net (fo=1816, routed)        1.350     4.556    <hidden>
    SLICE_X40Y129        FDRE                                         r  <hidden>
  -------------------------------------------------------------------    -------------------
    SLICE_X40Y129        FDRE (Prop_fdre_C_Q)         0.348     4.904 r  <hidden>
                         net (fo=1, routed)           0.384     5.288    <hidden>
    SLICE_X41Y128        FDRE                                         r  <hidden>
  -------------------------------------------------------------------    -------------------

                         (clock userclk1 rise edge)
                                                      0.000     0.000 r  
    GTPE2_CHANNEL_X0Y7   GTPE2_CHANNEL                0.000     0.000 r  i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_lane[0].gt_wrapper_i/gtp_channel.gtpe2_channel_i/TXOUTCLK
                         net (fo=1, routed)           1.199     1.199    i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/txoutclk_i.txoutclk_i_0
    BUFGCTRL_X0Y19       BUFG (Prop_bufg_I_O)         0.077     1.276 r  i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/txoutclk_i.txoutclk_i/O
                         net (fo=1, routed)           1.302     2.578    i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/refclk
    MMCME2_ADV_X0Y1      MMCME2_ADV (Prop_mmcme2_adv_CLKIN1_CLKOUT2)
                                                      0.073     2.651 r  i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/mmcm_i/CLKOUT2
                         net (fo=1, routed)           1.374     4.024    i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/userclk1
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.077     4.102 r  i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/userclk1_i1.usrclk1_i1/O
                         net (fo=3403, routed)        1.243     5.345    <hidden>
    SLICE_X41Y128        FDRE                                         r  <hidden>
                         clock pessimism              0.000     5.345    
    SLICE_X41Y128        FDRE (Setup_fdre_C_D)       -0.207     5.138    <hidden>
  -------------------------------------------------------------------
                         data arrival                           5.288    
                         clock arrival                          5.138    
  -------------------------------------------------------------------
                         relative delay                         0.150    

Reference path:
    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock net_clk rise edge)    0.000     0.000 r  
    J19                                               0.000     0.000 r  clk (IN)
                         net (fo=0)                   0.000     0.000    clk
    J19                  IBUF (Prop_ibuf_I_O)         1.350     1.350 r  clk_IBUF_inst/O
                         net (fo=1, routed)           1.620     2.970    clk_IBUF
    BUFGCTRL_X0Y16       BUFG (Prop_bufg_I_O)         0.077     3.047 r  clk_IBUF_BUFG_inst/O
                         net (fo=1816, routed)        1.241     4.288    <hidden>
    SLICE_X39Y128        FDRE                                         r  <hidden>
  -------------------------------------------------------------------    -------------------
    SLICE_X39Y128        FDRE (Prop_fdre_C_Q)         0.304     4.592 r  <hidden>
                         net (fo=1, routed)           0.200     4.792    <hidden>
    SLICE_X40Y128        FDRE                                         r  <hidden>
  -------------------------------------------------------------------    -------------------

                         (clock userclk1 rise edge)
                                                      0.000     0.000 r  
    GTPE2_CHANNEL_X0Y7   GTPE2_CHANNEL                0.000     0.000 r  i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_lane[0].gt_wrapper_i/gtp_channel.gtpe2_channel_i/TXOUTCLK
                         net (fo=1, routed)           1.257     1.257    i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/txoutclk_i.txoutclk_i_0
    BUFGCTRL_X0Y19       BUFG (Prop_bufg_I_O)         0.081     1.338 r  i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/txoutclk_i.txoutclk_i/O
                         net (fo=1, routed)           1.408     2.745    i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/refclk
    MMCME2_ADV_X0Y1      MMCME2_ADV (Prop_mmcme2_adv_CLKIN1_CLKOUT2)
                                                      0.077     2.822 r  i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/mmcm_i/CLKOUT2
                         net (fo=1, routed)           1.441     4.263    i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/userclk1
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.081     4.344 r  i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/userclk1_i1.usrclk1_i1/O
                         net (fo=3403, routed)        1.351     5.695    <hidden>
    SLICE_X40Y128        FDRE                                         r  <hidden>
                         clock pessimism              0.000     5.695    
    SLICE_X40Y128        FDRE (Hold_fdre_C_D)         0.139     5.834    <hidden>
  -------------------------------------------------------------------
                         data arrival                           4.792    
                         clock arrival                          5.834    
  -------------------------------------------------------------------
                         relative delay                        -1.043    



Id: 10
set_bus_skew -from [get_cells [list {i_pcileech_pcie_a7/i_pcileech_pcie_cfg_a7/i_fifo_pcie_cfg_rx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/src_gray_ff_reg[0]} {i_pcileech_pcie_a7/i_pcileech_pcie_cfg_a7/i_fifo_pcie_cfg_rx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/src_gray_ff_reg[1]} {i_pcileech_pcie_a7/i_pcileech_pcie_cfg_a7/i_fifo_pcie_cfg_rx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/src_gray_ff_reg[2]} {i_pcileech_pcie_a7/i_pcileech_pcie_cfg_a7/i_fifo_pcie_cfg_rx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/src_gray_ff_reg[3]} {i_pcileech_pcie_a7/i_pcileech_pcie_cfg_a7/i_fifo_pcie_cfg_rx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/src_gray_ff_reg[4]} {i_pcileech_pcie_a7/i_pcileech_pcie_cfg_a7/i_fifo_pcie_cfg_rx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/src_gray_ff_reg[5]} {i_pcileech_pcie_a7/i_pcileech_pcie_cfg_a7/i_fifo_pcie_cfg_rx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/src_gray_ff_reg[6]} {i_pcileech_pcie_a7/i_pcileech_pcie_cfg_a7/i_fifo_pcie_cfg_rx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/src_gray_ff_reg[7]} {i_pcileech_pcie_a7/i_pcileech_pcie_cfg_a7/i_fifo_pcie_cfg_rx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/src_gray_ff_reg[8]}]] -to [get_cells [list {i_pcileech_pcie_a7/i_pcileech_pcie_cfg_a7/i_fifo_pcie_cfg_rx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/dest_graysync_ff_reg[0][0]} {i_pcileech_pcie_a7/i_pcileech_pcie_cfg_a7/i_fifo_pcie_cfg_rx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/dest_graysync_ff_reg[0][1]} {i_pcileech_pcie_a7/i_pcileech_pcie_cfg_a7/i_fifo_pcie_cfg_rx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/dest_graysync_ff_reg[0][2]} {i_pcileech_pcie_a7/i_pcileech_pcie_cfg_a7/i_fifo_pcie_cfg_rx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/dest_graysync_ff_reg[0][3]} {i_pcileech_pcie_a7/i_pcileech_pcie_cfg_a7/i_fifo_pcie_cfg_rx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/dest_graysync_ff_reg[0][4]} {i_pcileech_pcie_a7/i_pcileech_pcie_cfg_a7/i_fifo_pcie_cfg_rx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/dest_graysync_ff_reg[0][5]} {i_pcileech_pcie_a7/i_pcileech_pcie_cfg_a7/i_fifo_pcie_cfg_rx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/dest_graysync_ff_reg[0][6]} {i_pcileech_pcie_a7/i_pcileech_pcie_cfg_a7/i_fifo_pcie_cfg_rx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/dest_graysync_ff_reg[0][7]} {i_pcileech_pcie_a7/i_pcileech_pcie_cfg_a7/i_fifo_pcie_cfg_rx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/dest_graysync_ff_reg[0][8]}]] 10.000
Requirement: 10.000ns
Endpoints: 9

From Clock            To Clock              Endpoint Pin                    Reference Pin                   Corner  Actual(ns)  Slack(ns)
--------------------  --------------------  ------------------------------  ------------------------------  ------  ----------  ---------
userclk1              net_clk               i_pcileech_pcie_a7/i_pcileech_pcie_cfg_a7/i_fifo_pcie_cfg_rx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/dest_graysync_ff_reg[0][8]/D
                                                                            i_pcileech_pcie_a7/i_pcileech_pcie_cfg_a7/i_fifo_pcie_cfg_rx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/dest_graysync_ff_reg[0][6]/D
                                                                                                            Slow         0.752      9.248


Slack (MET) :             9.248ns  (requirement - actual skew)
  Endpoint Source:        <hidden>
                            (rising edge-triggered cell FDRE clocked by userclk1)
  Endpoint Destination:   <hidden>
                            (rising edge-triggered cell FDRE clocked by net_clk)
  Reference Source:       <hidden>
                            (rising edge-triggered cell FDRE clocked by userclk1)
  Reference Destination:  <hidden>
                            (rising edge-triggered cell FDRE clocked by net_clk)
  Path Type:              Bus Skew (Max at Slow Process Corner)
  Requirement:            10.000ns
  Endpoint Relative Delay:    2.467ns
  Reference Relative Delay:   1.148ns
  Relative CRPR:              0.567ns
  Actual Bus Skew:            0.752ns  (Endpoint Relative Delay - Reference Relative Delay - Relative CRPR)

Endpoint path:
    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock userclk1 rise edge)
                                                      0.000     0.000 r  
    GTPE2_CHANNEL_X0Y7   GTPE2_CHANNEL                0.000     0.000 r  i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_lane[0].gt_wrapper_i/gtp_channel.gtpe2_channel_i/TXOUTCLK
                         net (fo=1, routed)           1.257     1.257    i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/txoutclk_i.txoutclk_i_0
    BUFGCTRL_X0Y19       BUFG (Prop_bufg_I_O)         0.081     1.338 r  i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/txoutclk_i.txoutclk_i/O
                         net (fo=1, routed)           1.408     2.745    i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/refclk
    MMCME2_ADV_X0Y1      MMCME2_ADV (Prop_mmcme2_adv_CLKIN1_CLKOUT2)
                                                      0.077     2.822 r  i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/mmcm_i/CLKOUT2
                         net (fo=1, routed)           1.441     4.263    i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/userclk1
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.081     4.344 r  i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/userclk1_i1.usrclk1_i1/O
                         net (fo=3403, routed)        1.350     5.694    <hidden>
    SLICE_X42Y128        FDRE                                         r  <hidden>
  -------------------------------------------------------------------    -------------------
    SLICE_X42Y128        FDRE (Prop_fdre_C_Q)         0.398     6.092 r  <hidden>
                         net (fo=1, routed)           0.459     6.552    <hidden>
    SLICE_X43Y128        FDRE                                         r  <hidden>
  -------------------------------------------------------------------    -------------------

                         (clock net_clk rise edge)    0.000     0.000 r  
    J19                                               0.000     0.000 r  clk (IN)
                         net (fo=0)                   0.000     0.000    clk
    J19                  IBUF (Prop_ibuf_I_O)         1.350     1.350 r  clk_IBUF_inst/O
                         net (fo=1, routed)           1.620     2.970    clk_IBUF
    BUFGCTRL_X0Y16       BUFG (Prop_bufg_I_O)         0.077     3.047 r  clk_IBUF_BUFG_inst/O
                         net (fo=1816, routed)        1.240     4.287    <hidden>
    SLICE_X43Y128        FDRE                                         r  <hidden>
                         clock pessimism              0.000     4.287    
    SLICE_X43Y128        FDRE (Setup_fdre_C_D)       -0.202     4.085    <hidden>
  -------------------------------------------------------------------
                         data arrival                           6.552    
                         clock arrival                          4.085    
  -------------------------------------------------------------------
                         relative delay                         2.467    

Reference path:
    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock userclk1 rise edge)
                                                      0.000     0.000 r  
    GTPE2_CHANNEL_X0Y7   GTPE2_CHANNEL                0.000     0.000 r  i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_lane[0].gt_wrapper_i/gtp_channel.gtpe2_channel_i/TXOUTCLK
                         net (fo=1, routed)           1.199     1.199    i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/txoutclk_i.txoutclk_i_0
    BUFGCTRL_X0Y19       BUFG (Prop_bufg_I_O)         0.077     1.276 r  i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/txoutclk_i.txoutclk_i/O
                         net (fo=1, routed)           1.302     2.578    i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/refclk
    MMCME2_ADV_X0Y1      MMCME2_ADV (Prop_mmcme2_adv_CLKIN1_CLKOUT2)
                                                      0.073     2.651 r  i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/mmcm_i/CLKOUT2
                         net (fo=1, routed)           1.374     4.024    i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/userclk1
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.077     4.102 r  i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/userclk1_i1.usrclk1_i1/O
                         net (fo=3403, routed)        1.242     5.344    <hidden>
    SLICE_X44Y127        FDRE                                         r  <hidden>
  -------------------------------------------------------------------    -------------------
    SLICE_X44Y127        FDRE (Prop_fdre_C_Q)         0.304     5.648 r  <hidden>
                         net (fo=1, routed)           0.190     5.838    <hidden>
    SLICE_X43Y127        FDRE                                         r  <hidden>
  -------------------------------------------------------------------    -------------------

                         (clock net_clk rise edge)    0.000     0.000 r  
    J19                                               0.000     0.000 r  clk (IN)
                         net (fo=0)                   0.000     0.000    clk
    J19                  IBUF (Prop_ibuf_I_O)         1.416     1.416 r  clk_IBUF_inst/O
                         net (fo=1, routed)           1.709     3.124    clk_IBUF
    BUFGCTRL_X0Y16       BUFG (Prop_bufg_I_O)         0.081     3.205 r  clk_IBUF_BUFG_inst/O
                         net (fo=1816, routed)        1.346     4.552    <hidden>
    SLICE_X43Y127        FDRE                                         r  <hidden>
                         clock pessimism              0.000     4.552    
    SLICE_X43Y127        FDRE (Hold_fdre_C_D)         0.138     4.690    <hidden>
  -------------------------------------------------------------------
                         data arrival                           5.838    
                         clock arrival                          4.690    
  -------------------------------------------------------------------
                         relative delay                         1.148    



Id: 11
set_bus_skew -from [get_cells [list {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_cfgspace_shadow/i_fifo_43_43_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/src_gray_ff_reg[0]} {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_cfgspace_shadow/i_fifo_43_43_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/src_gray_ff_reg[1]} {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_cfgspace_shadow/i_fifo_43_43_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/src_gray_ff_reg[2]} {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_cfgspace_shadow/i_fifo_43_43_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/src_gray_ff_reg[3]}]] -to [get_cells [list {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_cfgspace_shadow/i_fifo_43_43_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/dest_graysync_ff_reg[0][0]} {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_cfgspace_shadow/i_fifo_43_43_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/dest_graysync_ff_reg[0][1]} {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_cfgspace_shadow/i_fifo_43_43_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/dest_graysync_ff_reg[0][2]} {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_cfgspace_shadow/i_fifo_43_43_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/dest_graysync_ff_reg[0][3]}]] 10.000
Requirement: 10.000ns
Endpoints: 4

From Clock            To Clock              Endpoint Pin                    Reference Pin                   Corner  Actual(ns)  Slack(ns)
--------------------  --------------------  ------------------------------  ------------------------------  ------  ----------  ---------
net_clk               userclk1              i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_cfgspace_shadow/i_fifo_43_43_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/dest_graysync_ff_reg[0][1]/D
                                                                            i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_cfgspace_shadow/i_fifo_43_43_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/dest_graysync_ff_reg[0][0]/D
                                                                                                            Slow         0.648      9.352


Slack (MET) :             9.352ns  (requirement - actual skew)
  Endpoint Source:        <hidden>
                            (rising edge-triggered cell FDRE clocked by net_clk)
  Endpoint Destination:   <hidden>
                            (rising edge-triggered cell FDRE clocked by userclk1)
  Reference Source:       <hidden>
                            (rising edge-triggered cell FDRE clocked by net_clk)
  Reference Destination:  <hidden>
                            (rising edge-triggered cell FDRE clocked by userclk1)
  Path Type:              Bus Skew (Max at Slow Process Corner)
  Requirement:            10.000ns
  Endpoint Relative Delay:    0.255ns
  Reference Relative Delay:  -1.024ns
  Relative CRPR:              0.631ns
  Actual Bus Skew:            0.648ns  (Endpoint Relative Delay - Reference Relative Delay - Relative CRPR)

Endpoint path:
    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock net_clk rise edge)    0.000     0.000 r  
    J19                                               0.000     0.000 r  clk (IN)
                         net (fo=0)                   0.000     0.000    clk
    J19                  IBUF (Prop_ibuf_I_O)         1.416     1.416 r  clk_IBUF_inst/O
                         net (fo=1, routed)           1.709     3.124    clk_IBUF
    BUFGCTRL_X0Y16       BUFG (Prop_bufg_I_O)         0.081     3.205 r  clk_IBUF_BUFG_inst/O
                         net (fo=1816, routed)        1.518     4.724    <hidden>
    SLICE_X21Y172        FDRE                                         r  <hidden>
  -------------------------------------------------------------------    -------------------
    SLICE_X21Y172        FDRE (Prop_fdre_C_Q)         0.348     5.072 r  <hidden>
                         net (fo=1, routed)           0.480     5.552    <hidden>
    SLICE_X20Y172        FDRE                                         r  <hidden>
  -------------------------------------------------------------------    -------------------

                         (clock userclk1 rise edge)
                                                      0.000     0.000 r  
    GTPE2_CHANNEL_X0Y7   GTPE2_CHANNEL                0.000     0.000 r  i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_lane[0].gt_wrapper_i/gtp_channel.gtpe2_channel_i/TXOUTCLK
                         net (fo=1, routed)           1.199     1.199    i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/txoutclk_i.txoutclk_i_0
    BUFGCTRL_X0Y19       BUFG (Prop_bufg_I_O)         0.077     1.276 r  i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/txoutclk_i.txoutclk_i/O
                         net (fo=1, routed)           1.302     2.578    i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/refclk
    MMCME2_ADV_X0Y1      MMCME2_ADV (Prop_mmcme2_adv_CLKIN1_CLKOUT2)
                                                      0.073     2.651 r  i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/mmcm_i/CLKOUT2
                         net (fo=1, routed)           1.374     4.024    i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/userclk1
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.077     4.102 r  i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/userclk1_i1.usrclk1_i1/O
                         net (fo=3403, routed)        1.406     5.507    <hidden>
    SLICE_X20Y172        FDRE                                         r  <hidden>
                         clock pessimism              0.000     5.507    
    SLICE_X20Y172        FDRE (Setup_fdre_C_D)       -0.210     5.297    <hidden>
  -------------------------------------------------------------------
                         data arrival                           5.552    
                         clock arrival                          5.297    
  -------------------------------------------------------------------
                         relative delay                         0.255    

Reference path:
    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock net_clk rise edge)    0.000     0.000 r  
    J19                                               0.000     0.000 r  clk (IN)
                         net (fo=0)                   0.000     0.000    clk
    J19                  IBUF (Prop_ibuf_I_O)         1.350     1.350 r  clk_IBUF_inst/O
                         net (fo=1, routed)           1.620     2.970    clk_IBUF
    BUFGCTRL_X0Y16       BUFG (Prop_bufg_I_O)         0.077     3.047 r  clk_IBUF_BUFG_inst/O
                         net (fo=1816, routed)        1.404     4.451    <hidden>
    SLICE_X21Y172        FDRE                                         r  <hidden>
  -------------------------------------------------------------------    -------------------
    SLICE_X21Y172        FDRE (Prop_fdre_C_Q)         0.304     4.755 r  <hidden>
                         net (fo=1, routed)           0.222     4.977    <hidden>
    SLICE_X20Y172        FDRE                                         r  <hidden>
  -------------------------------------------------------------------    -------------------

                         (clock userclk1 rise edge)
                                                      0.000     0.000 r  
    GTPE2_CHANNEL_X0Y7   GTPE2_CHANNEL                0.000     0.000 r  i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_lane[0].gt_wrapper_i/gtp_channel.gtpe2_channel_i/TXOUTCLK
                         net (fo=1, routed)           1.257     1.257    i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/txoutclk_i.txoutclk_i_0
    BUFGCTRL_X0Y19       BUFG (Prop_bufg_I_O)         0.081     1.338 r  i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/txoutclk_i.txoutclk_i/O
                         net (fo=1, routed)           1.408     2.745    i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/refclk
    MMCME2_ADV_X0Y1      MMCME2_ADV (Prop_mmcme2_adv_CLKIN1_CLKOUT2)
                                                      0.077     2.822 r  i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/mmcm_i/CLKOUT2
                         net (fo=1, routed)           1.441     4.263    i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/userclk1
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.081     4.344 r  i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/userclk1_i1.usrclk1_i1/O
                         net (fo=3403, routed)        1.521     5.865    <hidden>
    SLICE_X20Y172        FDRE                                         r  <hidden>
                         clock pessimism              0.000     5.865    
    SLICE_X20Y172        FDRE (Hold_fdre_C_D)         0.136     6.001    <hidden>
  -------------------------------------------------------------------
                         data arrival                           4.977    
                         clock arrival                          6.001    
  -------------------------------------------------------------------
                         relative delay                        -1.024    



Id: 12
set_bus_skew -from [get_cells [list {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_cfgspace_shadow/i_fifo_43_43_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/src_gray_ff_reg[0]} {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_cfgspace_shadow/i_fifo_43_43_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/src_gray_ff_reg[1]} {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_cfgspace_shadow/i_fifo_43_43_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/src_gray_ff_reg[2]} {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_cfgspace_shadow/i_fifo_43_43_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/src_gray_ff_reg[3]}]] -to [get_cells [list {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_cfgspace_shadow/i_fifo_43_43_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/dest_graysync_ff_reg[0][0]} {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_cfgspace_shadow/i_fifo_43_43_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/dest_graysync_ff_reg[0][1]} {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_cfgspace_shadow/i_fifo_43_43_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/dest_graysync_ff_reg[0][2]} {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_cfgspace_shadow/i_fifo_43_43_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/dest_graysync_ff_reg[0][3]}]] 10.000
Requirement: 10.000ns
Endpoints: 4

From Clock            To Clock              Endpoint Pin                    Reference Pin                   Corner  Actual(ns)  Slack(ns)
--------------------  --------------------  ------------------------------  ------------------------------  ------  ----------  ---------
userclk1              net_clk               i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_cfgspace_shadow/i_fifo_43_43_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/dest_graysync_ff_reg[0][1]/D
                                                                            i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_cfgspace_shadow/i_fifo_43_43_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/dest_graysync_ff_reg[0][0]/D
                                                                                                            Slow         0.703      9.297


Slack (MET) :             9.297ns  (requirement - actual skew)
  Endpoint Source:        <hidden>
                            (rising edge-triggered cell FDRE clocked by userclk1)
  Endpoint Destination:   <hidden>
                            (rising edge-triggered cell FDRE clocked by net_clk)
  Reference Source:       <hidden>
                            (rising edge-triggered cell FDRE clocked by userclk1)
  Reference Destination:  <hidden>
                            (rising edge-triggered cell FDRE clocked by net_clk)
  Path Type:              Bus Skew (Max at Slow Process Corner)
  Requirement:            10.000ns
  Endpoint Relative Delay:    2.478ns
  Reference Relative Delay:   1.145ns
  Relative CRPR:              0.631ns
  Actual Bus Skew:            0.703ns  (Endpoint Relative Delay - Reference Relative Delay - Relative CRPR)

Endpoint path:
    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock userclk1 rise edge)
                                                      0.000     0.000 r  
    GTPE2_CHANNEL_X0Y7   GTPE2_CHANNEL                0.000     0.000 r  i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_lane[0].gt_wrapper_i/gtp_channel.gtpe2_channel_i/TXOUTCLK
                         net (fo=1, routed)           1.257     1.257    i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/txoutclk_i.txoutclk_i_0
    BUFGCTRL_X0Y19       BUFG (Prop_bufg_I_O)         0.081     1.338 r  i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/txoutclk_i.txoutclk_i/O
                         net (fo=1, routed)           1.408     2.745    i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/refclk
    MMCME2_ADV_X0Y1      MMCME2_ADV (Prop_mmcme2_adv_CLKIN1_CLKOUT2)
                                                      0.077     2.822 r  i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/mmcm_i/CLKOUT2
                         net (fo=1, routed)           1.441     4.263    i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/userclk1
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.081     4.344 r  i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/userclk1_i1.usrclk1_i1/O
                         net (fo=3403, routed)        1.519     5.863    <hidden>
    SLICE_X18Y174        FDRE                                         r  <hidden>
  -------------------------------------------------------------------    -------------------
    SLICE_X18Y174        FDRE (Prop_fdre_C_Q)         0.348     6.211 r  <hidden>
                         net (fo=1, routed)           0.506     6.717    <hidden>
    SLICE_X19Y174        FDRE                                         r  <hidden>
  -------------------------------------------------------------------    -------------------

                         (clock net_clk rise edge)    0.000     0.000 r  
    J19                                               0.000     0.000 r  clk (IN)
                         net (fo=0)                   0.000     0.000    clk
    J19                  IBUF (Prop_ibuf_I_O)         1.350     1.350 r  clk_IBUF_inst/O
                         net (fo=1, routed)           1.620     2.970    clk_IBUF
    BUFGCTRL_X0Y16       BUFG (Prop_bufg_I_O)         0.077     3.047 r  clk_IBUF_BUFG_inst/O
                         net (fo=1816, routed)        1.402     4.449    <hidden>
    SLICE_X19Y174        FDRE                                         r  <hidden>
                         clock pessimism              0.000     4.449    
    SLICE_X19Y174        FDRE (Setup_fdre_C_D)       -0.210     4.239    <hidden>
  -------------------------------------------------------------------
                         data arrival                           6.717    
                         clock arrival                          4.239    
  -------------------------------------------------------------------
                         relative delay                         2.478    

Reference path:
    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock userclk1 rise edge)
                                                      0.000     0.000 r  
    GTPE2_CHANNEL_X0Y7   GTPE2_CHANNEL                0.000     0.000 r  i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_lane[0].gt_wrapper_i/gtp_channel.gtpe2_channel_i/TXOUTCLK
                         net (fo=1, routed)           1.199     1.199    i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/txoutclk_i.txoutclk_i_0
    BUFGCTRL_X0Y19       BUFG (Prop_bufg_I_O)         0.077     1.276 r  i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/txoutclk_i.txoutclk_i/O
                         net (fo=1, routed)           1.302     2.578    i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/refclk
    MMCME2_ADV_X0Y1      MMCME2_ADV (Prop_mmcme2_adv_CLKIN1_CLKOUT2)
                                                      0.073     2.651 r  i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/mmcm_i/CLKOUT2
                         net (fo=1, routed)           1.374     4.024    i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/userclk1
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.077     4.102 r  i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/userclk1_i1.usrclk1_i1/O
                         net (fo=3403, routed)        1.404     5.505    <hidden>
    SLICE_X18Y174        FDRE                                         r  <hidden>
  -------------------------------------------------------------------    -------------------
    SLICE_X18Y174        FDRE (Prop_fdre_C_Q)         0.304     5.809 r  <hidden>
                         net (fo=1, routed)           0.193     6.002    <hidden>
    SLICE_X19Y174        FDRE                                         r  <hidden>
  -------------------------------------------------------------------    -------------------

                         (clock net_clk rise edge)    0.000     0.000 r  
    J19                                               0.000     0.000 r  clk (IN)
                         net (fo=0)                   0.000     0.000    clk
    J19                  IBUF (Prop_ibuf_I_O)         1.416     1.416 r  clk_IBUF_inst/O
                         net (fo=1, routed)           1.709     3.124    clk_IBUF
    BUFGCTRL_X0Y16       BUFG (Prop_bufg_I_O)         0.081     3.205 r  clk_IBUF_BUFG_inst/O
                         net (fo=1816, routed)        1.516     4.722    <hidden>
    SLICE_X19Y174        FDRE                                         r  <hidden>
                         clock pessimism              0.000     4.722    
    SLICE_X19Y174        FDRE (Hold_fdre_C_D)         0.136     4.858    <hidden>
  -------------------------------------------------------------------
                         data arrival                           6.002    
                         clock arrival                          4.858    
  -------------------------------------------------------------------
                         relative delay                         1.145    



Id: 13
set_bus_skew -from [get_cells [list {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_cfgspace_shadow/i_fifo_49_49_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/src_gray_ff_reg[0]} {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_cfgspace_shadow/i_fifo_49_49_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/src_gray_ff_reg[1]} {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_cfgspace_shadow/i_fifo_49_49_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/src_gray_ff_reg[2]} {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_cfgspace_shadow/i_fifo_49_49_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/src_gray_ff_reg[3]}]] -to [get_cells [list {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_cfgspace_shadow/i_fifo_49_49_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/dest_graysync_ff_reg[0][0]} {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_cfgspace_shadow/i_fifo_49_49_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/dest_graysync_ff_reg[0][1]} {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_cfgspace_shadow/i_fifo_49_49_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/dest_graysync_ff_reg[0][2]} {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_cfgspace_shadow/i_fifo_49_49_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/dest_graysync_ff_reg[0][3]}]] 10.000
Requirement: 10.000ns
Endpoints: 4

From Clock            To Clock              Endpoint Pin                    Reference Pin                   Corner  Actual(ns)  Slack(ns)
--------------------  --------------------  ------------------------------  ------------------------------  ------  ----------  ---------
userclk1              net_clk               i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_cfgspace_shadow/i_fifo_49_49_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/dest_graysync_ff_reg[0][1]/D
                                                                            i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_cfgspace_shadow/i_fifo_49_49_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/dest_graysync_ff_reg[0][3]/D
                                                                                                            Slow         0.512      9.488


Slack (MET) :             9.488ns  (requirement - actual skew)
  Endpoint Source:        <hidden>
                            (rising edge-triggered cell FDRE clocked by userclk1)
  Endpoint Destination:   <hidden>
                            (rising edge-triggered cell FDRE clocked by net_clk)
  Reference Source:       <hidden>
                            (rising edge-triggered cell FDRE clocked by userclk1)
  Reference Destination:  <hidden>
                            (rising edge-triggered cell FDRE clocked by net_clk)
  Path Type:              Bus Skew (Max at Slow Process Corner)
  Requirement:            10.000ns
  Endpoint Relative Delay:    2.322ns
  Reference Relative Delay:   1.260ns
  Relative CRPR:              0.550ns
  Actual Bus Skew:            0.512ns  (Endpoint Relative Delay - Reference Relative Delay - Relative CRPR)

Endpoint path:
    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock userclk1 rise edge)
                                                      0.000     0.000 r  
    GTPE2_CHANNEL_X0Y7   GTPE2_CHANNEL                0.000     0.000 r  i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_lane[0].gt_wrapper_i/gtp_channel.gtpe2_channel_i/TXOUTCLK
                         net (fo=1, routed)           1.257     1.257    i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/txoutclk_i.txoutclk_i_0
    BUFGCTRL_X0Y19       BUFG (Prop_bufg_I_O)         0.081     1.338 r  i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/txoutclk_i.txoutclk_i/O
                         net (fo=1, routed)           1.408     2.745    i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/refclk
    MMCME2_ADV_X0Y1      MMCME2_ADV (Prop_mmcme2_adv_CLKIN1_CLKOUT2)
                                                      0.077     2.822 r  i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/mmcm_i/CLKOUT2
                         net (fo=1, routed)           1.441     4.263    i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/userclk1
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.081     4.344 r  i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/userclk1_i1.usrclk1_i1/O
                         net (fo=3403, routed)        1.359     5.703    <hidden>
    SLICE_X40Y135        FDRE                                         r  <hidden>
  -------------------------------------------------------------------    -------------------
    SLICE_X40Y135        FDRE (Prop_fdre_C_Q)         0.348     6.051 r  <hidden>
                         net (fo=1, routed)           0.355     6.406    <hidden>
    SLICE_X39Y135        FDRE                                         r  <hidden>
  -------------------------------------------------------------------    -------------------

                         (clock net_clk rise edge)    0.000     0.000 r  
    J19                                               0.000     0.000 r  clk (IN)
                         net (fo=0)                   0.000     0.000    clk
    J19                  IBUF (Prop_ibuf_I_O)         1.350     1.350 r  clk_IBUF_inst/O
                         net (fo=1, routed)           1.620     2.970    clk_IBUF
    BUFGCTRL_X0Y16       BUFG (Prop_bufg_I_O)         0.077     3.047 r  clk_IBUF_BUFG_inst/O
                         net (fo=1816, routed)        1.248     4.295    <hidden>
    SLICE_X39Y135        FDRE                                         r  <hidden>
                         clock pessimism              0.000     4.295    
    SLICE_X39Y135        FDRE (Setup_fdre_C_D)       -0.210     4.085    <hidden>
  -------------------------------------------------------------------
                         data arrival                           6.406    
                         clock arrival                          4.085    
  -------------------------------------------------------------------
                         relative delay                         2.322    

Reference path:
    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock userclk1 rise edge)
                                                      0.000     0.000 r  
    GTPE2_CHANNEL_X0Y7   GTPE2_CHANNEL                0.000     0.000 r  i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_lane[0].gt_wrapper_i/gtp_channel.gtpe2_channel_i/TXOUTCLK
                         net (fo=1, routed)           1.199     1.199    i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/txoutclk_i.txoutclk_i_0
    BUFGCTRL_X0Y19       BUFG (Prop_bufg_I_O)         0.077     1.276 r  i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/txoutclk_i.txoutclk_i/O
                         net (fo=1, routed)           1.302     2.578    i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/refclk
    MMCME2_ADV_X0Y1      MMCME2_ADV (Prop_mmcme2_adv_CLKIN1_CLKOUT2)
                                                      0.073     2.651 r  i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/mmcm_i/CLKOUT2
                         net (fo=1, routed)           1.374     4.024    i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/userclk1
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.077     4.102 r  i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/userclk1_i1.usrclk1_i1/O
                         net (fo=3403, routed)        1.253     5.355    <hidden>
    SLICE_X36Y136        FDRE                                         r  <hidden>
  -------------------------------------------------------------------    -------------------
    SLICE_X36Y136        FDRE (Prop_fdre_C_Q)         0.304     5.659 r  <hidden>
                         net (fo=1, routed)           0.301     5.960    <hidden>
    SLICE_X39Y136        FDRE                                         r  <hidden>
  -------------------------------------------------------------------    -------------------

                         (clock net_clk rise edge)    0.000     0.000 r  
    J19                                               0.000     0.000 r  clk (IN)
                         net (fo=0)                   0.000     0.000    clk
    J19                  IBUF (Prop_ibuf_I_O)         1.416     1.416 r  clk_IBUF_inst/O
                         net (fo=1, routed)           1.709     3.124    clk_IBUF
    BUFGCTRL_X0Y16       BUFG (Prop_bufg_I_O)         0.081     3.205 r  clk_IBUF_BUFG_inst/O
                         net (fo=1816, routed)        1.358     4.564    <hidden>
    SLICE_X39Y136        FDRE                                         r  <hidden>
                         clock pessimism              0.000     4.564    
    SLICE_X39Y136        FDRE (Hold_fdre_C_D)         0.136     4.700    <hidden>
  -------------------------------------------------------------------
                         data arrival                           5.960    
                         clock arrival                          4.700    
  -------------------------------------------------------------------
                         relative delay                         1.260    



Id: 14
set_bus_skew -from [get_cells [list {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_cfgspace_shadow/i_fifo_49_49_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/src_gray_ff_reg[0]} {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_cfgspace_shadow/i_fifo_49_49_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/src_gray_ff_reg[1]} {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_cfgspace_shadow/i_fifo_49_49_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/src_gray_ff_reg[2]} {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_cfgspace_shadow/i_fifo_49_49_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/src_gray_ff_reg[3]}]] -to [get_cells [list {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_cfgspace_shadow/i_fifo_49_49_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/dest_graysync_ff_reg[0][0]} {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_cfgspace_shadow/i_fifo_49_49_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/dest_graysync_ff_reg[0][1]} {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_cfgspace_shadow/i_fifo_49_49_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/dest_graysync_ff_reg[0][2]} {i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_cfgspace_shadow/i_fifo_49_49_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/dest_graysync_ff_reg[0][3]}]] 10.000
Requirement: 10.000ns
Endpoints: 4

From Clock            To Clock              Endpoint Pin                    Reference Pin                   Corner  Actual(ns)  Slack(ns)
--------------------  --------------------  ------------------------------  ------------------------------  ------  ----------  ---------
net_clk               userclk1              i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_cfgspace_shadow/i_fifo_49_49_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/dest_graysync_ff_reg[0][1]/D
                                                                            i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_cfgspace_shadow/i_fifo_49_49_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/dest_graysync_ff_reg[0][3]/D
                                                                                                            Slow         0.665      9.335


Slack (MET) :             9.335ns  (requirement - actual skew)
  Endpoint Source:        <hidden>
                            (rising edge-triggered cell FDRE clocked by net_clk)
  Endpoint Destination:   <hidden>
                            (rising edge-triggered cell FDRE clocked by userclk1)
  Reference Source:       <hidden>
                            (rising edge-triggered cell FDRE clocked by net_clk)
  Reference Destination:  <hidden>
                            (rising edge-triggered cell FDRE clocked by userclk1)
  Path Type:              Bus Skew (Max at Slow Process Corner)
  Requirement:            10.000ns
  Endpoint Relative Delay:    0.290ns
  Reference Relative Delay:  -0.908ns
  Relative CRPR:              0.533ns
  Actual Bus Skew:            0.665ns  (Endpoint Relative Delay - Reference Relative Delay - Relative CRPR)

Endpoint path:
    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock net_clk rise edge)    0.000     0.000 r  
    J19                                               0.000     0.000 r  clk (IN)
                         net (fo=0)                   0.000     0.000    clk
    J19                  IBUF (Prop_ibuf_I_O)         1.416     1.416 r  clk_IBUF_inst/O
                         net (fo=1, routed)           1.709     3.124    clk_IBUF
    BUFGCTRL_X0Y16       BUFG (Prop_bufg_I_O)         0.081     3.205 r  clk_IBUF_BUFG_inst/O
                         net (fo=1816, routed)        1.355     4.561    <hidden>
    SLICE_X41Y134        FDRE                                         r  <hidden>
  -------------------------------------------------------------------    -------------------
    SLICE_X41Y134        FDRE (Prop_fdre_C_Q)         0.348     4.909 r  <hidden>
                         net (fo=1, routed)           0.523     5.432    <hidden>
    SLICE_X41Y137        FDRE                                         r  <hidden>
  -------------------------------------------------------------------    -------------------

                         (clock userclk1 rise edge)
                                                      0.000     0.000 r  
    GTPE2_CHANNEL_X0Y7   GTPE2_CHANNEL                0.000     0.000 r  i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_lane[0].gt_wrapper_i/gtp_channel.gtpe2_channel_i/TXOUTCLK
                         net (fo=1, routed)           1.199     1.199    i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/txoutclk_i.txoutclk_i_0
    BUFGCTRL_X0Y19       BUFG (Prop_bufg_I_O)         0.077     1.276 r  i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/txoutclk_i.txoutclk_i/O
                         net (fo=1, routed)           1.302     2.578    i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/refclk
    MMCME2_ADV_X0Y1      MMCME2_ADV (Prop_mmcme2_adv_CLKIN1_CLKOUT2)
                                                      0.073     2.651 r  i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/mmcm_i/CLKOUT2
                         net (fo=1, routed)           1.374     4.024    i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/userclk1
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.077     4.102 r  i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/userclk1_i1.usrclk1_i1/O
                         net (fo=3403, routed)        1.252     5.354    <hidden>
    SLICE_X41Y137        FDRE                                         r  <hidden>
                         clock pessimism              0.000     5.354    
    SLICE_X41Y137        FDRE (Setup_fdre_C_D)       -0.212     5.142    <hidden>
  -------------------------------------------------------------------
                         data arrival                           5.432    
                         clock arrival                          5.142    
  -------------------------------------------------------------------
                         relative delay                         0.290    

Reference path:
    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock net_clk rise edge)    0.000     0.000 r  
    J19                                               0.000     0.000 r  clk (IN)
                         net (fo=0)                   0.000     0.000    clk
    J19                  IBUF (Prop_ibuf_I_O)         1.350     1.350 r  clk_IBUF_inst/O
                         net (fo=1, routed)           1.620     2.970    clk_IBUF
    BUFGCTRL_X0Y16       BUFG (Prop_bufg_I_O)         0.077     3.047 r  clk_IBUF_BUFG_inst/O
                         net (fo=1816, routed)        1.242     4.289    <hidden>
    SLICE_X47Y134        FDRE                                         r  <hidden>
  -------------------------------------------------------------------    -------------------
    SLICE_X47Y134        FDRE (Prop_fdre_C_Q)         0.304     4.593 r  <hidden>
                         net (fo=1, routed)           0.348     4.941    <hidden>
    SLICE_X45Y133        FDRE                                         r  <hidden>
  -------------------------------------------------------------------    -------------------

                         (clock userclk1 rise edge)
                                                      0.000     0.000 r  
    GTPE2_CHANNEL_X0Y7   GTPE2_CHANNEL                0.000     0.000 r  i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_lane[0].gt_wrapper_i/gtp_channel.gtpe2_channel_i/TXOUTCLK
                         net (fo=1, routed)           1.257     1.257    i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/txoutclk_i.txoutclk_i_0
    BUFGCTRL_X0Y19       BUFG (Prop_bufg_I_O)         0.081     1.338 r  i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/txoutclk_i.txoutclk_i/O
                         net (fo=1, routed)           1.408     2.745    i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/refclk
    MMCME2_ADV_X0Y1      MMCME2_ADV (Prop_mmcme2_adv_CLKIN1_CLKOUT2)
                                                      0.077     2.822 r  i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/mmcm_i/CLKOUT2
                         net (fo=1, routed)           1.441     4.263    i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/userclk1
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.081     4.344 r  i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/userclk1_i1.usrclk1_i1/O
                         net (fo=3403, routed)        1.356     5.700    <hidden>
    SLICE_X45Y133        FDRE                                         r  <hidden>
                         clock pessimism              0.000     5.700    
    SLICE_X45Y133        FDRE (Hold_fdre_C_D)         0.148     5.848    <hidden>
  -------------------------------------------------------------------
                         data arrival                           4.941    
                         clock arrival                          5.848    
  -------------------------------------------------------------------
                         relative delay                        -0.908    



Id: 15
set_bus_skew -from [get_cells [list {i_pcileech_com/i_fifo_64_64_clk2_comrx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/src_gray_ff_reg[0]} {i_pcileech_com/i_fifo_64_64_clk2_comrx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/src_gray_ff_reg[1]} {i_pcileech_com/i_fifo_64_64_clk2_comrx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/src_gray_ff_reg[2]} {i_pcileech_com/i_fifo_64_64_clk2_comrx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/src_gray_ff_reg[3]}]] -to [get_cells [list {i_pcileech_com/i_fifo_64_64_clk2_comrx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/dest_graysync_ff_reg[0][0]} {i_pcileech_com/i_fifo_64_64_clk2_comrx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/dest_graysync_ff_reg[0][1]} {i_pcileech_com/i_fifo_64_64_clk2_comrx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/dest_graysync_ff_reg[0][2]} {i_pcileech_com/i_fifo_64_64_clk2_comrx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/dest_graysync_ff_reg[0][3]}]] 10.000
Requirement: 10.000ns
Endpoints: 4

From Clock            To Clock              Endpoint Pin                    Reference Pin                   Corner  Actual(ns)  Slack(ns)
--------------------  --------------------  ------------------------------  ------------------------------  ------  ----------  ---------
net_clk               net_ft601_clk         i_pcileech_com/i_fifo_64_64_clk2_comrx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/dest_graysync_ff_reg[0][3]/D
                                                                            i_pcileech_com/i_fifo_64_64_clk2_comrx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/dest_graysync_ff_reg[0][2]/D
                                                                                                            Slow         0.715      9.285


Slack (MET) :             9.285ns  (requirement - actual skew)
  Endpoint Source:        <hidden>
                            (rising edge-triggered cell FDRE clocked by net_clk)
  Endpoint Destination:   <hidden>
                            (rising edge-triggered cell FDRE clocked by net_ft601_clk)
  Reference Source:       <hidden>
                            (rising edge-triggered cell FDRE clocked by net_clk)
  Reference Destination:  <hidden>
                            (rising edge-triggered cell FDRE clocked by net_ft601_clk)
  Path Type:              Bus Skew (Max at Slow Process Corner)
  Requirement:            10.000ns
  Endpoint Relative Delay:    1.323ns
  Reference Relative Delay:   0.083ns
  Relative CRPR:              0.526ns
  Actual Bus Skew:            0.715ns  (Endpoint Relative Delay - Reference Relative Delay - Relative CRPR)

Endpoint path:
    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock net_clk rise edge)    0.000     0.000 r  
    J19                                               0.000     0.000 r  clk (IN)
                         net (fo=0)                   0.000     0.000    clk
    J19                  IBUF (Prop_ibuf_I_O)         1.416     1.416 r  clk_IBUF_inst/O
                         net (fo=1, routed)           1.709     3.124    clk_IBUF
    BUFGCTRL_X0Y16       BUFG (Prop_bufg_I_O)         0.081     3.205 r  clk_IBUF_BUFG_inst/O
                         net (fo=1816, routed)        1.533     4.739    <hidden>
    SLICE_X26Y150        FDRE                                         r  <hidden>
  -------------------------------------------------------------------    -------------------
    SLICE_X26Y150        FDRE (Prop_fdre_C_Q)         0.348     5.087 r  <hidden>
                         net (fo=1, routed)           0.493     5.580    <hidden>
    SLICE_X24Y151        FDRE                                         r  <hidden>
  -------------------------------------------------------------------    -------------------

                         (clock net_ft601_clk rise edge)
                                                      0.000     0.000 r  
    K18                                               0.000     0.000 r  ft601_clk (IN)
                         net (fo=0)                   0.000     0.000    ft601_clk
    K18                  IBUF (Prop_ibuf_I_O)         1.353     1.353 r  ft601_clk_IBUF_inst/O
                         net (fo=1, routed)           1.620     2.974    ft601_clk_IBUF
    BUFGCTRL_X0Y17       BUFG (Prop_bufg_I_O)         0.077     3.051 r  ft601_clk_IBUF_BUFG_inst/O
                         net (fo=411, routed)         1.415     4.465    <hidden>
    SLICE_X24Y151        FDRE                                         r  <hidden>
                         clock pessimism              0.000     4.465    
    SLICE_X24Y151        FDRE (Setup_fdre_C_D)       -0.209     4.256    <hidden>
  -------------------------------------------------------------------
                         data arrival                           5.580    
                         clock arrival                          4.256    
  -------------------------------------------------------------------
                         relative delay                         1.323    

Reference path:
    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock net_clk rise edge)    0.000     0.000 r  
    J19                                               0.000     0.000 r  clk (IN)
                         net (fo=0)                   0.000     0.000    clk
    J19                  IBUF (Prop_ibuf_I_O)         1.350     1.350 r  clk_IBUF_inst/O
                         net (fo=1, routed)           1.620     2.970    clk_IBUF
    BUFGCTRL_X0Y16       BUFG (Prop_bufg_I_O)         0.077     3.047 r  clk_IBUF_BUFG_inst/O
                         net (fo=1816, routed)        1.415     4.462    <hidden>
    SLICE_X26Y150        FDRE                                         r  <hidden>
  -------------------------------------------------------------------    -------------------
    SLICE_X26Y150        FDRE (Prop_fdre_C_Q)         0.304     4.766 r  <hidden>
                         net (fo=1, routed)           0.197     4.963    <hidden>
    SLICE_X24Y150        FDRE                                         r  <hidden>
  -------------------------------------------------------------------    -------------------

                         (clock net_ft601_clk rise edge)
                                                      0.000     0.000 r  
    K18                                               0.000     0.000 r  ft601_clk (IN)
                         net (fo=0)                   0.000     0.000    ft601_clk
    K18                  IBUF (Prop_ibuf_I_O)         1.419     1.419 r  ft601_clk_IBUF_inst/O
                         net (fo=1, routed)           1.709     3.128    ft601_clk_IBUF
    BUFGCTRL_X0Y17       BUFG (Prop_bufg_I_O)         0.081     3.209 r  ft601_clk_IBUF_BUFG_inst/O
                         net (fo=411, routed)         1.533     4.742    <hidden>
    SLICE_X24Y150        FDRE                                         r  <hidden>
                         clock pessimism              0.000     4.742    
    SLICE_X24Y150        FDRE (Hold_fdre_C_D)         0.138     4.880    <hidden>
  -------------------------------------------------------------------
                         data arrival                           4.963    
                         clock arrival                          4.880    
  -------------------------------------------------------------------
                         relative delay                         0.083    



Id: 16
set_bus_skew -from [get_cells [list {i_pcileech_com/i_fifo_64_64_clk2_comrx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/src_gray_ff_reg[0]} {i_pcileech_com/i_fifo_64_64_clk2_comrx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/src_gray_ff_reg[1]} {i_pcileech_com/i_fifo_64_64_clk2_comrx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/src_gray_ff_reg[2]} {i_pcileech_com/i_fifo_64_64_clk2_comrx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/src_gray_ff_reg[3]}]] -to [get_cells [list {i_pcileech_com/i_fifo_64_64_clk2_comrx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/dest_graysync_ff_reg[0][0]} {i_pcileech_com/i_fifo_64_64_clk2_comrx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/dest_graysync_ff_reg[0][1]} {i_pcileech_com/i_fifo_64_64_clk2_comrx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/dest_graysync_ff_reg[0][2]} {i_pcileech_com/i_fifo_64_64_clk2_comrx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/dest_graysync_ff_reg[0][3]}]] 10.000
Requirement: 10.000ns
Endpoints: 4

From Clock            To Clock              Endpoint Pin                    Reference Pin                   Corner  Actual(ns)  Slack(ns)
--------------------  --------------------  ------------------------------  ------------------------------  ------  ----------  ---------
net_ft601_clk         net_clk               i_pcileech_com/i_fifo_64_64_clk2_comrx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/dest_graysync_ff_reg[0][3]/D
                                                                            i_pcileech_com/i_fifo_64_64_clk2_comrx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/dest_graysync_ff_reg[0][2]/D
                                                                                                            Slow         0.651      9.349


Slack (MET) :             9.349ns  (requirement - actual skew)
  Endpoint Source:        <hidden>
                            (rising edge-triggered cell FDRE clocked by net_ft601_clk)
  Endpoint Destination:   <hidden>
                            (rising edge-triggered cell FDRE clocked by net_clk)
  Reference Source:       <hidden>
                            (rising edge-triggered cell FDRE clocked by net_ft601_clk)
  Reference Destination:  <hidden>
                            (rising edge-triggered cell FDRE clocked by net_clk)
  Path Type:              Bus Skew (Max at Slow Process Corner)
  Requirement:            10.000ns
  Endpoint Relative Delay:    1.326ns
  Reference Relative Delay:   0.194ns
  Relative CRPR:              0.481ns
  Actual Bus Skew:            0.651ns  (Endpoint Relative Delay - Reference Relative Delay - Relative CRPR)

Endpoint path:
    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock net_ft601_clk rise edge)
                                                      0.000     0.000 r  
    K18                                               0.000     0.000 r  ft601_clk (IN)
                         net (fo=0)                   0.000     0.000    ft601_clk
    K18                  IBUF (Prop_ibuf_I_O)         1.419     1.419 r  ft601_clk_IBUF_inst/O
                         net (fo=1, routed)           1.709     3.128    ft601_clk_IBUF
    BUFGCTRL_X0Y17       BUFG (Prop_bufg_I_O)         0.081     3.209 r  ft601_clk_IBUF_BUFG_inst/O
                         net (fo=411, routed)         1.533     4.742    <hidden>
    SLICE_X25Y152        FDRE                                         r  <hidden>
  -------------------------------------------------------------------    -------------------
    SLICE_X25Y152        FDRE (Prop_fdre_C_Q)         0.379     5.121 r  <hidden>
                         net (fo=1, routed)           0.594     5.715    <hidden>
    SLICE_X24Y152        FDRE                                         r  <hidden>
  -------------------------------------------------------------------    -------------------

                         (clock net_clk rise edge)    0.000     0.000 r  
    J19                                               0.000     0.000 r  clk (IN)
                         net (fo=0)                   0.000     0.000    clk
    J19                  IBUF (Prop_ibuf_I_O)         1.350     1.350 r  clk_IBUF_inst/O
                         net (fo=1, routed)           1.620     2.970    clk_IBUF
    BUFGCTRL_X0Y16       BUFG (Prop_bufg_I_O)         0.077     3.047 r  clk_IBUF_BUFG_inst/O
                         net (fo=1816, routed)        1.415     4.462    <hidden>
    SLICE_X24Y152        FDRE                                         r  <hidden>
                         clock pessimism              0.000     4.462    
    SLICE_X24Y152        FDRE (Setup_fdre_C_D)       -0.073     4.389    <hidden>
  -------------------------------------------------------------------
                         data arrival                           5.715    
                         clock arrival                          4.389    
  -------------------------------------------------------------------
                         relative delay                         1.326    

Reference path:
    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock net_ft601_clk rise edge)
                                                      0.000     0.000 r  
    K18                                               0.000     0.000 r  ft601_clk (IN)
                         net (fo=0)                   0.000     0.000    ft601_clk
    K18                  IBUF (Prop_ibuf_I_O)         1.353     1.353 r  ft601_clk_IBUF_inst/O
                         net (fo=1, routed)           1.620     2.974    ft601_clk_IBUF
    BUFGCTRL_X0Y17       BUFG (Prop_bufg_I_O)         0.077     3.051 r  ft601_clk_IBUF_BUFG_inst/O
                         net (fo=411, routed)         1.415     4.465    <hidden>
    SLICE_X24Y150        FDRE                                         r  <hidden>
  -------------------------------------------------------------------    -------------------
    SLICE_X24Y150        FDRE (Prop_fdre_C_Q)         0.304     4.769 r  <hidden>
                         net (fo=1, routed)           0.302     5.072    <hidden>
    SLICE_X23Y150        FDRE                                         r  <hidden>
  -------------------------------------------------------------------    -------------------

                         (clock net_clk rise edge)    0.000     0.000 r  
    J19                                               0.000     0.000 r  clk (IN)
                         net (fo=0)                   0.000     0.000    clk
    J19                  IBUF (Prop_ibuf_I_O)         1.416     1.416 r  clk_IBUF_inst/O
                         net (fo=1, routed)           1.709     3.124    clk_IBUF
    BUFGCTRL_X0Y16       BUFG (Prop_bufg_I_O)         0.081     3.205 r  clk_IBUF_BUFG_inst/O
                         net (fo=1816, routed)        1.534     4.740    <hidden>
    SLICE_X23Y150        FDRE                                         r  <hidden>
                         clock pessimism              0.000     4.740    
    SLICE_X23Y150        FDRE (Hold_fdre_C_D)         0.138     4.878    <hidden>
  -------------------------------------------------------------------
                         data arrival                           5.072    
                         clock arrival                          4.878    
  -------------------------------------------------------------------
                         relative delay                         0.194    



Id: 17
set_bus_skew -from [get_cells [list {i_pcileech_pcie_a7/i_pcileech_pcie_cfg_a7/i_fifo_pcie_cfg_tx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/src_gray_ff_reg[0]} {i_pcileech_pcie_a7/i_pcileech_pcie_cfg_a7/i_fifo_pcie_cfg_tx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/src_gray_ff_reg[1]} {i_pcileech_pcie_a7/i_pcileech_pcie_cfg_a7/i_fifo_pcie_cfg_tx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/src_gray_ff_reg[2]} {i_pcileech_pcie_a7/i_pcileech_pcie_cfg_a7/i_fifo_pcie_cfg_tx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/src_gray_ff_reg[3]} {i_pcileech_pcie_a7/i_pcileech_pcie_cfg_a7/i_fifo_pcie_cfg_tx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/src_gray_ff_reg[4]} {i_pcileech_pcie_a7/i_pcileech_pcie_cfg_a7/i_fifo_pcie_cfg_tx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/src_gray_ff_reg[5]} {i_pcileech_pcie_a7/i_pcileech_pcie_cfg_a7/i_fifo_pcie_cfg_tx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/src_gray_ff_reg[6]} {i_pcileech_pcie_a7/i_pcileech_pcie_cfg_a7/i_fifo_pcie_cfg_tx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/src_gray_ff_reg[7]}]] -to [get_cells [list {i_pcileech_pcie_a7/i_pcileech_pcie_cfg_a7/i_fifo_pcie_cfg_tx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/dest_graysync_ff_reg[0][0]} {i_pcileech_pcie_a7/i_pcileech_pcie_cfg_a7/i_fifo_pcie_cfg_tx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/dest_graysync_ff_reg[0][1]} {i_pcileech_pcie_a7/i_pcileech_pcie_cfg_a7/i_fifo_pcie_cfg_tx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/dest_graysync_ff_reg[0][2]} {i_pcileech_pcie_a7/i_pcileech_pcie_cfg_a7/i_fifo_pcie_cfg_tx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/dest_graysync_ff_reg[0][3]} {i_pcileech_pcie_a7/i_pcileech_pcie_cfg_a7/i_fifo_pcie_cfg_tx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/dest_graysync_ff_reg[0][4]} {i_pcileech_pcie_a7/i_pcileech_pcie_cfg_a7/i_fifo_pcie_cfg_tx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/dest_graysync_ff_reg[0][5]} {i_pcileech_pcie_a7/i_pcileech_pcie_cfg_a7/i_fifo_pcie_cfg_tx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/dest_graysync_ff_reg[0][6]} {i_pcileech_pcie_a7/i_pcileech_pcie_cfg_a7/i_fifo_pcie_cfg_tx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/dest_graysync_ff_reg[0][7]}]] 10.000
Requirement: 10.000ns
Endpoints: 8

From Clock            To Clock              Endpoint Pin                    Reference Pin                   Corner  Actual(ns)  Slack(ns)
--------------------  --------------------  ------------------------------  ------------------------------  ------  ----------  ---------
userclk1              net_clk               i_pcileech_pcie_a7/i_pcileech_pcie_cfg_a7/i_fifo_pcie_cfg_tx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/dest_graysync_ff_reg[0][0]/D
                                                                            i_pcileech_pcie_a7/i_pcileech_pcie_cfg_a7/i_fifo_pcie_cfg_tx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/rd_pntr_cdc_inst/dest_graysync_ff_reg[0][4]/D
                                                                                                            Slow         0.990      9.010


Slack (MET) :             9.010ns  (requirement - actual skew)
  Endpoint Source:        <hidden>
                            (rising edge-triggered cell FDRE clocked by userclk1)
  Endpoint Destination:   <hidden>
                            (rising edge-triggered cell FDRE clocked by net_clk)
  Reference Source:       <hidden>
                            (rising edge-triggered cell FDRE clocked by userclk1)
  Reference Destination:  <hidden>
                            (rising edge-triggered cell FDRE clocked by net_clk)
  Path Type:              Bus Skew (Max at Slow Process Corner)
  Requirement:            10.000ns
  Endpoint Relative Delay:    2.764ns
  Reference Relative Delay:   1.268ns
  Relative CRPR:              0.507ns
  Actual Bus Skew:            0.990ns  (Endpoint Relative Delay - Reference Relative Delay - Relative CRPR)

Endpoint path:
    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock userclk1 rise edge)
                                                      0.000     0.000 r  
    GTPE2_CHANNEL_X0Y7   GTPE2_CHANNEL                0.000     0.000 r  i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_lane[0].gt_wrapper_i/gtp_channel.gtpe2_channel_i/TXOUTCLK
                         net (fo=1, routed)           1.257     1.257    i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/txoutclk_i.txoutclk_i_0
    BUFGCTRL_X0Y19       BUFG (Prop_bufg_I_O)         0.081     1.338 r  i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/txoutclk_i.txoutclk_i/O
                         net (fo=1, routed)           1.408     2.745    i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/refclk
    MMCME2_ADV_X0Y1      MMCME2_ADV (Prop_mmcme2_adv_CLKIN1_CLKOUT2)
                                                      0.077     2.822 r  i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/mmcm_i/CLKOUT2
                         net (fo=1, routed)           1.441     4.263    i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/userclk1
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.081     4.344 r  i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/userclk1_i1.usrclk1_i1/O
                         net (fo=3403, routed)        1.518     5.862    <hidden>
    SLICE_X52Y154        FDRE                                         r  <hidden>
  -------------------------------------------------------------------    -------------------
    SLICE_X52Y154        FDRE (Prop_fdre_C_Q)         0.379     6.241 r  <hidden>
                         net (fo=1, routed)           0.909     7.151    <hidden>
    SLICE_X27Y154        FDRE                                         r  <hidden>
  -------------------------------------------------------------------    -------------------

                         (clock net_clk rise edge)    0.000     0.000 r  
    J19                                               0.000     0.000 r  clk (IN)
                         net (fo=0)                   0.000     0.000    clk
    J19                  IBUF (Prop_ibuf_I_O)         1.350     1.350 r  clk_IBUF_inst/O
                         net (fo=1, routed)           1.620     2.970    clk_IBUF
    BUFGCTRL_X0Y16       BUFG (Prop_bufg_I_O)         0.077     3.047 r  clk_IBUF_BUFG_inst/O
                         net (fo=1816, routed)        1.415     4.462    <hidden>
    SLICE_X27Y154        FDRE                                         r  <hidden>
                         clock pessimism              0.000     4.462    
    SLICE_X27Y154        FDRE (Setup_fdre_C_D)       -0.075     4.387    <hidden>
  -------------------------------------------------------------------
                         data arrival                           7.151    
                         clock arrival                          4.387    
  -------------------------------------------------------------------
                         relative delay                         2.764    

Reference path:
    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock userclk1 rise edge)
                                                      0.000     0.000 r  
    GTPE2_CHANNEL_X0Y7   GTPE2_CHANNEL                0.000     0.000 r  i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_lane[0].gt_wrapper_i/gtp_channel.gtpe2_channel_i/TXOUTCLK
                         net (fo=1, routed)           1.199     1.199    i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/txoutclk_i.txoutclk_i_0
    BUFGCTRL_X0Y19       BUFG (Prop_bufg_I_O)         0.077     1.276 r  i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/txoutclk_i.txoutclk_i/O
                         net (fo=1, routed)           1.302     2.578    i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/refclk
    MMCME2_ADV_X0Y1      MMCME2_ADV (Prop_mmcme2_adv_CLKIN1_CLKOUT2)
                                                      0.073     2.651 r  i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/mmcm_i/CLKOUT2
                         net (fo=1, routed)           1.374     4.024    i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/userclk1
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.077     4.102 r  i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/userclk1_i1.usrclk1_i1/O
                         net (fo=3403, routed)        1.413     5.514    <hidden>
    SLICE_X33Y153        FDRE                                         r  <hidden>
  -------------------------------------------------------------------    -------------------
    SLICE_X33Y153        FDRE (Prop_fdre_C_Q)         0.304     5.818 r  <hidden>
                         net (fo=1, routed)           0.324     6.142    <hidden>
    SLICE_X26Y152        FDRE                                         r  <hidden>
  -------------------------------------------------------------------    -------------------

                         (clock net_clk rise edge)    0.000     0.000 r  
    J19                                               0.000     0.000 r  clk (IN)
                         net (fo=0)                   0.000     0.000    clk
    J19                  IBUF (Prop_ibuf_I_O)         1.416     1.416 r  clk_IBUF_inst/O
                         net (fo=1, routed)           1.709     3.124    clk_IBUF
    BUFGCTRL_X0Y16       BUFG (Prop_bufg_I_O)         0.081     3.205 r  clk_IBUF_BUFG_inst/O
                         net (fo=1816, routed)        1.533     4.739    <hidden>
    SLICE_X26Y152        FDRE                                         r  <hidden>
                         clock pessimism              0.000     4.739    
    SLICE_X26Y152        FDRE (Hold_fdre_C_D)         0.136     4.875    <hidden>
  -------------------------------------------------------------------
                         data arrival                           6.142    
                         clock arrival                          4.875    
  -------------------------------------------------------------------
                         relative delay                         1.268    



Id: 18
set_bus_skew -from [get_cells [list {i_pcileech_pcie_a7/i_pcileech_pcie_cfg_a7/i_fifo_pcie_cfg_tx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/src_gray_ff_reg[0]} {i_pcileech_pcie_a7/i_pcileech_pcie_cfg_a7/i_fifo_pcie_cfg_tx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/src_gray_ff_reg[1]} {i_pcileech_pcie_a7/i_pcileech_pcie_cfg_a7/i_fifo_pcie_cfg_tx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/src_gray_ff_reg[2]} {i_pcileech_pcie_a7/i_pcileech_pcie_cfg_a7/i_fifo_pcie_cfg_tx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/src_gray_ff_reg[3]} {i_pcileech_pcie_a7/i_pcileech_pcie_cfg_a7/i_fifo_pcie_cfg_tx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/src_gray_ff_reg[4]} {i_pcileech_pcie_a7/i_pcileech_pcie_cfg_a7/i_fifo_pcie_cfg_tx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/src_gray_ff_reg[5]} {i_pcileech_pcie_a7/i_pcileech_pcie_cfg_a7/i_fifo_pcie_cfg_tx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/src_gray_ff_reg[6]} {i_pcileech_pcie_a7/i_pcileech_pcie_cfg_a7/i_fifo_pcie_cfg_tx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/src_gray_ff_reg[7]}]] -to [get_cells [list {i_pcileech_pcie_a7/i_pcileech_pcie_cfg_a7/i_fifo_pcie_cfg_tx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/dest_graysync_ff_reg[0][0]} {i_pcileech_pcie_a7/i_pcileech_pcie_cfg_a7/i_fifo_pcie_cfg_tx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/dest_graysync_ff_reg[0][1]} {i_pcileech_pcie_a7/i_pcileech_pcie_cfg_a7/i_fifo_pcie_cfg_tx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/dest_graysync_ff_reg[0][2]} {i_pcileech_pcie_a7/i_pcileech_pcie_cfg_a7/i_fifo_pcie_cfg_tx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/dest_graysync_ff_reg[0][3]} {i_pcileech_pcie_a7/i_pcileech_pcie_cfg_a7/i_fifo_pcie_cfg_tx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/dest_graysync_ff_reg[0][4]} {i_pcileech_pcie_a7/i_pcileech_pcie_cfg_a7/i_fifo_pcie_cfg_tx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/dest_graysync_ff_reg[0][5]} {i_pcileech_pcie_a7/i_pcileech_pcie_cfg_a7/i_fifo_pcie_cfg_tx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/dest_graysync_ff_reg[0][6]} {i_pcileech_pcie_a7/i_pcileech_pcie_cfg_a7/i_fifo_pcie_cfg_tx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/dest_graysync_ff_reg[0][7]}]] 10.000
Requirement: 10.000ns
Endpoints: 8

From Clock            To Clock              Endpoint Pin                    Reference Pin                   Corner  Actual(ns)  Slack(ns)
--------------------  --------------------  ------------------------------  ------------------------------  ------  ----------  ---------
net_clk               userclk1              i_pcileech_pcie_a7/i_pcileech_pcie_cfg_a7/i_fifo_pcie_cfg_tx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/dest_graysync_ff_reg[0][7]/D
                                                                            i_pcileech_pcie_a7/i_pcileech_pcie_cfg_a7/i_fifo_pcie_cfg_tx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/wr_pntr_cdc_inst/dest_graysync_ff_reg[0][1]/D
                                                                                                            Slow         0.705      9.295


Slack (MET) :             9.295ns  (requirement - actual skew)
  Endpoint Source:        <hidden>
                            (rising edge-triggered cell FDRE clocked by net_clk)
  Endpoint Destination:   <hidden>
                            (rising edge-triggered cell FDRE clocked by userclk1)
  Reference Source:       <hidden>
                            (rising edge-triggered cell FDRE clocked by net_clk)
  Reference Destination:  <hidden>
                            (rising edge-triggered cell FDRE clocked by userclk1)
  Path Type:              Bus Skew (Max at Slow Process Corner)
  Requirement:            10.000ns
  Endpoint Relative Delay:    0.289ns
  Reference Relative Delay:  -0.964ns
  Relative CRPR:              0.549ns
  Actual Bus Skew:            0.705ns  (Endpoint Relative Delay - Reference Relative Delay - Relative CRPR)

Endpoint path:
    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock net_clk rise edge)    0.000     0.000 r  
    J19                                               0.000     0.000 r  clk (IN)
                         net (fo=0)                   0.000     0.000    clk
    J19                  IBUF (Prop_ibuf_I_O)         1.416     1.416 r  clk_IBUF_inst/O
                         net (fo=1, routed)           1.709     3.124    clk_IBUF
    BUFGCTRL_X0Y16       BUFG (Prop_bufg_I_O)         0.081     3.205 r  clk_IBUF_BUFG_inst/O
                         net (fo=1816, routed)        1.527     4.733    <hidden>
    SLICE_X41Y153        FDRE                                         r  <hidden>
  -------------------------------------------------------------------    -------------------
    SLICE_X41Y153        FDRE (Prop_fdre_C_Q)         0.348     5.081 r  <hidden>
                         net (fo=1, routed)           0.510     5.591    <hidden>
    SLICE_X45Y152        FDRE                                         r  <hidden>
  -------------------------------------------------------------------    -------------------

                         (clock userclk1 rise edge)
                                                      0.000     0.000 r  
    GTPE2_CHANNEL_X0Y7   GTPE2_CHANNEL                0.000     0.000 r  i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_lane[0].gt_wrapper_i/gtp_channel.gtpe2_channel_i/TXOUTCLK
                         net (fo=1, routed)           1.199     1.199    i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/txoutclk_i.txoutclk_i_0
    BUFGCTRL_X0Y19       BUFG (Prop_bufg_I_O)         0.077     1.276 r  i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/txoutclk_i.txoutclk_i/O
                         net (fo=1, routed)           1.302     2.578    i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/refclk
    MMCME2_ADV_X0Y1      MMCME2_ADV (Prop_mmcme2_adv_CLKIN1_CLKOUT2)
                                                      0.073     2.651 r  i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/mmcm_i/CLKOUT2
                         net (fo=1, routed)           1.374     4.024    i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/userclk1
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.077     4.102 r  i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/userclk1_i1.usrclk1_i1/O
                         net (fo=3403, routed)        1.409     5.510    <hidden>
    SLICE_X45Y152        FDRE                                         r  <hidden>
                         clock pessimism              0.000     5.510    
    SLICE_X45Y152        FDRE (Setup_fdre_C_D)       -0.209     5.301    <hidden>
  -------------------------------------------------------------------
                         data arrival                           5.591    
                         clock arrival                          5.301    
  -------------------------------------------------------------------
                         relative delay                         0.289    

Reference path:
    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock net_clk rise edge)    0.000     0.000 r  
    J19                                               0.000     0.000 r  clk (IN)
                         net (fo=0)                   0.000     0.000    clk
    J19                  IBUF (Prop_ibuf_I_O)         1.350     1.350 r  clk_IBUF_inst/O
                         net (fo=1, routed)           1.620     2.970    clk_IBUF
    BUFGCTRL_X0Y16       BUFG (Prop_bufg_I_O)         0.077     3.047 r  clk_IBUF_BUFG_inst/O
                         net (fo=1816, routed)        1.407     4.454    <hidden>
    SLICE_X44Y154        FDRE                                         r  <hidden>
  -------------------------------------------------------------------    -------------------
    SLICE_X44Y154        FDRE (Prop_fdre_C_Q)         0.279     4.733 r  <hidden>
                         net (fo=1, routed)           0.202     4.935    <hidden>
    SLICE_X48Y154        FDRE                                         r  <hidden>
  -------------------------------------------------------------------    -------------------

                         (clock userclk1 rise edge)
                                                      0.000     0.000 r  
    GTPE2_CHANNEL_X0Y7   GTPE2_CHANNEL                0.000     0.000 r  i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_lane[0].gt_wrapper_i/gtp_channel.gtpe2_channel_i/TXOUTCLK
                         net (fo=1, routed)           1.257     1.257    i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/txoutclk_i.txoutclk_i_0
    BUFGCTRL_X0Y19       BUFG (Prop_bufg_I_O)         0.081     1.338 r  i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/txoutclk_i.txoutclk_i/O
                         net (fo=1, routed)           1.408     2.745    i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/refclk
    MMCME2_ADV_X0Y1      MMCME2_ADV (Prop_mmcme2_adv_CLKIN1_CLKOUT2)
                                                      0.077     2.822 r  i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/mmcm_i/CLKOUT2
                         net (fo=1, routed)           1.441     4.263    i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/userclk1
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.081     4.344 r  i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/userclk1_i1.usrclk1_i1/O
                         net (fo=3403, routed)        1.527     5.871    <hidden>
    SLICE_X48Y154        FDRE                                         r  <hidden>
                         clock pessimism              0.000     5.871    
    SLICE_X48Y154        FDRE (Hold_fdre_C_D)         0.028     5.899    <hidden>
  -------------------------------------------------------------------
                         data arrival                           4.935    
                         clock arrival                          5.899    
  -------------------------------------------------------------------
                         relative delay                        -0.964    



