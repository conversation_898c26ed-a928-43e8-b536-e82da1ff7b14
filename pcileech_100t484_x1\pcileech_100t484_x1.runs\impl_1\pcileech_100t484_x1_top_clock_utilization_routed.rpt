Copyright 1986-2022 Xilinx, Inc. All Rights Reserved. Copyright 2022-2025 Advanced Micro Devices, Inc. All Rights Reserved.
---------------------------------------------------------------------------------------------------------------------------------------------
| Tool Version : Vivado v.2024.2.2 (win64) Build 6060944 Thu Mar 06 19:10:01 MST 2025
| Date         : Wed Jun 18 17:38:28 2025
| Host         : DESKTOP-7T6N58Q running 64-bit major release  (build 9200)
| Command      : report_clock_utilization -file pcileech_100t484_x1_top_clock_utilization_routed.rpt
| Design       : pcileech_100t484_x1_top
| Device       : 7a100t-fgg484
| Speed File   : -2  PRODUCTION 1.23 2018-06-13
| Design State : Routed
---------------------------------------------------------------------------------------------------------------------------------------------

Clock Utilization Report

Table of Contents
-----------------
1. Clock Primitive Utilization
2. Global Clock Resources
3. Global Clock Source Details
4. Local Clock Details
5. Clock Regions: Key Resource Utilization
6. Clock Regions : Global Clock Summary
7. Device Cell Placement Summary for Global Clock g0
8. Device Cell Placement Summary for Global Clock g1
9. Device Cell Placement Summary for Global Clock g2
10. Device Cell Placement Summary for Global Clock g3
11. Device Cell Placement Summary for Global Clock g4
12. Device Cell Placement Summary for Global Clock g5
13. Device Cell Placement Summary for Global Clock g6
14. Clock Region Cell Placement per Global Clock: Region X0Y1
15. Clock Region Cell Placement per Global Clock: Region X0Y2
16. Clock Region Cell Placement per Global Clock: Region X1Y2
17. Clock Region Cell Placement per Global Clock: Region X0Y3
18. Clock Region Cell Placement per Global Clock: Region X1Y3

1. Clock Primitive Utilization
------------------------------

+----------+------+-----------+-----+--------------+--------+
| Type     | Used | Available | LOC | Clock Region | Pblock |
+----------+------+-----------+-----+--------------+--------+
| BUFGCTRL |    7 |        32 |   0 |            0 |      0 |
| BUFH     |    0 |        96 |   0 |            0 |      0 |
| BUFIO    |    0 |        24 |   0 |            0 |      0 |
| BUFMR    |    0 |        12 |   0 |            0 |      0 |
| BUFR     |    0 |        24 |   0 |            0 |      0 |
| MMCM     |    1 |         6 |   0 |            0 |      0 |
| PLL      |    0 |         6 |   0 |            0 |      0 |
+----------+------+-----------+-----+--------------+--------+


2. Global Clock Resources
-------------------------

+-----------+-----------+-----------------+------------+----------------+--------------+-------------------+-------------+-----------------+--------------+-----------------+-------------------------------------------------------------------------------------------------------------------------+-------------------------------------------------------------------------------------------------------------------------+
| Global Id | Source Id | Driver Type/Pin | Constraint | Site           | Clock Region | Load Clock Region | Clock Loads | Non-Clock Loads | Clock Period | Clock           | Driver Pin                                                                                                              | Net                                                                                                                     |
+-----------+-----------+-----------------+------------+----------------+--------------+-------------------+-------------+-----------------+--------------+-----------------+-------------------------------------------------------------------------------------------------------------------------+-------------------------------------------------------------------------------------------------------------------------+
| g0        | src0      | BUFG/O          | None       | BUFGCTRL_X0Y0  | n/a          |                 4 |        2959 |               0 |       16.000 | userclk1        | i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/userclk1_i1.usrclk1_i1/O   | i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/user_clk                   |
| g1        | src1      | BUFG/O          | None       | BUFGCTRL_X0Y16 | n/a          |                 5 |        1488 |               0 |       10.000 | net_clk         | clk_IBUF_BUFG_inst/O                                                                                                    | clk_IBUF_BUFG                                                                                                           |
| g2        | src2      | BUFGCTRL/O      | None       | BUFGCTRL_X0Y1  | n/a          |                 1 |         341 |               0 |        8.000 | clk_125mhz_x0y0 | i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/pclk_i1_bufgctrl.pclk_i1/O | i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/pclk_i1_bufgctrl.pclk_i1_0 |
| g2        | src3      | BUFGCTRL/O      | None       | BUFGCTRL_X0Y1  | n/a          |                 1 |         341 |               0 |        8.000 | clk_250mhz_x0y0 | i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/pclk_i1_bufgctrl.pclk_i1/O | i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/pclk_i1_bufgctrl.pclk_i1_0 |
| g3        | src2      | BUFG/O          | None       | BUFGCTRL_X0Y2  | n/a          |                 1 |         145 |               0 |        8.000 | clk_125mhz_x0y0 | i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/dclk_i_bufg.dclk_i/O       | i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/CLK                        |
| g4        | src4      | BUFG/O          | None       | BUFGCTRL_X0Y17 | n/a          |                 3 |         334 |               0 |       10.000 | net_ft601_clk   | ft601_clk_IBUF_BUFG_inst/O                                                                                              | ft601_clk_IBUF_BUFG                                                                                                     |
| g5        | src5      | BUFG/O          | None       | BUFGCTRL_X0Y18 | n/a          |                 1 |           9 |               0 |       10.000 | pcie_sys_clk_p  | i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/cpllpd_refclk_inst/O                                   | i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/gt_cpllpdrefclk                                        |
| g6        | src6      | BUFG/O          | None       | BUFGCTRL_X0Y19 | n/a          |                 1 |           1 |               0 |       10.000 | txoutclk_x0y0   | i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/txoutclk_i.txoutclk_i/O    | i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/refclk                     |
+-----------+-----------+-----------------+------------+----------------+--------------+-------------------+-------------+-----------------+--------------+-----------------+-------------------------------------------------------------------------------------------------------------------------+-------------------------------------------------------------------------------------------------------------------------+
* Clock Loads column represents cell count of net connects that connect to a clock pin. Internal cell leaf pins are not considered
** Non-Clock Loads column represents cell count of non-clock pin loads


3. Global Clock Source Details
------------------------------

+-----------+-----------+------------------------+--------------------+--------------------+--------------+-------------+-----------------+---------------------+-----------------+---------------------------------------------------------------------------------------------------------------------------------+--------------------------------------------------------------------------------------------------------------------------+
| Source Id | Global Id | Driver Type/Pin        | Constraint         | Site               | Clock Region | Clock Loads | Non-Clock Loads | Source Clock Period | Source Clock    | Driver Pin                                                                                                                      | Net                                                                                                                      |
+-----------+-----------+------------------------+--------------------+--------------------+--------------+-------------+-----------------+---------------------+-----------------+---------------------------------------------------------------------------------------------------------------------------------+--------------------------------------------------------------------------------------------------------------------------+
| src0      | g0        | MMCME2_ADV/CLKOUT2     | None               | MMCME2_ADV_X0Y1    | X0Y1         |           1 |               0 |              16.000 | userclk1        | i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/mmcm_i/CLKOUT2                     | i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/userclk1                    |
| src1      | g1        | IBUF/O                 | IOB_X0Y126         | IOB_X0Y126         | X0Y2         |           1 |               0 |              10.000 | net_clk         | clk_IBUF_inst/O                                                                                                                 | clk_IBUF                                                                                                                 |
| src2      | g2        | MMCME2_ADV/CLKOUT0     | None               | MMCME2_ADV_X0Y1    | X0Y1         |           2 |               0 |               8.000 | clk_125mhz_x0y0 | i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/mmcm_i/CLKOUT0                     | i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/clk_125mhz                  |
| src2      | g3        | MMCME2_ADV/CLKOUT0     | None               | MMCME2_ADV_X0Y1    | X0Y1         |           2 |               0 |               8.000 | clk_125mhz_x0y0 | i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/mmcm_i/CLKOUT0                     | i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/clk_125mhz                  |
| src3      | g2        | MMCME2_ADV/CLKOUT1     | None               | MMCME2_ADV_X0Y1    | X0Y1         |           0 |               0 |               4.000 | clk_250mhz_x0y0 | i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/mmcm_i/CLKOUT1                     | i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/clk_250mhz                  |
| src4      | g4        | IBUF/O                 | IOB_X0Y124         | IOB_X0Y124         | X0Y2         |           1 |               0 |              10.000 | net_ft601_clk   | ft601_clk_IBUF_inst/O                                                                                                           | ft601_clk_IBUF                                                                                                           |
| src5      | g5        | IBUFDS_GTE2/O          | IBUFDS_GTE2_X0Y3   | IBUFDS_GTE2_X0Y3   | X1Y3         |          28 |               0 |              10.000 | pcie_sys_clk_p  | i_pcileech_pcie_a7/refclk_ibuf/O                                                                                                | i_pcileech_pcie_a7/pcie_clk_c                                                                                            |
| src6      | g6        | GTPE2_CHANNEL/TXOUTCLK | GTPE2_CHANNEL_X0Y7 | GTPE2_CHANNEL_X0Y7 | X1Y3         |           1 |               0 |              10.000 | txoutclk_x0y0   | i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_lane[0].gt_wrapper_i/gtp_channel.gtpe2_channel_i/TXOUTCLK | i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_lane[0].gt_wrapper_i/gtp_channel.gtpe2_channel_i_6 |
+-----------+-----------+------------------------+--------------------+--------------------+--------------+-------------+-----------------+---------------------+-----------------+---------------------------------------------------------------------------------------------------------------------------------+--------------------------------------------------------------------------------------------------------------------------+
* Clock Loads column represents cell count of net connects that connect to a clock pin. Internal cell leaf pins are not considered
** Non-Clock Loads column represents cell count of non-clock pin loads


4. Local Clock Details
----------------------

+----------+-------------------------+-------------------+--------------------------------+--------------+-------------+-----------------+--------------+-------+-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
| Local Id | Driver Type/Pin         | Constraint        | Site/BEL                       | Clock Region | Clock Loads | Non-Clock Loads | Clock Period | Clock | Driver Pin                                                                                                                                                                              | Net                                                                                                                                                                            |
+----------+-------------------------+-------------------+--------------------------------+--------------+-------------+-----------------+--------------+-------+-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
| 0        | GTPE2_COMMON/PLL0OUTCLK | GTPE2_COMMON_X0Y1 | GTPE2_COMMON_X0Y1/GTPE2_COMMON | X1Y3         |           1 |               0 |              |       | i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_lane[0].pipe_quad.gt_common_enabled.gt_common_int.gt_common_i/qpll_wrapper_i/gtp_common.gtpe2_common_i/PLL0OUTCLK | i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_lane[0].pipe_quad.gt_common_enabled.gt_common_int.gt_common_i/qpll_wrapper_i/gtp_common.gtpe2_common_i_1 |
| 1        | FDRE/Q                  | None              | SLICE_X78Y186/BFF              | X1Y3         |           1 |               0 |              |       | i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_lane[0].pipe_user_i/oobclk_div.oobclk_reg/Q                                                                       | i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_lane[0].pipe_user_i/user_oobclk                                                                          |
+----------+-------------------------+-------------------+--------------------------------+--------------+-------------+-----------------+--------------+-------+-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
* Local Clocks in this context represents only clocks driven by non-global buffers
** Clock Loads column represents cell count of net connects that connect to a clock pin. Internal cell leaf pins are not considered
*** Non-Clock Loads column represents cell count of non-clock pin loads


5. Clock Regions: Key Resource Utilization
------------------------------------------

+-------------------+--------------+--------------+--------------+--------------+--------------+--------------+--------------+--------------+--------------+--------------+--------------+--------------+--------------+--------------+--------------+
|                   | Global Clock |     BUFRs    |    BUFMRs    |    BUFIOs    |     MMCM     |      PLL     |      GT      |      PCI     |    ILOGIC    |    OLOGIC    |      FF      |     LUTM     |    RAMB18    |    RAMB36    |    DSP48E2   |
+-------------------+------+-------+------+-------+------+-------+------+-------+------+-------+------+-------+------+-------+------+-------+------+-------+------+-------+------+-------+------+-------+------+-------+------+-------+------+-------+
| Clock Region Name | Used | Avail | Used | Avail | Used | Avail | Used | Avail | Used | Avail | Used | Avail | Used | Avail | Used | Avail | Used | Avail | Used | Avail | Used | Avail | Used | Avail | Used | Avail | Used | Avail | Used | Avail |
+-------------------+------+-------+------+-------+------+-------+------+-------+------+-------+------+-------+------+-------+------+-------+------+-------+------+-------+------+-------+------+-------+------+-------+------+-------+------+-------+
| X0Y0              |    0 |    12 |    0 |     4 |    0 |     2 |    0 |     4 |    0 |     1 |    0 |     1 |    0 |     0 |    0 |     0 |    0 |    50 |    0 |    50 |    0 |  2600 |    0 |   600 |    0 |    20 |    0 |    10 |    0 |    20 |
| X1Y0              |    0 |    12 |    0 |     0 |    0 |     0 |    0 |     0 |    0 |     0 |    0 |     0 |    0 |     4 |    0 |     0 |    0 |     0 |    0 |     0 |    0 |  1500 |    0 |   550 |    0 |    40 |    0 |    20 |    0 |    40 |
| X0Y1              |    3 |    12 |    0 |     4 |    0 |     2 |    0 |     4 |    1 |     1 |    0 |     1 |    0 |     0 |    0 |     0 |    0 |    50 |    0 |    50 |   29 |  2000 |    0 |   600 |    0 |    20 |    0 |    10 |    0 |    20 |
| X1Y1              |    0 |    12 |    0 |     4 |    0 |     2 |    0 |     4 |    0 |     1 |    0 |     1 |    0 |     0 |    0 |     0 |    0 |    50 |    0 |    50 |    0 |  1900 |    0 |   650 |    0 |    60 |    0 |    30 |    0 |    40 |
| X0Y2              |    3 |    12 |    0 |     4 |    0 |     2 |    0 |     4 |    0 |     1 |    0 |     1 |    0 |     0 |    0 |     0 |    0 |    50 |    0 |    50 | 1805 |  2000 |  249 |   600 |    0 |    20 |    1 |    10 |    0 |    20 |
| X1Y2              |    2 |    12 |    0 |     4 |    0 |     2 |    0 |     4 |    0 |     1 |    0 |     1 |    0 |     0 |    0 |     0 |    0 |    50 |    0 |    50 | 1203 |  1900 |  373 |   650 |    0 |    60 |   12 |    30 |    0 |    40 |
| X0Y3              |    3 |    12 |    0 |     4 |    0 |     2 |    0 |     4 |    0 |     1 |    0 |     1 |    0 |     0 |    0 |     0 |    0 |    50 |    0 |    50 | 1122 |  2600 |  157 |   600 |    0 |    20 |    1 |    10 |    0 |    20 |
| X1Y3              |    5 |    12 |    0 |     0 |    0 |     0 |    0 |     0 |    0 |     0 |    0 |     0 |    1 |     4 |    1 |     1 |    0 |     0 |    0 |     0 |  855 |  1350 |  338 |   500 |    0 |    30 |    9 |    15 |    0 |    40 |
+-------------------+------+-------+------+-------+------+-------+------+-------+------+-------+------+-------+------+-------+------+-------+------+-------+------+-------+------+-------+------+-------+------+-------+------+-------+------+-------+
* Global Clock column represents track count; while other columns represents cell counts


6. Clock Regions : Global Clock Summary
---------------------------------------

All Modules
+----+----+----+
|    | X0 | X1 |
+----+----+----+
| Y3 |  0 |  0 |
| Y2 |  0 |  0 |
| Y1 |  0 |  0 |
| Y0 |  0 |  0 |
+----+----+----+


7. Device Cell Placement Summary for Global Clock g0
----------------------------------------------------

+-----------+-----------------+-------------------+----------+-------------+---------------+-------------+----------+----------------+----------+-------------------------------------------------------------------------------------------------------+
| Global Id | Driver Type/Pin | Driver Region (D) | Clock    | Period (ns) | Waveform (ns) | Slice Loads | IO Loads | Clocking Loads | GT Loads | Net                                                                                                   |
+-----------+-----------------+-------------------+----------+-------------+---------------+-------------+----------+----------------+----------+-------------------------------------------------------------------------------------------------------+
| g0        | BUFG/O          | n/a               | userclk1 |      16.000 | {0.000 8.000} |        2847 |        0 |              0 |        0 | i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/user_clk |
+-----------+-----------------+-------------------+----------+-------------+---------------+-------------+----------+----------------+----------+-------------------------------------------------------------------------------------------------------+
* Slice Loads column represents load cell count of all cell types other than IO, GT and clock resources
** IO Loads column represents load cell count of IO types
*** Clocking Loads column represents load cell count that are clock resources (global clock buffer, MMCM, PLL, etc)
**** GT Loads column represents load cell count of GT types


+----+------+------+-----------------------+
|    | X0   | X1   | HORIZONTAL PROG DELAY |
+----+------+------+-----------------------+
| Y3 |  822 |  349 |                     0 |
| Y2 |  920 |  756 |                     0 |
| Y1 |    0 |    0 |                     - |
| Y0 |    0 |    0 |                     - |
+----+------+------+-----------------------+


8. Device Cell Placement Summary for Global Clock g1
----------------------------------------------------

+-----------+-----------------+-------------------+---------+-------------+---------------+-------------+----------+----------------+----------+---------------+
| Global Id | Driver Type/Pin | Driver Region (D) | Clock   | Period (ns) | Waveform (ns) | Slice Loads | IO Loads | Clocking Loads | GT Loads | Net           |
+-----------+-----------------+-------------------+---------+-------------+---------------+-------------+----------+----------------+----------+---------------+
| g1        | BUFG/O          | n/a               | net_clk |      10.000 | {0.000 5.000} |        1398 |        0 |              0 |        0 | clk_IBUF_BUFG |
+-----------+-----------------+-------------------+---------+-------------+---------------+-------------+----------+----------------+----------+---------------+
* Slice Loads column represents load cell count of all cell types other than IO, GT and clock resources
** IO Loads column represents load cell count of IO types
*** Clocking Loads column represents load cell count that are clock resources (global clock buffer, MMCM, PLL, etc)
**** GT Loads column represents load cell count of GT types


+----+------+------+-----------------------+
|    | X0   | X1   | HORIZONTAL PROG DELAY |
+----+------+------+-----------------------+
| Y3 |  241 |   40 |                     0 |
| Y2 |  654 |  462 |                     0 |
| Y1 |    1 |    0 |                     0 |
| Y0 |    0 |    0 |                     - |
+----+------+------+-----------------------+


9. Device Cell Placement Summary for Global Clock g2
----------------------------------------------------

+-----------+-----------------+-------------------+----------+-------------+---------------+-------------+----------+----------------+----------+-------------------------------------------------------------------------------------------------------------------------+
| Global Id | Driver Type/Pin | Driver Region (D) | Clock    | Period (ns) | Waveform (ns) | Slice Loads | IO Loads | Clocking Loads | GT Loads | Net                                                                                                                     |
+-----------+-----------------+-------------------+----------+-------------+---------------+-------------+----------+----------------+----------+-------------------------------------------------------------------------------------------------------------------------+
| g2        | BUFGCTRL/O      | n/a               | Multiple |       8.000 | {0.000 4.000} |         340 |        0 |              0 |        1 | i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/pclk_i1_bufgctrl.pclk_i1_0 |
+-----------+-----------------+-------------------+----------+-------------+---------------+-------------+----------+----------------+----------+-------------------------------------------------------------------------------------------------------------------------+
* Slice Loads column represents load cell count of all cell types other than IO, GT and clock resources
** IO Loads column represents load cell count of IO types
*** Clocking Loads column represents load cell count that are clock resources (global clock buffer, MMCM, PLL, etc)
**** GT Loads column represents load cell count of GT types


+----+----+------+-----------------------+
|    | X0 | X1   | HORIZONTAL PROG DELAY |
+----+----+------+-----------------------+
| Y3 |  0 |  341 |                     0 |
| Y2 |  0 |    0 |                     - |
| Y1 |  0 |    0 |                     - |
| Y0 |  0 |    0 |                     - |
+----+----+------+-----------------------+


10. Device Cell Placement Summary for Global Clock g3
-----------------------------------------------------

+-----------+-----------------+-------------------+-----------------+-------------+---------------+-------------+----------+----------------+----------+--------------------------------------------------------------------------------------------------+
| Global Id | Driver Type/Pin | Driver Region (D) | Clock           | Period (ns) | Waveform (ns) | Slice Loads | IO Loads | Clocking Loads | GT Loads | Net                                                                                              |
+-----------+-----------------+-------------------+-----------------+-------------+---------------+-------------+----------+----------------+----------+--------------------------------------------------------------------------------------------------+
| g3        | BUFG/O          | n/a               | clk_125mhz_x0y0 |       8.000 | {0.000 4.000} |         143 |        0 |              0 |        2 | i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/CLK |
+-----------+-----------------+-------------------+-----------------+-------------+---------------+-------------+----------+----------------+----------+--------------------------------------------------------------------------------------------------+
* Slice Loads column represents load cell count of all cell types other than IO, GT and clock resources
** IO Loads column represents load cell count of IO types
*** Clocking Loads column represents load cell count that are clock resources (global clock buffer, MMCM, PLL, etc)
**** GT Loads column represents load cell count of GT types


+----+----+------+-----------------------+
|    | X0 | X1   | HORIZONTAL PROG DELAY |
+----+----+------+-----------------------+
| Y3 |  0 |  145 |                     0 |
| Y2 |  0 |    0 |                     - |
| Y1 |  0 |    0 |                     - |
| Y0 |  0 |    0 |                     - |
+----+----+------+-----------------------+


11. Device Cell Placement Summary for Global Clock g4
-----------------------------------------------------

+-----------+-----------------+-------------------+---------------+-------------+---------------+-------------+----------+----------------+----------+---------------------+
| Global Id | Driver Type/Pin | Driver Region (D) | Clock         | Period (ns) | Waveform (ns) | Slice Loads | IO Loads | Clocking Loads | GT Loads | Net                 |
+-----------+-----------------+-------------------+---------------+-------------+---------------+-------------+----------+----------------+----------+---------------------+
| g4        | BUFG/O          | n/a               | net_ft601_clk |      10.000 | {0.000 5.000} |         323 |        0 |              0 |        0 | ft601_clk_IBUF_BUFG |
+-----------+-----------------+-------------------+---------------+-------------+---------------+-------------+----------+----------------+----------+---------------------+
* Slice Loads column represents load cell count of all cell types other than IO, GT and clock resources
** IO Loads column represents load cell count of IO types
*** Clocking Loads column represents load cell count that are clock resources (global clock buffer, MMCM, PLL, etc)
**** GT Loads column represents load cell count of GT types


+----+------+----+-----------------------+
|    | X0   | X1 | HORIZONTAL PROG DELAY |
+----+------+----+-----------------------+
| Y3 |   61 |  0 |                     0 |
| Y2 |  233 |  0 |                     0 |
| Y1 |   29 |  0 |                     0 |
| Y0 |    0 |  0 |                     - |
+----+------+----+-----------------------+


12. Device Cell Placement Summary for Global Clock g5
-----------------------------------------------------

+-----------+-----------------+-------------------+----------------+-------------+---------------+-------------+----------+----------------+----------+----------------------------------------------------------------------------------+
| Global Id | Driver Type/Pin | Driver Region (D) | Clock          | Period (ns) | Waveform (ns) | Slice Loads | IO Loads | Clocking Loads | GT Loads | Net                                                                              |
+-----------+-----------------+-------------------+----------------+-------------+---------------+-------------+----------+----------------+----------+----------------------------------------------------------------------------------+
| g5        | BUFG/O          | n/a               | pcie_sys_clk_p |      10.000 | {0.000 5.000} |           9 |        0 |              0 |        0 | i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/gt_cpllpdrefclk |
+-----------+-----------------+-------------------+----------------+-------------+---------------+-------------+----------+----------------+----------+----------------------------------------------------------------------------------+
* Slice Loads column represents load cell count of all cell types other than IO, GT and clock resources
** IO Loads column represents load cell count of IO types
*** Clocking Loads column represents load cell count that are clock resources (global clock buffer, MMCM, PLL, etc)
**** GT Loads column represents load cell count of GT types


+----+----+----+-----------------------+
|    | X0 | X1 | HORIZONTAL PROG DELAY |
+----+----+----+-----------------------+
| Y3 |  0 |  9 |                     0 |
| Y2 |  0 |  0 |                     - |
| Y1 |  0 |  0 |                     - |
| Y0 |  0 |  0 |                     - |
+----+----+----+-----------------------+


13. Device Cell Placement Summary for Global Clock g6
-----------------------------------------------------

+-----------+-----------------+-------------------+---------------+-------------+---------------+-------------+----------+----------------+----------+-----------------------------------------------------------------------------------------------------+
| Global Id | Driver Type/Pin | Driver Region (D) | Clock         | Period (ns) | Waveform (ns) | Slice Loads | IO Loads | Clocking Loads | GT Loads | Net                                                                                                 |
+-----------+-----------------+-------------------+---------------+-------------+---------------+-------------+----------+----------------+----------+-----------------------------------------------------------------------------------------------------+
| g6        | BUFG/O          | n/a               | txoutclk_x0y0 |      10.000 | {0.000 5.000} |           0 |        0 |              1 |        0 | i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/refclk |
+-----------+-----------------+-------------------+---------------+-------------+---------------+-------------+----------+----------------+----------+-----------------------------------------------------------------------------------------------------+
* Slice Loads column represents load cell count of all cell types other than IO, GT and clock resources
** IO Loads column represents load cell count of IO types
*** Clocking Loads column represents load cell count that are clock resources (global clock buffer, MMCM, PLL, etc)
**** GT Loads column represents load cell count of GT types


+----+----+----+-----------------------+
|    | X0 | X1 | HORIZONTAL PROG DELAY |
+----+----+----+-----------------------+
| Y3 |  0 |  0 |                     - |
| Y2 |  0 |  0 |                     - |
| Y1 |  1 |  0 |                     0 |
| Y0 |  0 |  0 |                     - |
+----+----+----+-----------------------+


14. Clock Region Cell Placement per Global Clock: Region X0Y1
-------------------------------------------------------------

+-----------+-------+-----------------+------------+-------------+-----------------+----+-------------+------+-----+----+------+-----+---------+-----------------------------------------------------------------------------------------------------+
| Global Id | Track | Driver Type/Pin | Constraint | Clock Loads | Non-Clock Loads | FF | Memory LUTs | RAMB | DSP | GT | MMCM | PLL | Hard IP | Net                                                                                                 |
+-----------+-------+-----------------+------------+-------------+-----------------+----+-------------+------+-----+----+------+-----+---------+-----------------------------------------------------------------------------------------------------+
| g1        | n/a   | BUFG/O          | None       |           1 |               0 |  0 |           0 |    0 |   0 |  0 |    0 |   0 |       0 | clk_IBUF_BUFG                                                                                       |
| g4        | n/a   | BUFG/O          | None       |          29 |               0 | 29 |           0 |    0 |   0 |  0 |    0 |   0 |       0 | ft601_clk_IBUF_BUFG                                                                                 |
| g6        | n/a   | BUFG/O          | None       |           1 |               0 |  0 |           0 |    0 |   0 |  0 |    1 |   0 |       0 | i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/refclk |
+-----------+-------+-----------------+------------+-------------+-----------------+----+-------------+------+-----+----+------+-----+---------+-----------------------------------------------------------------------------------------------------+
* Clock Loads column represents cell count of net connects that connect to a clock pin. Internal cell leaf pins are not considered
** Non-Clock Loads column represents cell count of non-clock pin loads
*** Columns FF, LUTRAM, RAMB through 'Hard IP' represents load cell counts


15. Clock Region Cell Placement per Global Clock: Region X0Y2
-------------------------------------------------------------

+-----------+-------+-----------------+------------+-------------+-----------------+-----+-------------+------+-----+----+------+-----+---------+-------------------------------------------------------------------------------------------------------+
| Global Id | Track | Driver Type/Pin | Constraint | Clock Loads | Non-Clock Loads | FF  | Memory LUTs | RAMB | DSP | GT | MMCM | PLL | Hard IP | Net                                                                                                   |
+-----------+-------+-----------------+------------+-------------+-----------------+-----+-------------+------+-----+----+------+-----+---------+-------------------------------------------------------------------------------------------------------+
| g0        | n/a   | BUFG/O          | None       |         920 |               0 | 919 |           0 |    1 |   0 |  0 |    0 |   0 |       0 | i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/user_clk |
| g1        | n/a   | BUFG/O          | None       |         654 |               0 | 653 |           0 |    1 |   0 |  0 |    0 |   0 |       0 | clk_IBUF_BUFG                                                                                         |
| g4        | n/a   | BUFG/O          | None       |         233 |               0 | 233 |           0 |    0 |   0 |  0 |    0 |   0 |       0 | ft601_clk_IBUF_BUFG                                                                                   |
+-----------+-------+-----------------+------------+-------------+-----------------+-----+-------------+------+-----+----+------+-----+---------+-------------------------------------------------------------------------------------------------------+
* Clock Loads column represents cell count of net connects that connect to a clock pin. Internal cell leaf pins are not considered
** Non-Clock Loads column represents cell count of non-clock pin loads
*** Columns FF, LUTRAM, RAMB through 'Hard IP' represents load cell counts


16. Clock Region Cell Placement per Global Clock: Region X1Y2
-------------------------------------------------------------

+-----------+-------+-----------------+------------+-------------+-----------------+-----+-------------+------+-----+----+------+-----+---------+-------------------------------------------------------------------------------------------------------+
| Global Id | Track | Driver Type/Pin | Constraint | Clock Loads | Non-Clock Loads | FF  | Memory LUTs | RAMB | DSP | GT | MMCM | PLL | Hard IP | Net                                                                                                   |
+-----------+-------+-----------------+------------+-------------+-----------------+-----+-------------+------+-----+----+------+-----+---------+-------------------------------------------------------------------------------------------------------+
| g0        | n/a   | BUFG/O          | None       |         756 |               0 | 743 |           0 |   12 |   0 |  0 |    0 |   0 |       0 | i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/user_clk |
| g1        | n/a   | BUFG/O          | None       |         462 |               0 | 460 |           0 |    2 |   0 |  0 |    0 |   0 |       0 | clk_IBUF_BUFG                                                                                         |
+-----------+-------+-----------------+------------+-------------+-----------------+-----+-------------+------+-----+----+------+-----+---------+-------------------------------------------------------------------------------------------------------+
* Clock Loads column represents cell count of net connects that connect to a clock pin. Internal cell leaf pins are not considered
** Non-Clock Loads column represents cell count of non-clock pin loads
*** Columns FF, LUTRAM, RAMB through 'Hard IP' represents load cell counts


17. Clock Region Cell Placement per Global Clock: Region X0Y3
-------------------------------------------------------------

+-----------+-------+-----------------+------------+-------------+-----------------+-----+-------------+------+-----+----+------+-----+---------+-------------------------------------------------------------------------------------------------------+
| Global Id | Track | Driver Type/Pin | Constraint | Clock Loads | Non-Clock Loads | FF  | Memory LUTs | RAMB | DSP | GT | MMCM | PLL | Hard IP | Net                                                                                                   |
+-----------+-------+-----------------+------------+-------------+-----------------+-----+-------------+------+-----+----+------+-----+---------+-------------------------------------------------------------------------------------------------------+
| g0        | n/a   | BUFG/O          | None       |         822 |               0 | 821 |           0 |    1 |   0 |  0 |    0 |   0 |       0 | i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/user_clk |
| g1        | n/a   | BUFG/O          | None       |         241 |               0 | 240 |           0 |    1 |   0 |  0 |    0 |   0 |       0 | clk_IBUF_BUFG                                                                                         |
| g4        | n/a   | BUFG/O          | None       |          61 |               0 |  61 |           0 |    0 |   0 |  0 |    0 |   0 |       0 | ft601_clk_IBUF_BUFG                                                                                   |
+-----------+-------+-----------------+------------+-------------+-----------------+-----+-------------+------+-----+----+------+-----+---------+-------------------------------------------------------------------------------------------------------+
* Clock Loads column represents cell count of net connects that connect to a clock pin. Internal cell leaf pins are not considered
** Non-Clock Loads column represents cell count of non-clock pin loads
*** Columns FF, LUTRAM, RAMB through 'Hard IP' represents load cell counts


18. Clock Region Cell Placement per Global Clock: Region X1Y3
-------------------------------------------------------------

+-----------+-------+-----------------+------------+-------------+-----------------+-----+-------------+------+-----+----+------+-----+---------+-------------------------------------------------------------------------------------------------------------------------+
| Global Id | Track | Driver Type/Pin | Constraint | Clock Loads | Non-Clock Loads | FF  | Memory LUTs | RAMB | DSP | GT | MMCM | PLL | Hard IP | Net                                                                                                                     |
+-----------+-------+-----------------+------------+-------------+-----------------+-----+-------------+------+-----+----+------+-----+---------+-------------------------------------------------------------------------------------------------------------------------+
| g0        | n/a   | BUFG/O          | None       |         349 |               0 | 339 |           0 |    9 |   0 |  0 |    0 |   0 |       1 | i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/user_clk                   |
| g1        | n/a   | BUFG/O          | None       |          40 |               0 |  38 |           0 |    1 |   0 |  0 |    0 |   0 |       1 | clk_IBUF_BUFG                                                                                                           |
| g2        | n/a   | BUFGCTRL/O      | None       |         341 |               0 | 333 |           6 |    0 |   0 |  1 |    0 |   0 |       1 | i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/pclk_i1_bufgctrl.pclk_i1_0 |
| g3        | n/a   | BUFG/O          | None       |         145 |               0 | 143 |           0 |    0 |   0 |  1 |    0 |   0 |       0 | i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/CLK                        |
| g5        | n/a   | BUFG/O          | None       |           9 |               0 |   2 |           7 |    0 |   0 |  0 |    0 |   0 |       0 | i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/gt_cpllpdrefclk                                        |
+-----------+-------+-----------------+------------+-------------+-----------------+-----+-------------+------+-----+----+------+-----+---------+-------------------------------------------------------------------------------------------------------------------------+
* Clock Loads column represents cell count of net connects that connect to a clock pin. Internal cell leaf pins are not considered
** Non-Clock Loads column represents cell count of non-clock pin loads
*** Columns FF, LUTRAM, RAMB through 'Hard IP' represents load cell counts



# Location of BUFG Primitives 
set_property LOC BUFGCTRL_X0Y0 [get_cells i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/userclk1_i1.usrclk1_i1]
set_property LOC BUFGCTRL_X0Y19 [get_cells i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/txoutclk_i.txoutclk_i]
set_property LOC BUFGCTRL_X0Y1 [get_cells i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/pclk_i1_bufgctrl.pclk_i1]
set_property LOC BUFGCTRL_X0Y2 [get_cells i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/dclk_i_bufg.dclk_i]
set_property LOC BUFGCTRL_X0Y18 [get_cells i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/cpllpd_refclk_inst]
set_property LOC BUFGCTRL_X0Y17 [get_cells ft601_clk_IBUF_BUFG_inst]
set_property LOC BUFGCTRL_X0Y16 [get_cells clk_IBUF_BUFG_inst]

# Location of IO Primitives which is load of clock spine

# Location of clock ports
set_property LOC IOB_X0Y126 [get_ports clk]
set_property LOC IOB_X0Y124 [get_ports ft601_clk]
set_property LOC IPAD_X1Y47 [get_ports pcie_clk_n]
set_property LOC IPAD_X1Y46 [get_ports pcie_clk_p]

# Clock net "i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/user_clk" driven by instance "i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/userclk1_i1.usrclk1_i1" located at site "BUFGCTRL_X0Y0"
#startgroup
create_pblock {CLKAG_i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/user_clk}
add_cells_to_pblock [get_pblocks  {CLKAG_i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/user_clk}] [get_cells -filter { PRIMITIVE_GROUP != I/O && IS_PRIMITIVE==1 && PRIMITIVE_LEVEL !=INTERNAL } -of_object [get_pins -filter {DIRECTION==IN} -of_objects [get_nets -hierarchical -filter {PARENT=="i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/user_clk"}]]]
resize_pblock [get_pblocks {CLKAG_i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/user_clk}] -add {CLOCKREGION_X0Y2:CLOCKREGION_X0Y2 CLOCKREGION_X0Y3:CLOCKREGION_X0Y3 CLOCKREGION_X1Y2:CLOCKREGION_X1Y2 CLOCKREGION_X1Y3:CLOCKREGION_X1Y3}
#endgroup

# Clock net "i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/pclk_i1_bufgctrl.pclk_i1_0" driven by instance "i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/pclk_i1_bufgctrl.pclk_i1" located at site "BUFGCTRL_X0Y1"
#startgroup
create_pblock {CLKAG_i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/pclk_i1_bufgctrl.pclk_i1_0}
add_cells_to_pblock [get_pblocks  {CLKAG_i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/pclk_i1_bufgctrl.pclk_i1_0}] [get_cells -filter { PRIMITIVE_GROUP != I/O && IS_PRIMITIVE==1 && PRIMITIVE_LEVEL !=INTERNAL } -of_object [get_pins -filter {DIRECTION==IN} -of_objects [get_nets -hierarchical -filter {PARENT=="i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/pclk_i1_bufgctrl.pclk_i1_0"}]]]
resize_pblock [get_pblocks {CLKAG_i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/pclk_i1_bufgctrl.pclk_i1_0}] -add {CLOCKREGION_X1Y3:CLOCKREGION_X1Y3}
#endgroup

# Clock net "i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/CLK" driven by instance "i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/dclk_i_bufg.dclk_i" located at site "BUFGCTRL_X0Y2"
#startgroup
create_pblock {CLKAG_i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/CLK}
add_cells_to_pblock [get_pblocks  {CLKAG_i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/CLK}] [get_cells -filter { PRIMITIVE_GROUP != I/O && IS_PRIMITIVE==1 && PRIMITIVE_LEVEL !=INTERNAL } -of_object [get_pins -filter {DIRECTION==IN} -of_objects [get_nets -hierarchical -filter {PARENT=="i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/CLK"}]]]
resize_pblock [get_pblocks {CLKAG_i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_clock_int.pipe_clock_i/CLK}] -add {CLOCKREGION_X1Y3:CLOCKREGION_X1Y3}
#endgroup

# Clock net "i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/gt_cpllpdrefclk" driven by instance "i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/cpllpd_refclk_inst" located at site "BUFGCTRL_X0Y18"
#startgroup
create_pblock {CLKAG_i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/gt_cpllpdrefclk}
add_cells_to_pblock [get_pblocks  {CLKAG_i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/gt_cpllpdrefclk}] [get_cells -filter { PRIMITIVE_GROUP != I/O && IS_PRIMITIVE==1 && PRIMITIVE_LEVEL !=INTERNAL } -of_object [get_pins -filter {DIRECTION==IN} -of_objects [get_nets -hierarchical -filter {PARENT=="i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/gt_cpllpdrefclk"}]]]
resize_pblock [get_pblocks {CLKAG_i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/gt_cpllpdrefclk}] -add {CLOCKREGION_X1Y3:CLOCKREGION_X1Y3}
#endgroup

# Clock net "ft601_clk_IBUF_BUFG" driven by instance "ft601_clk_IBUF_BUFG_inst" located at site "BUFGCTRL_X0Y17"
#startgroup
create_pblock {CLKAG_ft601_clk_IBUF_BUFG}
add_cells_to_pblock [get_pblocks  {CLKAG_ft601_clk_IBUF_BUFG}] [get_cells -filter { PRIMITIVE_GROUP != I/O && IS_PRIMITIVE==1 && PRIMITIVE_LEVEL !=INTERNAL } -of_object [get_pins -filter {DIRECTION==IN} -of_objects [get_nets -hierarchical -filter {PARENT=="ft601_clk_IBUF_BUFG"}]]]
resize_pblock [get_pblocks {CLKAG_ft601_clk_IBUF_BUFG}] -add {CLOCKREGION_X0Y1:CLOCKREGION_X0Y1 CLOCKREGION_X0Y2:CLOCKREGION_X0Y2 CLOCKREGION_X0Y3:CLOCKREGION_X0Y3}
#endgroup

# Clock net "clk_IBUF_BUFG" driven by instance "clk_IBUF_BUFG_inst" located at site "BUFGCTRL_X0Y16"
#startgroup
create_pblock {CLKAG_clk_IBUF_BUFG}
add_cells_to_pblock [get_pblocks  {CLKAG_clk_IBUF_BUFG}] [get_cells -filter { PRIMITIVE_GROUP != I/O && IS_PRIMITIVE==1 && PRIMITIVE_LEVEL !=INTERNAL } -of_object [get_pins -filter {DIRECTION==IN} -of_objects [get_nets -hierarchical -filter {PARENT=="clk_IBUF_BUFG"}]]]
resize_pblock [get_pblocks {CLKAG_clk_IBUF_BUFG}] -add {CLOCKREGION_X0Y1:CLOCKREGION_X0Y1 CLOCKREGION_X0Y2:CLOCKREGION_X0Y2 CLOCKREGION_X0Y3:CLOCKREGION_X0Y3 CLOCKREGION_X1Y2:CLOCKREGION_X1Y2 CLOCKREGION_X1Y3:CLOCKREGION_X1Y3}
#endgroup
