Copyright 1986-2022 Xilinx, Inc. All Rights Reserved. Copyright 2022-2025 Advanced Micro Devices, Inc. All Rights Reserved.
-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
| Tool Version : Vivado v.2024.2.2 (win64) Build 6060944 Thu Mar 06 19:10:01 MST 2025
| Date         : Wed Jun 18 17:38:24 2025
| Host         : DESKTOP-7T6N58Q running 64-bit major release  (build 9200)
| Command      : report_methodology -file pcileech_100t484_x1_top_methodology_drc_routed.rpt -pb pcileech_100t484_x1_top_methodology_drc_routed.pb -rpx pcileech_100t484_x1_top_methodology_drc_routed.rpx
| Design       : pcileech_100t484_x1_top
| Device       : xc7a100tfgg484-2
| Speed File   : -2
| Design State : Fully Routed
-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Report Methodology

Table of Contents
-----------------
1. REPORT SUMMARY
2. REPORT DETAILS

1. REPORT SUMMARY
-----------------
            Netlist: netlist
          Floorplan: design_1
      Design limits: <entire design considered>
             Max checks: <unlimited>
             Checks found: 43
+-----------+----------+----------------------------------+--------+
| Rule      | Severity | Description                      | Checks |
+-----------+----------+----------------------------------+--------+
| HPDR-1    | Warning  | Port pin direction inconsistency | 32     |
| LUTAR-1   | Warning  | LUT drives async reset alert     | 5      |
| TIMING-9  | Warning  | Unknown CDC Logic                | 1      |
| TIMING-10 | Warning  | Missing property on synchronizer | 1      |
| TIMING-18 | Warning  | Missing input or output delay    | 4      |
+-----------+----------+----------------------------------+--------+

2. REPORT DETAILS
-----------------
HPDR-1#1 Warning
Port pin direction inconsistency  
Hierarchical port(pin) ft601_data[0] direction INOUT is not consistent with the directions of the cell pins/ports found at that level of the netlist hierarchy. Please review the design and consider changing the port(pin) direction. Both drivers and loads were expected for the net (ft601_data[0]) connected to this Port, but both were not found.
Related violations: <none>

HPDR-1#2 Warning
Port pin direction inconsistency  
Hierarchical port(pin) ft601_data[10] direction INOUT is not consistent with the directions of the cell pins/ports found at that level of the netlist hierarchy. Please review the design and consider changing the port(pin) direction. Both drivers and loads were expected for the net (ft601_data[10]) connected to this Port, but both were not found.
Related violations: <none>

HPDR-1#3 Warning
Port pin direction inconsistency  
Hierarchical port(pin) ft601_data[11] direction INOUT is not consistent with the directions of the cell pins/ports found at that level of the netlist hierarchy. Please review the design and consider changing the port(pin) direction. Both drivers and loads were expected for the net (ft601_data[11]) connected to this Port, but both were not found.
Related violations: <none>

HPDR-1#4 Warning
Port pin direction inconsistency  
Hierarchical port(pin) ft601_data[12] direction INOUT is not consistent with the directions of the cell pins/ports found at that level of the netlist hierarchy. Please review the design and consider changing the port(pin) direction. Both drivers and loads were expected for the net (ft601_data[12]) connected to this Port, but both were not found.
Related violations: <none>

HPDR-1#5 Warning
Port pin direction inconsistency  
Hierarchical port(pin) ft601_data[13] direction INOUT is not consistent with the directions of the cell pins/ports found at that level of the netlist hierarchy. Please review the design and consider changing the port(pin) direction. Both drivers and loads were expected for the net (ft601_data[13]) connected to this Port, but both were not found.
Related violations: <none>

HPDR-1#6 Warning
Port pin direction inconsistency  
Hierarchical port(pin) ft601_data[14] direction INOUT is not consistent with the directions of the cell pins/ports found at that level of the netlist hierarchy. Please review the design and consider changing the port(pin) direction. Both drivers and loads were expected for the net (ft601_data[14]) connected to this Port, but both were not found.
Related violations: <none>

HPDR-1#7 Warning
Port pin direction inconsistency  
Hierarchical port(pin) ft601_data[15] direction INOUT is not consistent with the directions of the cell pins/ports found at that level of the netlist hierarchy. Please review the design and consider changing the port(pin) direction. Both drivers and loads were expected for the net (ft601_data[15]) connected to this Port, but both were not found.
Related violations: <none>

HPDR-1#8 Warning
Port pin direction inconsistency  
Hierarchical port(pin) ft601_data[16] direction INOUT is not consistent with the directions of the cell pins/ports found at that level of the netlist hierarchy. Please review the design and consider changing the port(pin) direction. Both drivers and loads were expected for the net (ft601_data[16]) connected to this Port, but both were not found.
Related violations: <none>

HPDR-1#9 Warning
Port pin direction inconsistency  
Hierarchical port(pin) ft601_data[17] direction INOUT is not consistent with the directions of the cell pins/ports found at that level of the netlist hierarchy. Please review the design and consider changing the port(pin) direction. Both drivers and loads were expected for the net (ft601_data[17]) connected to this Port, but both were not found.
Related violations: <none>

HPDR-1#10 Warning
Port pin direction inconsistency  
Hierarchical port(pin) ft601_data[18] direction INOUT is not consistent with the directions of the cell pins/ports found at that level of the netlist hierarchy. Please review the design and consider changing the port(pin) direction. Both drivers and loads were expected for the net (ft601_data[18]) connected to this Port, but both were not found.
Related violations: <none>

HPDR-1#11 Warning
Port pin direction inconsistency  
Hierarchical port(pin) ft601_data[19] direction INOUT is not consistent with the directions of the cell pins/ports found at that level of the netlist hierarchy. Please review the design and consider changing the port(pin) direction. Both drivers and loads were expected for the net (ft601_data[19]) connected to this Port, but both were not found.
Related violations: <none>

HPDR-1#12 Warning
Port pin direction inconsistency  
Hierarchical port(pin) ft601_data[1] direction INOUT is not consistent with the directions of the cell pins/ports found at that level of the netlist hierarchy. Please review the design and consider changing the port(pin) direction. Both drivers and loads were expected for the net (ft601_data[1]) connected to this Port, but both were not found.
Related violations: <none>

HPDR-1#13 Warning
Port pin direction inconsistency  
Hierarchical port(pin) ft601_data[20] direction INOUT is not consistent with the directions of the cell pins/ports found at that level of the netlist hierarchy. Please review the design and consider changing the port(pin) direction. Both drivers and loads were expected for the net (ft601_data[20]) connected to this Port, but both were not found.
Related violations: <none>

HPDR-1#14 Warning
Port pin direction inconsistency  
Hierarchical port(pin) ft601_data[21] direction INOUT is not consistent with the directions of the cell pins/ports found at that level of the netlist hierarchy. Please review the design and consider changing the port(pin) direction. Both drivers and loads were expected for the net (ft601_data[21]) connected to this Port, but both were not found.
Related violations: <none>

HPDR-1#15 Warning
Port pin direction inconsistency  
Hierarchical port(pin) ft601_data[22] direction INOUT is not consistent with the directions of the cell pins/ports found at that level of the netlist hierarchy. Please review the design and consider changing the port(pin) direction. Both drivers and loads were expected for the net (ft601_data[22]) connected to this Port, but both were not found.
Related violations: <none>

HPDR-1#16 Warning
Port pin direction inconsistency  
Hierarchical port(pin) ft601_data[23] direction INOUT is not consistent with the directions of the cell pins/ports found at that level of the netlist hierarchy. Please review the design and consider changing the port(pin) direction. Both drivers and loads were expected for the net (ft601_data[23]) connected to this Port, but both were not found.
Related violations: <none>

HPDR-1#17 Warning
Port pin direction inconsistency  
Hierarchical port(pin) ft601_data[24] direction INOUT is not consistent with the directions of the cell pins/ports found at that level of the netlist hierarchy. Please review the design and consider changing the port(pin) direction. Both drivers and loads were expected for the net (ft601_data[24]) connected to this Port, but both were not found.
Related violations: <none>

HPDR-1#18 Warning
Port pin direction inconsistency  
Hierarchical port(pin) ft601_data[25] direction INOUT is not consistent with the directions of the cell pins/ports found at that level of the netlist hierarchy. Please review the design and consider changing the port(pin) direction. Both drivers and loads were expected for the net (ft601_data[25]) connected to this Port, but both were not found.
Related violations: <none>

HPDR-1#19 Warning
Port pin direction inconsistency  
Hierarchical port(pin) ft601_data[26] direction INOUT is not consistent with the directions of the cell pins/ports found at that level of the netlist hierarchy. Please review the design and consider changing the port(pin) direction. Both drivers and loads were expected for the net (ft601_data[26]) connected to this Port, but both were not found.
Related violations: <none>

HPDR-1#20 Warning
Port pin direction inconsistency  
Hierarchical port(pin) ft601_data[27] direction INOUT is not consistent with the directions of the cell pins/ports found at that level of the netlist hierarchy. Please review the design and consider changing the port(pin) direction. Both drivers and loads were expected for the net (ft601_data[27]) connected to this Port, but both were not found.
Related violations: <none>

HPDR-1#21 Warning
Port pin direction inconsistency  
Hierarchical port(pin) ft601_data[28] direction INOUT is not consistent with the directions of the cell pins/ports found at that level of the netlist hierarchy. Please review the design and consider changing the port(pin) direction. Both drivers and loads were expected for the net (ft601_data[28]) connected to this Port, but both were not found.
Related violations: <none>

HPDR-1#22 Warning
Port pin direction inconsistency  
Hierarchical port(pin) ft601_data[29] direction INOUT is not consistent with the directions of the cell pins/ports found at that level of the netlist hierarchy. Please review the design and consider changing the port(pin) direction. Both drivers and loads were expected for the net (ft601_data[29]) connected to this Port, but both were not found.
Related violations: <none>

HPDR-1#23 Warning
Port pin direction inconsistency  
Hierarchical port(pin) ft601_data[2] direction INOUT is not consistent with the directions of the cell pins/ports found at that level of the netlist hierarchy. Please review the design and consider changing the port(pin) direction. Both drivers and loads were expected for the net (ft601_data[2]) connected to this Port, but both were not found.
Related violations: <none>

HPDR-1#24 Warning
Port pin direction inconsistency  
Hierarchical port(pin) ft601_data[30] direction INOUT is not consistent with the directions of the cell pins/ports found at that level of the netlist hierarchy. Please review the design and consider changing the port(pin) direction. Both drivers and loads were expected for the net (ft601_data[30]) connected to this Port, but both were not found.
Related violations: <none>

HPDR-1#25 Warning
Port pin direction inconsistency  
Hierarchical port(pin) ft601_data[31] direction INOUT is not consistent with the directions of the cell pins/ports found at that level of the netlist hierarchy. Please review the design and consider changing the port(pin) direction. Both drivers and loads were expected for the net (ft601_data[31]) connected to this Port, but both were not found.
Related violations: <none>

HPDR-1#26 Warning
Port pin direction inconsistency  
Hierarchical port(pin) ft601_data[3] direction INOUT is not consistent with the directions of the cell pins/ports found at that level of the netlist hierarchy. Please review the design and consider changing the port(pin) direction. Both drivers and loads were expected for the net (ft601_data[3]) connected to this Port, but both were not found.
Related violations: <none>

HPDR-1#27 Warning
Port pin direction inconsistency  
Hierarchical port(pin) ft601_data[4] direction INOUT is not consistent with the directions of the cell pins/ports found at that level of the netlist hierarchy. Please review the design and consider changing the port(pin) direction. Both drivers and loads were expected for the net (ft601_data[4]) connected to this Port, but both were not found.
Related violations: <none>

HPDR-1#28 Warning
Port pin direction inconsistency  
Hierarchical port(pin) ft601_data[5] direction INOUT is not consistent with the directions of the cell pins/ports found at that level of the netlist hierarchy. Please review the design and consider changing the port(pin) direction. Both drivers and loads were expected for the net (ft601_data[5]) connected to this Port, but both were not found.
Related violations: <none>

HPDR-1#29 Warning
Port pin direction inconsistency  
Hierarchical port(pin) ft601_data[6] direction INOUT is not consistent with the directions of the cell pins/ports found at that level of the netlist hierarchy. Please review the design and consider changing the port(pin) direction. Both drivers and loads were expected for the net (ft601_data[6]) connected to this Port, but both were not found.
Related violations: <none>

HPDR-1#30 Warning
Port pin direction inconsistency  
Hierarchical port(pin) ft601_data[7] direction INOUT is not consistent with the directions of the cell pins/ports found at that level of the netlist hierarchy. Please review the design and consider changing the port(pin) direction. Both drivers and loads were expected for the net (ft601_data[7]) connected to this Port, but both were not found.
Related violations: <none>

HPDR-1#31 Warning
Port pin direction inconsistency  
Hierarchical port(pin) ft601_data[8] direction INOUT is not consistent with the directions of the cell pins/ports found at that level of the netlist hierarchy. Please review the design and consider changing the port(pin) direction. Both drivers and loads were expected for the net (ft601_data[8]) connected to this Port, but both were not found.
Related violations: <none>

HPDR-1#32 Warning
Port pin direction inconsistency  
Hierarchical port(pin) ft601_data[9] direction INOUT is not consistent with the directions of the cell pins/ports found at that level of the netlist hierarchy. Please review the design and consider changing the port(pin) direction. Both drivers and loads were expected for the net (ft601_data[9]) connected to this Port, but both were not found.
Related violations: <none>

LUTAR-1#1 Warning
LUT drives async reset alert  
LUT cell i_pcileech_com/i_fifo_64_64_clk2_comrx_i_1, with 2 or more inputs, drives asynchronous preset/clear pin(s) i_pcileech_com/i_fifo_64_64_clk2_comrx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/rstblk/ngwrdrst.grst.g7serrst.gnsckt_wrst.gic_rst.rst_rd_reg2_inst/arststages_ff_reg[0]/PRE,
i_pcileech_com/i_fifo_64_64_clk2_comrx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/rstblk/ngwrdrst.grst.g7serrst.gnsckt_wrst.gic_rst.rst_rd_reg2_inst/arststages_ff_reg[1]/PRE,
i_pcileech_com/i_fifo_64_64_clk2_comrx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/rstblk/ngwrdrst.grst.g7serrst.gnsckt_wrst.rst_wr_reg2_inst/arststages_ff_reg[0]/PRE
i_pcileech_com/i_fifo_64_64_clk2_comrx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/rstblk/ngwrdrst.grst.g7serrst.gnsckt_wrst.rst_wr_reg2_inst/arststages_ff_reg[1]/PRE. The LUT may glitch and trigger an unexpected reset, even if it is a properly timed path.
Related violations: <none>

LUTAR-1#2 Warning
LUT drives async reset alert  
LUT cell i_pcileech_com/i_pcileech_ft601_optimized/i_fifo_cmd_rx_i_1, with 2 or more inputs, drives asynchronous preset/clear pin(s) i_pcileech_com/i_fifo_256_32_clk2_comtx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/rstblk/ngwrdrst.grst.g7serrst.gnsckt_wrst.gic_rst.rst_rd_reg2_inst/arststages_ff_reg[0]/PRE,
i_pcileech_com/i_fifo_256_32_clk2_comtx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/rstblk/ngwrdrst.grst.g7serrst.gnsckt_wrst.gic_rst.rst_rd_reg2_inst/arststages_ff_reg[1]/PRE,
i_pcileech_com/i_fifo_256_32_clk2_comtx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/rstblk/ngwrdrst.grst.g7serrst.gnsckt_wrst.rst_wr_reg2_inst/arststages_ff_reg[0]/PRE
i_pcileech_com/i_fifo_256_32_clk2_comtx/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/rstblk/ngwrdrst.grst.g7serrst.gnsckt_wrst.rst_wr_reg2_inst/arststages_ff_reg[1]/PRE. The LUT may glitch and trigger an unexpected reset, even if it is a properly timed path.
Related violations: <none>

LUTAR-1#3 Warning
LUT drives async reset alert  
LUT cell i_pcileech_fifo/i_pcie_7x_0_i_70, with 2 or more inputs, drives asynchronous preset/clear pin(s) i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/reset_n_reg1_reg/CLR
i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/reset_n_reg2_reg/CLR. The LUT may glitch and trigger an unexpected reset, even if it is a properly timed path.
Related violations: <none>

LUTAR-1#4 Warning
LUT drives async reset alert  
LUT cell i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/user_reset_out_i_1, with 2 or more inputs, drives asynchronous preset/clear pin(s) i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/user_reset_int_reg/PRE
i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/user_reset_out_reg/PRE. The LUT may glitch and trigger an unexpected reset, even if it is a properly timed path.
Related violations: <none>

LUTAR-1#5 Warning
LUT drives async reset alert  
LUT cell i_pcileech_pcie_a7/i_pcileech_pcie_cfg_a7/i_fifo_pcie_cfg_tx_i_1, with 2 or more inputs, drives asynchronous preset/clear pin(s) i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_cfgspace_shadow/i_fifo_49_49_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/rstblk/ngwrdrst.grst.g7serrst.gnsckt_wrst.gic_rst.rst_rd_reg2_inst/arststages_ff_reg[1]/PRE,
i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_cfgspace_shadow/i_fifo_49_49_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/rstblk/ngwrdrst.grst.g7serrst.gnsckt_wrst.rst_wr_reg2_inst/arststages_ff_reg[0]/PRE,
i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_cfgspace_shadow/i_fifo_49_49_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/rstblk/ngwrdrst.grst.g7serrst.gnsckt_wrst.rst_wr_reg2_inst/arststages_ff_reg[1]/PRE,
i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_dst_fifo/i_fifo_134_134_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/rstblk/ngwrdrst.grst.g7serrst.gnsckt_wrst.gic_rst.rst_rd_reg2_inst/arststages_ff_reg[0]/PRE,
i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_dst_fifo/i_fifo_134_134_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/rstblk/ngwrdrst.grst.g7serrst.gnsckt_wrst.gic_rst.rst_rd_reg2_inst/arststages_ff_reg[1]/PRE,
i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_dst_fifo/i_fifo_134_134_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/rstblk/ngwrdrst.grst.g7serrst.gnsckt_wrst.rst_wr_reg2_inst/arststages_ff_reg[0]/PRE,
i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_dst_fifo/i_fifo_134_134_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/rstblk/ngwrdrst.grst.g7serrst.gnsckt_wrst.rst_wr_reg2_inst/arststages_ff_reg[1]/PRE,
i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_src_fifo/i_fifo_134_134_clk2_rxfifo/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/rstblk/ngwrdrst.grst.g7serrst.gnsckt_wrst.gic_rst.rst_rd_reg2_inst/arststages_ff_reg[0]/PRE,
i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_src_fifo/i_fifo_134_134_clk2_rxfifo/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/rstblk/ngwrdrst.grst.g7serrst.gnsckt_wrst.gic_rst.rst_rd_reg2_inst/arststages_ff_reg[1]/PRE,
i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_src_fifo/i_fifo_134_134_clk2_rxfifo/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/rstblk/ngwrdrst.grst.g7serrst.gnsckt_wrst.rst_wr_reg2_inst/arststages_ff_reg[0]/PRE,
i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_src_fifo/i_fifo_134_134_clk2_rxfifo/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/rstblk/ngwrdrst.grst.g7serrst.gnsckt_wrst.rst_wr_reg2_inst/arststages_ff_reg[1]/PRE,
i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_src_fifo/i_fifo_1_1_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/rstblk/ngwrdrst.grst.g7serrst.gnsckt_wrst.gic_rst.rst_rd_reg2_inst/arststages_ff_reg[0]/PRE,
i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_src_fifo/i_fifo_1_1_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/rstblk/ngwrdrst.grst.g7serrst.gnsckt_wrst.gic_rst.rst_rd_reg2_inst/arststages_ff_reg[1]/PRE,
i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_src_fifo/i_fifo_1_1_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/rstblk/ngwrdrst.grst.g7serrst.gnsckt_wrst.rst_wr_reg2_inst/arststages_ff_reg[0]/PRE,
i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_src_fifo/i_fifo_1_1_clk2/U0/inst_fifo_gen/gconvfifo.rf/grf.rf/rstblk/ngwrdrst.grst.g7serrst.gnsckt_wrst.rst_wr_reg2_inst/arststages_ff_reg[1]/PRE
 (the first 15 of 28 listed). The LUT may glitch and trigger an unexpected reset, even if it is a properly timed path.
Related violations: <none>

TIMING-9#1 Warning
Unknown CDC Logic  
One or more asynchronous Clock Domain Crossing has been detected between 2 clock domains through a set_false_path or a set_clock_groups or set_max_delay -datapath_only constraint but no double-registers logic synchronizer has been found on the side of the capture clock. It is recommended to run report_cdc for a complete and detailed CDC coverage. Please consider using XPM_CDC to avoid Critical severities
Related violations: <none>

TIMING-10#1 Warning
Missing property on synchronizer  
One or more logic synchronizer has been detected between 2 clock domains but the synchronizer does not have the property ASYNC_REG defined on one or both registers. It is recommended to run report_cdc for a complete and detailed CDC coverage
Related violations: <none>

TIMING-18#1 Warning
Missing input or output delay  
An input delay is missing on user_sw2_n relative to the rising and/or falling clock edge(s) of net_clk, net_ft601_clk.
Related violations: <none>

TIMING-18#2 Warning
Missing input or output delay  
An output delay is missing on ft601_rst_n relative to the rising and/or falling clock edge(s) of net_clk.
Related violations: <none>

TIMING-18#3 Warning
Missing input or output delay  
An output delay is missing on user_ld1_n relative to the rising and/or falling clock edge(s) of pcie_sys_clk_p.
Related violations: <none>

TIMING-18#4 Warning
Missing input or output delay  
An output delay is missing on user_ld2_n relative to the rising and/or falling clock edge(s) of net_clk, net_ft601_clk.
Related violations: <none>


