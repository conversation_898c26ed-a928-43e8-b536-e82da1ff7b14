Copyright 1986-2022 Xilinx, Inc. All Rights Reserved. Copyright 2022-2025 Advanced Micro Devices, Inc. All Rights Reserved.
---------------------------------------------------------------------------------------------------------------------------------------------
| Tool Version : Vivado v.2024.2.2 (win64) Build 6060944 Thu Mar 06 19:10:01 MST 2025
| Date         : Wed Jun 18 17:37:34 2025
| Host         : DESKTOP-7T6N58Q running 64-bit major release  (build 9200)
| Command      : report_utilization -file pcileech_100t484_x1_top_utilization_placed.rpt -pb pcileech_100t484_x1_top_utilization_placed.pb
| Design       : pcileech_100t484_x1_top
| Device       : xc7a100tfgg484-2
| Speed File   : -2
| Design State : Fully Placed
---------------------------------------------------------------------------------------------------------------------------------------------

Utilization Design Information

Table of Contents
-----------------
1. Slice Logic
1.1 Summary of Registers by Type
2. Slice Logic Distribution
3. Memory
4. DSP
5. IO and GT Specific
6. Clocking
7. Specific Feature
8. Primitives
9. Black Boxes
10. Instantiated Netlists

1. Slice Logic
--------------

+----------------------------+------+-------+------------+-----------+-------+
|          Site Type         | Used | Fixed | Prohibited | Available | Util% |
+----------------------------+------+-------+------------+-----------+-------+
| Slice LUTs                 | 6437 |     0 |          0 |     63400 | 10.15 |
|   LUT as Logic             | 5584 |     0 |          0 |     63400 |  8.81 |
|   LUT as Memory            |  853 |     0 |          0 |     19000 |  4.49 |
|     LUT as Distributed RAM |  842 |     0 |            |           |       |
|     LUT as Shift Register  |   11 |     0 |            |           |       |
| Slice Registers            | 5034 |     0 |          0 |    126800 |  3.97 |
|   Register as Flip Flop    | 5034 |     0 |          0 |    126800 |  3.97 |
|   Register as Latch        |    0 |     0 |          0 |    126800 |  0.00 |
| F7 Muxes                   |    0 |     0 |          0 |     31700 |  0.00 |
| F8 Muxes                   |    0 |     0 |          0 |     15850 |  0.00 |
+----------------------------+------+-------+------------+-----------+-------+
* Warning! LUT value is adjusted to account for LUT combining.


1.1 Summary of Registers by Type
--------------------------------

+-------+--------------+-------------+--------------+
| Total | Clock Enable | Synchronous | Asynchronous |
+-------+--------------+-------------+--------------+
| 0     |            _ |           - |            - |
| 0     |            _ |           - |          Set |
| 0     |            _ |           - |        Reset |
| 0     |            _ |         Set |            - |
| 0     |            _ |       Reset |            - |
| 0     |          Yes |           - |            - |
| 161   |          Yes |           - |          Set |
| 572   |          Yes |           - |        Reset |
| 96    |          Yes |         Set |            - |
| 4205  |          Yes |       Reset |            - |
+-------+--------------+-------------+--------------+


2. Slice Logic Distribution
---------------------------

+--------------------------------------------+------+-------+------------+-----------+-------+
|                  Site Type                 | Used | Fixed | Prohibited | Available | Util% |
+--------------------------------------------+------+-------+------------+-----------+-------+
| Slice                                      | 2373 |     0 |          0 |     15850 | 14.97 |
|   SLICEL                                   | 1707 |     0 |            |           |       |
|   SLICEM                                   |  666 |     0 |            |           |       |
| LUT as Logic                               | 5584 |     0 |          0 |     63400 |  8.81 |
|   using O5 output only                     |    0 |       |            |           |       |
|   using O6 output only                     | 4618 |       |            |           |       |
|   using O5 and O6                          |  966 |       |            |           |       |
| LUT as Memory                              |  853 |     0 |          0 |     19000 |  4.49 |
|   LUT as Distributed RAM                   |  842 |     0 |            |           |       |
|     using O5 output only                   |    0 |       |            |           |       |
|     using O6 output only                   |  642 |       |            |           |       |
|     using O5 and O6                        |  200 |       |            |           |       |
|   LUT as Shift Register                    |   11 |     0 |            |           |       |
|     using O5 output only                   |    0 |       |            |           |       |
|     using O6 output only                   |    9 |       |            |           |       |
|     using O5 and O6                        |    2 |       |            |           |       |
| Slice Registers                            | 5034 |     0 |          0 |    126800 |  3.97 |
|   Register driven from within the Slice    | 2854 |       |            |           |       |
|   Register driven from outside the Slice   | 2180 |       |            |           |       |
|     LUT in front of the register is unused | 1505 |       |            |           |       |
|     LUT in front of the register is used   |  675 |       |            |           |       |
| Unique Control Sets                        |  149 |       |          0 |     15850 |  0.94 |
+--------------------------------------------+------+-------+------------+-----------+-------+
* * Note: Available Control Sets calculated as Slice * 1, Review the Control Sets Report for more information regarding control sets.


3. Memory
---------

+-------------------+------+-------+------------+-----------+-------+
|     Site Type     | Used | Fixed | Prohibited | Available | Util% |
+-------------------+------+-------+------------+-----------+-------+
| Block RAM Tile    | 23.5 |     0 |          0 |       135 | 17.41 |
|   RAMB36/FIFO*    |   23 |     8 |          0 |       135 | 17.04 |
|     RAMB36E1 only |   23 |       |            |           |       |
|   RAMB18          |    1 |     0 |          0 |       270 |  0.37 |
|     RAMB18E1 only |    1 |       |            |           |       |
+-------------------+------+-------+------------+-----------+-------+
* Note: Each Block RAM Tile only has one FIFO logic available and therefore can accommodate only one FIFO36E1 or one FIFO18E1. However, if a FIFO18E1 occupies a Block RAM Tile, that tile can still accommodate a RAMB18E1


4. DSP
------

+-----------+------+-------+------------+-----------+-------+
| Site Type | Used | Fixed | Prohibited | Available | Util% |
+-----------+------+-------+------------+-----------+-------+
| DSPs      |    0 |     0 |          0 |       240 |  0.00 |
+-----------+------+-------+------------+-----------+-------+


5. IO and GT Specific
---------------------

+-----------------------------+------+-------+------------+-----------+-------+
|          Site Type          | Used | Fixed | Prohibited | Available | Util% |
+-----------------------------+------+-------+------------+-----------+-------+
| Bonded IOB                  |   51 |    51 |          0 |       285 | 17.89 |
|   IOB Master Pads           |   27 |       |            |           |       |
|   IOB Slave Pads            |   24 |       |            |           |       |
| Bonded IPADs                |    4 |     4 |          0 |        14 | 28.57 |
| Bonded OPADs                |    2 |     2 |          0 |         8 | 25.00 |
| PHY_CONTROL                 |    0 |     0 |          0 |         6 |  0.00 |
| PHASER_REF                  |    0 |     0 |          0 |         6 |  0.00 |
| OUT_FIFO                    |    0 |     0 |          0 |        24 |  0.00 |
| IN_FIFO                     |    0 |     0 |          0 |        24 |  0.00 |
| IDELAYCTRL                  |    0 |     0 |          0 |         6 |  0.00 |
| IBUFDS                      |    0 |     0 |          0 |       274 |  0.00 |
| GTPE2_CHANNEL               |    1 |     1 |          0 |         4 | 25.00 |
| PHASER_OUT/PHASER_OUT_PHY   |    0 |     0 |          0 |        24 |  0.00 |
| PHASER_IN/PHASER_IN_PHY     |    0 |     0 |          0 |        24 |  0.00 |
| IDELAYE2/IDELAYE2_FINEDELAY |    0 |     0 |          0 |       300 |  0.00 |
| IBUFDS_GTE2                 |    1 |     1 |          0 |         2 | 50.00 |
| ILOGIC                      |    0 |     0 |          0 |       285 |  0.00 |
| OLOGIC                      |    0 |     0 |          0 |       285 |  0.00 |
+-----------------------------+------+-------+------------+-----------+-------+


6. Clocking
-----------

+------------+------+-------+------------+-----------+-------+
|  Site Type | Used | Fixed | Prohibited | Available | Util% |
+------------+------+-------+------------+-----------+-------+
| BUFGCTRL   |    7 |     0 |          0 |        32 | 21.88 |
| BUFIO      |    0 |     0 |          0 |        24 |  0.00 |
| MMCME2_ADV |    1 |     0 |          0 |         6 | 16.67 |
| PLLE2_ADV  |    0 |     0 |          0 |         6 |  0.00 |
| BUFMRCE    |    0 |     0 |          0 |        12 |  0.00 |
| BUFHCE     |    0 |     0 |          0 |        96 |  0.00 |
| BUFR       |    0 |     0 |          0 |        24 |  0.00 |
+------------+------+-------+------------+-----------+-------+


7. Specific Feature
-------------------

+-------------+------+-------+------------+-----------+--------+
|  Site Type  | Used | Fixed | Prohibited | Available |  Util% |
+-------------+------+-------+------------+-----------+--------+
| BSCANE2     |    0 |     0 |          0 |         4 |   0.00 |
| CAPTUREE2   |    0 |     0 |          0 |         1 |   0.00 |
| DNA_PORT    |    0 |     0 |          0 |         1 |   0.00 |
| EFUSE_USR   |    0 |     0 |          0 |         1 |   0.00 |
| FRAME_ECCE2 |    0 |     0 |          0 |         1 |   0.00 |
| ICAPE2      |    0 |     0 |          0 |         2 |   0.00 |
| PCIE_2_1    |    1 |     1 |          0 |         1 | 100.00 |
| STARTUPE2   |    1 |     0 |          0 |         1 | 100.00 |
| XADC        |    0 |     0 |          0 |         1 |   0.00 |
+-------------+------+-------+------------+-----------+--------+


8. Primitives
-------------

+---------------+------+----------------------+
|    Ref Name   | Used |  Functional Category |
+---------------+------+----------------------+
| FDRE          | 4205 |         Flop & Latch |
| LUT6          | 2582 |                  LUT |
| LUT2          | 1329 |                  LUT |
| LUT5          |  833 |                  LUT |
| LUT4          |  826 |                  LUT |
| LUT3          |  770 |                  LUT |
| RAMD64E       |  640 |   Distributed Memory |
| FDCE          |  572 |         Flop & Latch |
| CARRY4        |  408 |           CarryLogic |
| RAMD32        |  302 |   Distributed Memory |
| LUT1          |  210 |                  LUT |
| FDPE          |  161 |         Flop & Latch |
| RAMS32        |  100 |   Distributed Memory |
| FDSE          |   96 |         Flop & Latch |
| OBUFT         |   32 |                   IO |
| RAMB36E1      |   23 |         Block Memory |
| OBUF          |   14 |                   IO |
| IBUF          |   11 |                   IO |
| SRLC32E       |    7 |   Distributed Memory |
| SRL16E        |    6 |   Distributed Memory |
| BUFG          |    6 |                Clock |
| STARTUPE2     |    1 |               Others |
| RAMB18E1      |    1 |         Block Memory |
| PCIE_2_1      |    1 | Specialized Resource |
| MMCME2_ADV    |    1 |                Clock |
| IBUFDS_GTE2   |    1 |                   IO |
| GTPE2_COMMON  |    1 |                   IO |
| GTPE2_CHANNEL |    1 |                   IO |
| BUFGCTRL      |    1 |                Clock |
+---------------+------+----------------------+


9. Black Boxes
--------------

+----------+------+
| Ref Name | Used |
+----------+------+


10. Instantiated Netlists
-------------------------

+-----------------------------+------+
|           Ref Name          | Used |
+-----------------------------+------+
| fifo_34_34                  |    2 |
| pcie_7x_0                   |    1 |
| fifo_74_74_clk1_bar_rd1     |    1 |
| fifo_64_64_clk2_comrx       |    1 |
| fifo_64_64_clk1_fifocmd     |    1 |
| fifo_64_64                  |    1 |
| fifo_49_49_clk2             |    1 |
| fifo_43_43_clk2             |    1 |
| fifo_32_32_clk2             |    1 |
| fifo_32_32_clk1_comtx       |    1 |
| fifo_256_32_clk2_comtx      |    1 |
| fifo_1_1_clk2               |    1 |
| fifo_141_141_clk1_bar_wr    |    1 |
| fifo_134_134_clk2_rxfifo    |    1 |
| fifo_134_134_clk2           |    1 |
| fifo_134_134_clk1_bar_rdrsp |    1 |
| fifo_129_129_clk1           |    1 |
| bram_pcie_cfgspace          |    1 |
| bram_bar_zero4k             |    1 |
+-----------------------------+------+


