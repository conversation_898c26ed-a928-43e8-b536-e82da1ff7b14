
*** Running vivado
    with args -log pcileech_100t484_x1_top.vdi -applog -m64 -product Vivado -messageDb vivado.pb -mode batch -source pcileech_100t484_x1_top.tcl -notrace



****** Vivado v2024.2.2 (64-bit)
  **** SW Build 6060944 on Thu Mar 06 19:10:01 MST 2025
  **** IP Build 6050500 on Thu Mar  6 23:33:39 MST 2025
  **** SharedData Build 6060542 on Thu Mar 06 10:31:07 MST 2025
  **** Start of session at: Wed Jun 18 17:36:38 2025
    ** Copyright 1986-2022 Xilinx, Inc. All Rights Reserved.
    ** Copyright 2022-2025 Advanced Micro Devices, Inc. All Rights Reserved.

source pcileech_100t484_x1_top.tcl -notrace
create_project: Time (s): cpu = 00:00:04 ; elapsed = 00:00:05 . Memory (MB): peak = 619.574 ; gain = 192.023
Command: link_design -top pcileech_100t484_x1_top -part xc7a100tfgg484-2
Design is defaulting to srcset: sources_1
Design is defaulting to constrset: constrs_1
INFO: [Device 21-403] Loading part xc7a100tfgg484-2
INFO: [Project 1-454] Reading design checkpoint 'g:/FPGA/********/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.gen/sources_1/ip/fifo_256_32_clk2_comtx/fifo_256_32_clk2_comtx.dcp' for cell 'i_pcileech_com/i_fifo_256_32_clk2_comtx'
INFO: [Project 1-454] Reading design checkpoint 'g:/FPGA/********/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.gen/sources_1/ip/fifo_32_32_clk1_comtx/fifo_32_32_clk1_comtx.dcp' for cell 'i_pcileech_com/i_fifo_32_32_clk2_comtx'
INFO: [Project 1-454] Reading design checkpoint 'g:/FPGA/********/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.gen/sources_1/ip/fifo_64_64_clk2_comrx/fifo_64_64_clk2_comrx.dcp' for cell 'i_pcileech_com/i_fifo_64_64_clk2_comrx'
INFO: [Project 1-454] Reading design checkpoint 'g:/FPGA/********/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.gen/sources_1/ip/fifo_64_64_clk1_fifocmd/fifo_64_64_clk1_fifocmd.dcp' for cell 'i_pcileech_fifo/i_fifo_cmd_rx'
INFO: [Project 1-454] Reading design checkpoint 'g:/FPGA/********/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.gen/sources_1/ip/fifo_34_34/fifo_34_34.dcp' for cell 'i_pcileech_fifo/i_fifo_cmd_tx'
INFO: [Project 1-454] Reading design checkpoint 'g:/FPGA/********/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.gen/sources_1/ip/pcie_7x_0/pcie_7x_0.dcp' for cell 'i_pcileech_pcie_a7/i_pcie_7x_0'
INFO: [Project 1-454] Reading design checkpoint 'g:/FPGA/********/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.gen/sources_1/ip/fifo_32_32_clk2/fifo_32_32_clk2.dcp' for cell 'i_pcileech_pcie_a7/i_pcileech_pcie_cfg_a7/i_fifo_pcie_cfg_rx'
INFO: [Project 1-454] Reading design checkpoint 'g:/FPGA/********/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.gen/sources_1/ip/fifo_64_64/fifo_64_64.dcp' for cell 'i_pcileech_pcie_a7/i_pcileech_pcie_cfg_a7/i_fifo_pcie_cfg_tx'
INFO: [Project 1-454] Reading design checkpoint 'g:/FPGA/********/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.gen/sources_1/ip/bram_bar_zero4k/bram_bar_zero4k.dcp' for cell 'i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_bar_controller/i_bar0/i_bram_bar_zero4k'
INFO: [Project 1-454] Reading design checkpoint 'g:/FPGA/********/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.gen/sources_1/ip/fifo_134_134_clk1_bar_rdrsp/fifo_134_134_clk1_bar_rdrsp.dcp' for cell 'i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_bar_controller/i_pcileech_tlps128_bar_rdengine/i_fifo_134_134_clk1_bar_rdrsp'
INFO: [Project 1-454] Reading design checkpoint 'g:/FPGA/********/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.gen/sources_1/ip/fifo_74_74_clk1_bar_rd1/fifo_74_74_clk1_bar_rd1.dcp' for cell 'i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_bar_controller/i_pcileech_tlps128_bar_rdengine/i_fifo_74_74_clk1_bar_rd1'
INFO: [Project 1-454] Reading design checkpoint 'g:/FPGA/********/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.gen/sources_1/ip/fifo_141_141_clk1_bar_wr/fifo_141_141_clk1_bar_wr.dcp' for cell 'i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_bar_controller/i_pcileech_tlps128_bar_wrengine/i_fifo_141_141_clk1_bar_wr'
INFO: [Project 1-454] Reading design checkpoint 'g:/FPGA/********/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.gen/sources_1/ip/fifo_43_43_clk2/fifo_43_43_clk2.dcp' for cell 'i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_cfgspace_shadow/i_fifo_43_43_clk2'
INFO: [Project 1-454] Reading design checkpoint 'g:/FPGA/********/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.gen/sources_1/ip/fifo_49_49_clk2/fifo_49_49_clk2.dcp' for cell 'i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_cfgspace_shadow/i_fifo_49_49_clk2'
INFO: [Project 1-454] Reading design checkpoint 'g:/FPGA/********/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.gen/sources_1/ip/fifo_129_129_clk1/fifo_129_129_clk1.dcp' for cell 'i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_cfgspace_shadow/i_pcileech_cfgspace_pcie_tx/i_fifo_129_129_clk1'
INFO: [Project 1-454] Reading design checkpoint 'g:/FPGA/********/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.gen/sources_1/ip/bram_pcie_cfgspace/bram_pcie_cfgspace.dcp' for cell 'i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_cfgspace_shadow/i_pcileech_mem_wrap/i_bram_pcie_cfgspace'
INFO: [Project 1-454] Reading design checkpoint 'g:/FPGA/********/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.gen/sources_1/ip/drom_pcie_cfgspace_writemask/drom_pcie_cfgspace_writemask.dcp' for cell 'i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_cfgspace_shadow/i_pcileech_mem_wrap/i_drom_pcie_cfgspace_writemask'
INFO: [Project 1-454] Reading design checkpoint 'g:/FPGA/********/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.gen/sources_1/ip/fifo_134_134_clk2/fifo_134_134_clk2.dcp' for cell 'i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_dst_fifo/i_fifo_134_134_clk2'
INFO: [Project 1-454] Reading design checkpoint 'g:/FPGA/********/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.gen/sources_1/ip/fifo_134_134_clk2_rxfifo/fifo_134_134_clk2_rxfifo.dcp' for cell 'i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_src_fifo/i_fifo_134_134_clk2_rxfifo'
INFO: [Project 1-454] Reading design checkpoint 'g:/FPGA/********/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.gen/sources_1/ip/fifo_1_1_clk2/fifo_1_1_clk2.dcp' for cell 'i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_src_fifo/i_fifo_1_1_clk2'
Netlist sorting complete. Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.347 . Memory (MB): peak = 930.727 ; gain = 0.000
INFO: [Netlist 29-17] Analyzing 1263 Unisim elements for replacement
INFO: [Netlist 29-28] Unisim Transformation completed in 0 CPU seconds
INFO: [Project 1-479] Netlist was created with Vivado 2024.2.2
INFO: [Project 1-570] Preparing netlist for logic optimization
Parsing XDC File [g:/FPGA/********/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.gen/sources_1/ip/fifo_64_64_clk1_fifocmd/fifo_64_64_clk1_fifocmd.xdc] for cell 'i_pcileech_fifo/i_fifo_cmd_rx/U0'
Finished Parsing XDC File [g:/FPGA/********/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.gen/sources_1/ip/fifo_64_64_clk1_fifocmd/fifo_64_64_clk1_fifocmd.xdc] for cell 'i_pcileech_fifo/i_fifo_cmd_rx/U0'
Parsing XDC File [g:/FPGA/********/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.gen/sources_1/ip/fifo_34_34/fifo_34_34.xdc] for cell 'i_pcileech_fifo/i_fifo_cmd_tx/U0'
Finished Parsing XDC File [g:/FPGA/********/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.gen/sources_1/ip/fifo_34_34/fifo_34_34.xdc] for cell 'i_pcileech_fifo/i_fifo_cmd_tx/U0'
Parsing XDC File [g:/FPGA/********/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.gen/sources_1/ip/fifo_34_34/fifo_34_34.xdc] for cell 'i_pcileech_fifo/i_fifo_loop_tx/U0'
Finished Parsing XDC File [g:/FPGA/********/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.gen/sources_1/ip/fifo_34_34/fifo_34_34.xdc] for cell 'i_pcileech_fifo/i_fifo_loop_tx/U0'
Parsing XDC File [g:/FPGA/********/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.gen/sources_1/ip/pcie_7x_0/source/pcie_7x_0-PCIE_X0Y0.xdc] for cell 'i_pcileech_pcie_a7/i_pcie_7x_0/inst'
INFO: [Timing 38-35] Done setting XDC timing constraints. [g:/FPGA/********/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.gen/sources_1/ip/pcie_7x_0/source/pcie_7x_0-PCIE_X0Y0.xdc:133]
INFO: [Timing 38-2] Deriving generated clocks [g:/FPGA/********/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.gen/sources_1/ip/pcie_7x_0/source/pcie_7x_0-PCIE_X0Y0.xdc:133]
create_generated_clock: Time (s): cpu = 00:00:03 ; elapsed = 00:00:05 . Memory (MB): peak = 1680.445 ; gain = 593.379
Finished Parsing XDC File [g:/FPGA/********/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.gen/sources_1/ip/pcie_7x_0/source/pcie_7x_0-PCIE_X0Y0.xdc] for cell 'i_pcileech_pcie_a7/i_pcie_7x_0/inst'
Parsing XDC File [g:/FPGA/********/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.gen/sources_1/ip/fifo_134_134_clk1_bar_rdrsp/fifo_134_134_clk1_bar_rdrsp.xdc] for cell 'i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_bar_controller/i_pcileech_tlps128_bar_rdengine/i_fifo_134_134_clk1_bar_rdrsp/U0'
Finished Parsing XDC File [g:/FPGA/********/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.gen/sources_1/ip/fifo_134_134_clk1_bar_rdrsp/fifo_134_134_clk1_bar_rdrsp.xdc] for cell 'i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_bar_controller/i_pcileech_tlps128_bar_rdengine/i_fifo_134_134_clk1_bar_rdrsp/U0'
Parsing XDC File [g:/FPGA/********/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.gen/sources_1/ip/fifo_43_43_clk2/fifo_43_43_clk2.xdc] for cell 'i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_cfgspace_shadow/i_fifo_43_43_clk2/U0'
Finished Parsing XDC File [g:/FPGA/********/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.gen/sources_1/ip/fifo_43_43_clk2/fifo_43_43_clk2.xdc] for cell 'i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_cfgspace_shadow/i_fifo_43_43_clk2/U0'
Parsing XDC File [g:/FPGA/********/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.gen/sources_1/ip/fifo_134_134_clk2_rxfifo/fifo_134_134_clk2_rxfifo.xdc] for cell 'i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_src_fifo/i_fifo_134_134_clk2_rxfifo/U0'
Finished Parsing XDC File [g:/FPGA/********/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.gen/sources_1/ip/fifo_134_134_clk2_rxfifo/fifo_134_134_clk2_rxfifo.xdc] for cell 'i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_src_fifo/i_fifo_134_134_clk2_rxfifo/U0'
Parsing XDC File [g:/FPGA/********/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.gen/sources_1/ip/fifo_64_64_clk2_comrx/fifo_64_64_clk2_comrx.xdc] for cell 'i_pcileech_com/i_fifo_64_64_clk2_comrx/U0'
Finished Parsing XDC File [g:/FPGA/********/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.gen/sources_1/ip/fifo_64_64_clk2_comrx/fifo_64_64_clk2_comrx.xdc] for cell 'i_pcileech_com/i_fifo_64_64_clk2_comrx/U0'
Parsing XDC File [g:/FPGA/********/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.gen/sources_1/ip/fifo_141_141_clk1_bar_wr/fifo_141_141_clk1_bar_wr.xdc] for cell 'i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_bar_controller/i_pcileech_tlps128_bar_wrengine/i_fifo_141_141_clk1_bar_wr/U0'
Finished Parsing XDC File [g:/FPGA/********/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.gen/sources_1/ip/fifo_141_141_clk1_bar_wr/fifo_141_141_clk1_bar_wr.xdc] for cell 'i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_bar_controller/i_pcileech_tlps128_bar_wrengine/i_fifo_141_141_clk1_bar_wr/U0'
Parsing XDC File [g:/FPGA/********/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.gen/sources_1/ip/fifo_64_64/fifo_64_64.xdc] for cell 'i_pcileech_pcie_a7/i_pcileech_pcie_cfg_a7/i_fifo_pcie_cfg_tx/U0'
Finished Parsing XDC File [g:/FPGA/********/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.gen/sources_1/ip/fifo_64_64/fifo_64_64.xdc] for cell 'i_pcileech_pcie_a7/i_pcileech_pcie_cfg_a7/i_fifo_pcie_cfg_tx/U0'
Parsing XDC File [g:/FPGA/********/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.gen/sources_1/ip/fifo_134_134_clk2/fifo_134_134_clk2.xdc] for cell 'i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_dst_fifo/i_fifo_134_134_clk2/U0'
Finished Parsing XDC File [g:/FPGA/********/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.gen/sources_1/ip/fifo_134_134_clk2/fifo_134_134_clk2.xdc] for cell 'i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_dst_fifo/i_fifo_134_134_clk2/U0'
Parsing XDC File [g:/FPGA/********/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.gen/sources_1/ip/fifo_49_49_clk2/fifo_49_49_clk2.xdc] for cell 'i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_cfgspace_shadow/i_fifo_49_49_clk2/U0'
Finished Parsing XDC File [g:/FPGA/********/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.gen/sources_1/ip/fifo_49_49_clk2/fifo_49_49_clk2.xdc] for cell 'i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_cfgspace_shadow/i_fifo_49_49_clk2/U0'
Parsing XDC File [g:/FPGA/********/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.gen/sources_1/ip/fifo_74_74_clk1_bar_rd1/fifo_74_74_clk1_bar_rd1.xdc] for cell 'i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_bar_controller/i_pcileech_tlps128_bar_rdengine/i_fifo_74_74_clk1_bar_rd1/U0'
Finished Parsing XDC File [g:/FPGA/********/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.gen/sources_1/ip/fifo_74_74_clk1_bar_rd1/fifo_74_74_clk1_bar_rd1.xdc] for cell 'i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_bar_controller/i_pcileech_tlps128_bar_rdengine/i_fifo_74_74_clk1_bar_rd1/U0'
Parsing XDC File [g:/FPGA/********/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.gen/sources_1/ip/fifo_32_32_clk1_comtx/fifo_32_32_clk1_comtx.xdc] for cell 'i_pcileech_com/i_fifo_32_32_clk2_comtx/U0'
Finished Parsing XDC File [g:/FPGA/********/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.gen/sources_1/ip/fifo_32_32_clk1_comtx/fifo_32_32_clk1_comtx.xdc] for cell 'i_pcileech_com/i_fifo_32_32_clk2_comtx/U0'
Parsing XDC File [g:/FPGA/********/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.gen/sources_1/ip/fifo_32_32_clk2/fifo_32_32_clk2.xdc] for cell 'i_pcileech_pcie_a7/i_pcileech_pcie_cfg_a7/i_fifo_pcie_cfg_rx/U0'
Finished Parsing XDC File [g:/FPGA/********/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.gen/sources_1/ip/fifo_32_32_clk2/fifo_32_32_clk2.xdc] for cell 'i_pcileech_pcie_a7/i_pcileech_pcie_cfg_a7/i_fifo_pcie_cfg_rx/U0'
Parsing XDC File [g:/FPGA/********/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.gen/sources_1/ip/fifo_256_32_clk2_comtx/fifo_256_32_clk2_comtx.xdc] for cell 'i_pcileech_com/i_fifo_256_32_clk2_comtx/U0'
Finished Parsing XDC File [g:/FPGA/********/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.gen/sources_1/ip/fifo_256_32_clk2_comtx/fifo_256_32_clk2_comtx.xdc] for cell 'i_pcileech_com/i_fifo_256_32_clk2_comtx/U0'
Parsing XDC File [g:/FPGA/********/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.gen/sources_1/ip/fifo_129_129_clk1/fifo_129_129_clk1.xdc] for cell 'i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_cfgspace_shadow/i_pcileech_cfgspace_pcie_tx/i_fifo_129_129_clk1/U0'
Finished Parsing XDC File [g:/FPGA/********/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.gen/sources_1/ip/fifo_129_129_clk1/fifo_129_129_clk1.xdc] for cell 'i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_cfgspace_shadow/i_pcileech_cfgspace_pcie_tx/i_fifo_129_129_clk1/U0'
Parsing XDC File [g:/FPGA/********/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.gen/sources_1/ip/fifo_1_1_clk2/fifo_1_1_clk2.xdc] for cell 'i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_src_fifo/i_fifo_1_1_clk2/U0'
Finished Parsing XDC File [g:/FPGA/********/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.gen/sources_1/ip/fifo_1_1_clk2/fifo_1_1_clk2.xdc] for cell 'i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_src_fifo/i_fifo_1_1_clk2/U0'
Parsing XDC File [G:/FPGA/********/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.srcs/constrs_1/imports/src/pcileech_100t484_x1_captaindma_100t.xdc]
WARNING: [Vivado 12-180] No cells matched 'i_pcileech_com/i_pcileech_ft601/FT601_OE_N_reg'. [G:/FPGA/********/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.srcs/constrs_1/imports/src/pcileech_100t484_x1_captaindma_100t.xdc:78]
CRITICAL WARNING: [Common 17-55] 'set_property' expects at least one object. [G:/FPGA/********/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.srcs/constrs_1/imports/src/pcileech_100t484_x1_captaindma_100t.xdc:78]
Resolution: If [get_<value>] was used to populate the object, check to make sure this command returns at least one valid object.
WARNING: [Vivado 12-180] No cells matched 'i_pcileech_com/i_pcileech_ft601/FT601_RD_N_reg'. [G:/FPGA/********/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.srcs/constrs_1/imports/src/pcileech_100t484_x1_captaindma_100t.xdc:79]
CRITICAL WARNING: [Common 17-55] 'set_property' expects at least one object. [G:/FPGA/********/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.srcs/constrs_1/imports/src/pcileech_100t484_x1_captaindma_100t.xdc:79]
Resolution: If [get_<value>] was used to populate the object, check to make sure this command returns at least one valid object.
WARNING: [Vivado 12-180] No cells matched 'i_pcileech_com/i_pcileech_ft601/FT601_WR_N_reg'. [G:/FPGA/********/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.srcs/constrs_1/imports/src/pcileech_100t484_x1_captaindma_100t.xdc:80]
CRITICAL WARNING: [Common 17-55] 'set_property' expects at least one object. [G:/FPGA/********/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.srcs/constrs_1/imports/src/pcileech_100t484_x1_captaindma_100t.xdc:80]
Resolution: If [get_<value>] was used to populate the object, check to make sure this command returns at least one valid object.
WARNING: [Vivado 12-180] No cells matched 'i_pcileech_com/i_pcileech_ft601/FT601_DATA_OUT_reg[0][*]'. [G:/FPGA/********/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.srcs/constrs_1/imports/src/pcileech_100t484_x1_captaindma_100t.xdc:81]
CRITICAL WARNING: [Common 17-55] 'set_property' expects at least one object. [G:/FPGA/********/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.srcs/constrs_1/imports/src/pcileech_100t484_x1_captaindma_100t.xdc:81]
Resolution: If [get_<value>] was used to populate the object, check to make sure this command returns at least one valid object.
WARNING: [Vivado 12-508] No pins matched 'i_pcileech_com/i_pcileech_ft601/OE_reg/C'. [G:/FPGA/********/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.srcs/constrs_1/imports/src/pcileech_100t484_x1_captaindma_100t.xdc:83]
CRITICAL WARNING: [Vivado 12-4739] set_multicycle_path:No valid object(s) found for '-from [get_pins i_pcileech_com/i_pcileech_ft601/OE_reg/C]'. [G:/FPGA/********/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.srcs/constrs_1/imports/src/pcileech_100t484_x1_captaindma_100t.xdc:83]
Resolution: Check if the specified object(s) exists in the current design. If it does, ensure that the correct design hierarchy was specified for the object. If you are working with clocks, make sure create_clock was used to create the clock object before it is referenced.
Finished Parsing XDC File [G:/FPGA/********/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.srcs/constrs_1/imports/src/pcileech_100t484_x1_captaindma_100t.xdc]
Parsing XDC File [g:/FPGA/********/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.gen/sources_1/ip/pcie_7x_0/source/ip_xilinx_pcie_2_1_7x_late.xdc] for cell 'i_pcileech_pcie_a7/i_pcie_7x_0/inst'
WARNING: [Vivado_Tcl 4-921] Waiver ID 'CDC-7' -to list should not be empty. [g:/FPGA/********/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.gen/sources_1/ip/pcie_7x_0/source/ip_xilinx_pcie_2_1_7x_late.xdc:82]
WARNING: [Vivado_Tcl 4-921] Waiver ID 'CDC-1' -to list should not be empty. [g:/FPGA/********/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.gen/sources_1/ip/pcie_7x_0/source/ip_xilinx_pcie_2_1_7x_late.xdc:87]
WARNING: [Vivado_Tcl 4-921] Waiver ID 'CDC-1' -to list should not be empty. [g:/FPGA/********/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.gen/sources_1/ip/pcie_7x_0/source/ip_xilinx_pcie_2_1_7x_late.xdc:88]
WARNING: [Vivado_Tcl 4-921] Waiver ID 'CDC-1' -to list should not be empty. [g:/FPGA/********/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.gen/sources_1/ip/pcie_7x_0/source/ip_xilinx_pcie_2_1_7x_late.xdc:89]
WARNING: [Vivado_Tcl 4-921] Waiver ID 'CDC-1' -to list should not be empty. [g:/FPGA/********/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.gen/sources_1/ip/pcie_7x_0/source/ip_xilinx_pcie_2_1_7x_late.xdc:90]
Finished Parsing XDC File [g:/FPGA/********/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.gen/sources_1/ip/pcie_7x_0/source/ip_xilinx_pcie_2_1_7x_late.xdc] for cell 'i_pcileech_pcie_a7/i_pcie_7x_0/inst'
Parsing XDC File [g:/FPGA/********/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.gen/sources_1/ip/fifo_43_43_clk2/fifo_43_43_clk2_clocks.xdc] for cell 'i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_cfgspace_shadow/i_fifo_43_43_clk2/U0'
Finished Parsing XDC File [g:/FPGA/********/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.gen/sources_1/ip/fifo_43_43_clk2/fifo_43_43_clk2_clocks.xdc] for cell 'i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_cfgspace_shadow/i_fifo_43_43_clk2/U0'
Parsing XDC File [g:/FPGA/********/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.gen/sources_1/ip/fifo_134_134_clk2_rxfifo/fifo_134_134_clk2_rxfifo_clocks.xdc] for cell 'i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_src_fifo/i_fifo_134_134_clk2_rxfifo/U0'
Finished Parsing XDC File [g:/FPGA/********/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.gen/sources_1/ip/fifo_134_134_clk2_rxfifo/fifo_134_134_clk2_rxfifo_clocks.xdc] for cell 'i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_src_fifo/i_fifo_134_134_clk2_rxfifo/U0'
Parsing XDC File [g:/FPGA/********/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.gen/sources_1/ip/fifo_64_64_clk2_comrx/fifo_64_64_clk2_comrx_clocks.xdc] for cell 'i_pcileech_com/i_fifo_64_64_clk2_comrx/U0'
Finished Parsing XDC File [g:/FPGA/********/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.gen/sources_1/ip/fifo_64_64_clk2_comrx/fifo_64_64_clk2_comrx_clocks.xdc] for cell 'i_pcileech_com/i_fifo_64_64_clk2_comrx/U0'
Parsing XDC File [g:/FPGA/********/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.gen/sources_1/ip/fifo_64_64/fifo_64_64_clocks.xdc] for cell 'i_pcileech_pcie_a7/i_pcileech_pcie_cfg_a7/i_fifo_pcie_cfg_tx/U0'
Finished Parsing XDC File [g:/FPGA/********/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.gen/sources_1/ip/fifo_64_64/fifo_64_64_clocks.xdc] for cell 'i_pcileech_pcie_a7/i_pcileech_pcie_cfg_a7/i_fifo_pcie_cfg_tx/U0'
Parsing XDC File [g:/FPGA/********/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.gen/sources_1/ip/fifo_134_134_clk2/fifo_134_134_clk2_clocks.xdc] for cell 'i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_dst_fifo/i_fifo_134_134_clk2/U0'
Finished Parsing XDC File [g:/FPGA/********/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.gen/sources_1/ip/fifo_134_134_clk2/fifo_134_134_clk2_clocks.xdc] for cell 'i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_dst_fifo/i_fifo_134_134_clk2/U0'
Parsing XDC File [g:/FPGA/********/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.gen/sources_1/ip/fifo_49_49_clk2/fifo_49_49_clk2_clocks.xdc] for cell 'i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_cfgspace_shadow/i_fifo_49_49_clk2/U0'
Finished Parsing XDC File [g:/FPGA/********/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.gen/sources_1/ip/fifo_49_49_clk2/fifo_49_49_clk2_clocks.xdc] for cell 'i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_cfgspace_shadow/i_fifo_49_49_clk2/U0'
Parsing XDC File [g:/FPGA/********/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.gen/sources_1/ip/fifo_32_32_clk2/fifo_32_32_clk2_clocks.xdc] for cell 'i_pcileech_pcie_a7/i_pcileech_pcie_cfg_a7/i_fifo_pcie_cfg_rx/U0'
Finished Parsing XDC File [g:/FPGA/********/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.gen/sources_1/ip/fifo_32_32_clk2/fifo_32_32_clk2_clocks.xdc] for cell 'i_pcileech_pcie_a7/i_pcileech_pcie_cfg_a7/i_fifo_pcie_cfg_rx/U0'
Parsing XDC File [g:/FPGA/********/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.gen/sources_1/ip/fifo_256_32_clk2_comtx/fifo_256_32_clk2_comtx_clocks.xdc] for cell 'i_pcileech_com/i_fifo_256_32_clk2_comtx/U0'
Finished Parsing XDC File [g:/FPGA/********/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.gen/sources_1/ip/fifo_256_32_clk2_comtx/fifo_256_32_clk2_comtx_clocks.xdc] for cell 'i_pcileech_com/i_fifo_256_32_clk2_comtx/U0'
Parsing XDC File [g:/FPGA/********/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.gen/sources_1/ip/fifo_1_1_clk2/fifo_1_1_clk2_clocks.xdc] for cell 'i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_src_fifo/i_fifo_1_1_clk2/U0'
Finished Parsing XDC File [g:/FPGA/********/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.gen/sources_1/ip/fifo_1_1_clk2/fifo_1_1_clk2_clocks.xdc] for cell 'i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_src_fifo/i_fifo_1_1_clk2/U0'
INFO: [Project 1-1714] 56 XPM XDC files have been applied to the design.
INFO: [Opt 31-138] Pushed 0 inverter(s) to 0 load pin(s).
INFO: [Project 1-1687] 9 scoped IP constraints or related sub-commands were skipped due to synthesis logic optimizations usually triggered by constant connectivity or unconnected output pins. To review the skipped constraints and messages, run the command 'set_param netlist.IPMsgFiltering false' before opening the design.
Netlist sorting complete. Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.005 . Memory (MB): peak = 1680.445 ; gain = 0.000
INFO: [Project 1-111] Unisim Transformation Summary:
  A total of 432 instances were transformed.
  RAM16X1D => RAM32X1D (RAMD32(x2)): 1 instance 
  RAM32M => RAM32M (RAMD32(x6), RAMS32(x2)): 59 instances
  RAM32X1D => RAM32X1D (RAMD32(x2)): 2 instances
  RAM64M => RAM64M (RAMD64E(x4)): 350 instances
  RAM64X1D => RAM64X1D (RAMD64E(x2)): 20 instances

31 Infos, 10 Warnings, 5 Critical Warnings and 0 Errors encountered.
link_design completed successfully
link_design: Time (s): cpu = 00:00:09 ; elapsed = 00:00:14 . Memory (MB): peak = 1680.445 ; gain = 1045.859
Command: opt_design
Attempting to get a license for feature 'Implementation' and/or device 'xc7a100t'
INFO: [Common 17-349] Got license for feature 'Implementation' and/or device 'xc7a100t'
Running DRC as a precondition to command opt_design

Starting DRC Task
INFO: [DRC 23-27] Running DRC with 2 threads
INFO: [Project 1-461] DRC finished with 0 Errors, 32 Warnings
INFO: [Project 1-462] Please refer to the DRC report (report_drc) for more information.

Time (s): cpu = 00:00:01 ; elapsed = 00:00:02 . Memory (MB): peak = 1680.445 ; gain = 0.000

Starting Cache Timing Information Task
INFO: [Timing 38-35] Done setting XDC timing constraints.
Ending Cache Timing Information Task | Checksum: 2384ba510

Time (s): cpu = 00:00:01 ; elapsed = 00:00:00.556 . Memory (MB): peak = 1680.445 ; gain = 0.000

Starting Logic Optimization Task

Phase 1 Initialization

Phase 1.1 Core Generation And Design Setup
Phase 1.1 Core Generation And Design Setup | Checksum: 2384ba510

Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.024 . Memory (MB): peak = 2067.863 ; gain = 0.000

Phase 1.2 Setup Constraints And Sort Netlist
Phase 1.2 Setup Constraints And Sort Netlist | Checksum: 2384ba510

Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.035 . Memory (MB): peak = 2067.863 ; gain = 0.000
Phase 1 Initialization | Checksum: 2384ba510

Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.037 . Memory (MB): peak = 2067.863 ; gain = 0.000

Phase 2 Timer Update And Timing Data Collection

Phase 2.1 Timer Update
Phase 2.1 Timer Update | Checksum: 2384ba510

Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.377 . Memory (MB): peak = 2067.863 ; gain = 0.000

Phase 2.2 Timing Data Collection
Phase 2.2 Timing Data Collection | Checksum: 2384ba510

Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.408 . Memory (MB): peak = 2067.863 ; gain = 0.000
Phase 2 Timer Update And Timing Data Collection | Checksum: 2384ba510

Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.410 . Memory (MB): peak = 2067.863 ; gain = 0.000

Phase 3 Retarget
INFO: [Opt 31-1851] Number of loadless carry chains removed were: 0
INFO: [Opt 31-1834] Total Chains To Be Transformed Were: 0 AND Number of Transformed insts Created are: 0
INFO: [Opt 31-1566] Pulled 5 inverters resulting in an inversion of 16 pins
INFO: [Opt 31-138] Pushed 6 inverter(s) to 36 load pin(s).
INFO: [Opt 31-49] Retargeted 0 cell(s).
Phase 3 Retarget | Checksum: 1d658a3b9

Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.618 . Memory (MB): peak = 2067.863 ; gain = 0.000
Retarget | Checksum: 1d658a3b9
INFO: [Opt 31-389] Phase Retarget created 66 cells and removed 168 cells
INFO: [Opt 31-1021] In phase Retarget, 78 netlist objects are constrained preventing optimization. Please run opt_design with -debug_log to get more detail. 

Phase 4 Constant propagation
INFO: [Opt 31-138] Pushed 0 inverter(s) to 0 load pin(s).
Phase 4 Constant propagation | Checksum: 21098f0ac

Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.703 . Memory (MB): peak = 2067.863 ; gain = 0.000
Constant propagation | Checksum: 21098f0ac
INFO: [Opt 31-389] Phase Constant propagation created 16 cells and removed 92 cells
INFO: [Opt 31-1021] In phase Constant propagation, 78 netlist objects are constrained preventing optimization. Please run opt_design with -debug_log to get more detail. 

Phase 5 Sweep
INFO: [Constraints 18-11670] Building netlist checker database with flags, 0x8
Done building netlist checker database: Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.007 . Memory (MB): peak = 2067.863 ; gain = 0.000
INFO: [Constraints 18-11670] Building netlist checker database with flags, 0x8
Done building netlist checker database: Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.006 . Memory (MB): peak = 2067.863 ; gain = 0.000
Phase 5 Sweep | Checksum: 22785af15

Time (s): cpu = 00:00:01 ; elapsed = 00:00:02 . Memory (MB): peak = 2067.863 ; gain = 0.000
Sweep | Checksum: 22785af15
INFO: [Opt 31-389] Phase Sweep created 0 cells and removed 7992 cells
INFO: [Opt 31-1021] In phase Sweep, 210 netlist objects are constrained preventing optimization. Please run opt_design with -debug_log to get more detail. 

Phase 6 BUFG optimization
Phase 6 BUFG optimization | Checksum: 24ee36fd4

Time (s): cpu = 00:00:01 ; elapsed = 00:00:02 . Memory (MB): peak = 2067.863 ; gain = 0.000
BUFG optimization | Checksum: 24ee36fd4
INFO: [Opt 31-662] Phase BUFG optimization created 0 cells of which 0 are BUFGs and removed 0 cells.

Phase 7 Shift Register Optimization
INFO: [Opt 31-1064] SRL Remap converted 0 SRLs to 0 registers and converted 0 registers of register chains to 0 SRLs
Phase 7 Shift Register Optimization | Checksum: 24ee36fd4

Time (s): cpu = 00:00:01 ; elapsed = 00:00:02 . Memory (MB): peak = 2067.863 ; gain = 0.000
Shift Register Optimization | Checksum: 24ee36fd4
INFO: [Opt 31-389] Phase Shift Register Optimization created 0 cells and removed 0 cells

Phase 8 Post Processing Netlist
Phase 8 Post Processing Netlist | Checksum: 28b75fd15

Time (s): cpu = 00:00:01 ; elapsed = 00:00:02 . Memory (MB): peak = 2067.863 ; gain = 0.000
Post Processing Netlist | Checksum: 28b75fd15
INFO: [Opt 31-389] Phase Post Processing Netlist created 0 cells and removed 0 cells
INFO: [Opt 31-1021] In phase Post Processing Netlist, 90 netlist objects are constrained preventing optimization. Please run opt_design with -debug_log to get more detail. 

Phase 9 Finalization

Phase 9.1 Finalizing Design Cores and Updating Shapes
Phase 9.1 Finalizing Design Cores and Updating Shapes | Checksum: 1d2374ac0

Time (s): cpu = 00:00:01 ; elapsed = 00:00:02 . Memory (MB): peak = 2067.863 ; gain = 0.000

Phase 9.2 Verifying Netlist Connectivity

Starting Connectivity Check Task

Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.016 . Memory (MB): peak = 2067.863 ; gain = 0.000
Phase 9.2 Verifying Netlist Connectivity | Checksum: 1d2374ac0

Time (s): cpu = 00:00:01 ; elapsed = 00:00:02 . Memory (MB): peak = 2067.863 ; gain = 0.000
Phase 9 Finalization | Checksum: 1d2374ac0

Time (s): cpu = 00:00:01 ; elapsed = 00:00:02 . Memory (MB): peak = 2067.863 ; gain = 0.000
Opt_design Change Summary
=========================


-------------------------------------------------------------------------------------------------------------------------
|  Phase                        |  #Cells created  |  #Cells Removed  |  #Constrained objects preventing optimizations  |
-------------------------------------------------------------------------------------------------------------------------
|  Retarget                     |              66  |             168  |                                             78  |
|  Constant propagation         |              16  |              92  |                                             78  |
|  Sweep                        |               0  |            7992  |                                            210  |
|  BUFG optimization            |               0  |               0  |                                              0  |
|  Shift Register Optimization  |               0  |               0  |                                              0  |
|  Post Processing Netlist      |               0  |               0  |                                             90  |
-------------------------------------------------------------------------------------------------------------------------


Ending Logic Optimization Task | Checksum: 1d2374ac0

Time (s): cpu = 00:00:01 ; elapsed = 00:00:02 . Memory (MB): peak = 2067.863 ; gain = 0.000

Starting Power Optimization Task
INFO: [Pwropt 34-132] Skipping clock gating for clocks with a period < 2.00 ns.
INFO: [Power 33-23] Power model is not available for i_STARTUPE2
INFO: [Timing 38-35] Done setting XDC timing constraints.
Running Vector-less Activity Propagation...
INFO: [Pwropt 34-9] Applying IDT optimizations ...
INFO: [Pwropt 34-10] Applying ODC optimizations ...

Finished Running Vector-less Activity Propagation


Starting PowerOpt Patch Enables Task
INFO: [Pwropt 34-162] WRITE_MODE attribute of 15 BRAM(s) out of a total of 24 has been updated to save power. Run report_power_opt to get a complete listing of the BRAMs updated.
INFO: [Pwropt 34-201] Structural ODC has moved 0 WE to EN ports
Number of BRAM Ports augmented: 0 newly gated: 0 Total Ports: 48
Ending PowerOpt Patch Enables Task | Checksum: 1d2374ac0

Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.070 . Memory (MB): peak = 2240.680 ; gain = 0.000
Ending Power Optimization Task | Checksum: 1d2374ac0

Time (s): cpu = 00:00:03 ; elapsed = 00:00:02 . Memory (MB): peak = 2240.680 ; gain = 172.816

Starting Final Cleanup Task
Ending Final Cleanup Task | Checksum: 1d2374ac0

Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.003 . Memory (MB): peak = 2240.680 ; gain = 0.000

Starting Netlist Obfuscation Task
Netlist sorting complete. Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.004 . Memory (MB): peak = 2240.680 ; gain = 0.000
Ending Netlist Obfuscation Task | Checksum: 1d2374ac0

Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.006 . Memory (MB): peak = 2240.680 ; gain = 0.000
INFO: [Common 17-83] Releasing license: Implementation
63 Infos, 10 Warnings, 5 Critical Warnings and 0 Errors encountered.
opt_design completed successfully
opt_design: Time (s): cpu = 00:00:07 ; elapsed = 00:00:09 . Memory (MB): peak = 2240.680 ; gain = 560.234
INFO: [Vivado 12-24828] Executing command : report_drc -file pcileech_100t484_x1_top_drc_opted.rpt -pb pcileech_100t484_x1_top_drc_opted.pb -rpx pcileech_100t484_x1_top_drc_opted.rpx
Command: report_drc -file pcileech_100t484_x1_top_drc_opted.rpt -pb pcileech_100t484_x1_top_drc_opted.pb -rpx pcileech_100t484_x1_top_drc_opted.rpx
INFO: [IP_Flow 19-1839] IP Catalog is up to date.
INFO: [DRC 23-27] Running DRC with 2 threads
INFO: [Vivado_Tcl 2-168] The results of DRC are in file G:/FPGA/********/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.runs/impl_1/pcileech_100t484_x1_top_drc_opted.rpt.
report_drc completed successfully
INFO: [Timing 38-35] Done setting XDC timing constraints.
INFO: [Timing 38-480] Writing timing data to binary archive.
Write ShapeDB Complete: Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.014 . Memory (MB): peak = 2240.680 ; gain = 0.000
Wrote PlaceDB: Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.019 . Memory (MB): peak = 2240.680 ; gain = 0.000
Wrote PulsedLatchDB: Time (s): cpu = 00:00:00 ; elapsed = 00:00:00 . Memory (MB): peak = 2240.680 ; gain = 0.000
Writing XDEF routing.
Writing XDEF routing logical nets.
Writing XDEF routing special nets.
Wrote RouteStorage: Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.020 . Memory (MB): peak = 2240.680 ; gain = 0.000
Wrote Netlist Cache: Time (s): cpu = 00:00:00 ; elapsed = 00:00:00 . Memory (MB): peak = 2240.680 ; gain = 0.000
Wrote Device Cache: Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.004 . Memory (MB): peak = 2240.680 ; gain = 0.000
Write Physdb Complete: Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.049 . Memory (MB): peak = 2240.680 ; gain = 0.000
INFO: [Common 17-1381] The checkpoint 'G:/FPGA/********/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.runs/impl_1/pcileech_100t484_x1_top_opt.dcp' has been generated.
Command: place_design
Attempting to get a license for feature 'Implementation' and/or device 'xc7a100t'
INFO: [Common 17-349] Got license for feature 'Implementation' and/or device 'xc7a100t'
INFO: [Common 17-83] Releasing license: Implementation
INFO: [DRC 23-27] Running DRC with 2 threads
INFO: [Vivado_Tcl 4-198] DRC finished with 0 Errors
INFO: [Vivado_Tcl 4-199] Please refer to the DRC report (report_drc) for more information.
Running DRC as a precondition to command place_design
INFO: [DRC 23-27] Running DRC with 2 threads
INFO: [Vivado_Tcl 4-198] DRC finished with 0 Errors, 55 Warnings
INFO: [Vivado_Tcl 4-199] Please refer to the DRC report (report_drc) for more information.
INFO: [Place 30-611] Multithreading enabled for place_design using a maximum of 2 CPUs

Starting Placer Task

Phase 1 Placer Initialization

Phase 1.1 Placer Initialization Netlist Sorting
Netlist sorting complete. Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.004 . Memory (MB): peak = 2240.680 ; gain = 0.000
Phase 1.1 Placer Initialization Netlist Sorting | Checksum: 15e831c43

Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.007 . Memory (MB): peak = 2240.680 ; gain = 0.000
Netlist sorting complete. Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.003 . Memory (MB): peak = 2240.680 ; gain = 0.000

Phase 1.2 IO Placement/ Clock Placement/ Build Placer Device
Phase 1.2 IO Placement/ Clock Placement/ Build Placer Device | Checksum: 195f66fe1

Time (s): cpu = 00:00:01 ; elapsed = 00:00:00.617 . Memory (MB): peak = 2240.680 ; gain = 0.000

Phase 1.3 Build Placer Netlist Model
Phase 1.3 Build Placer Netlist Model | Checksum: 1ac67d353

Time (s): cpu = 00:00:02 ; elapsed = 00:00:02 . Memory (MB): peak = 2240.680 ; gain = 0.000

Phase 1.4 Constrain Clocks/Macros
Phase 1.4 Constrain Clocks/Macros | Checksum: 1ac67d353

Time (s): cpu = 00:00:02 ; elapsed = 00:00:02 . Memory (MB): peak = 2240.680 ; gain = 0.000
Phase 1 Placer Initialization | Checksum: 1ac67d353

Time (s): cpu = 00:00:02 ; elapsed = 00:00:02 . Memory (MB): peak = 2240.680 ; gain = 0.000

Phase 2 Global Placement

Phase 2.1 Floorplanning
Phase 2.1 Floorplanning | Checksum: 1aebea585

Time (s): cpu = 00:00:02 ; elapsed = 00:00:03 . Memory (MB): peak = 2240.680 ; gain = 0.000

Phase 2.2 Update Timing before SLR Path Opt
Phase 2.2 Update Timing before SLR Path Opt | Checksum: 227fa7056

Time (s): cpu = 00:00:02 ; elapsed = 00:00:03 . Memory (MB): peak = 2240.680 ; gain = 0.000

Phase 2.3 Post-Processing in Floorplanning
Phase 2.3 Post-Processing in Floorplanning | Checksum: 1c32dd604

Time (s): cpu = 00:00:02 ; elapsed = 00:00:03 . Memory (MB): peak = 2240.680 ; gain = 0.000

Phase 2.4 Global Place Phase1
Phase 2.4 Global Place Phase1 | Checksum: 30640349d

Time (s): cpu = 00:00:04 ; elapsed = 00:00:07 . Memory (MB): peak = 2240.680 ; gain = 0.000

Phase 2.5 Global Place Phase2

Phase 2.5.1 UpdateTiming Before Physical Synthesis
Phase 2.5.1 UpdateTiming Before Physical Synthesis | Checksum: 3493fed9e

Time (s): cpu = 00:00:04 ; elapsed = 00:00:07 . Memory (MB): peak = 2240.680 ; gain = 0.000

Phase 2.5.2 Physical Synthesis In Placer
INFO: [Physopt 32-1035] Found 0 LUTNM shape to break, 360 LUT instances to create LUTNM shape
INFO: [Physopt 32-1044] Break lutnm for timing: one critical 0, two critical 0, total 0, new lutff created 0
INFO: [Physopt 32-1138] End 1 Pass. Optimized 145 nets or LUTs. Breaked 0 LUT, combined 145 existing LUTs and moved 0 existing LUT
INFO: [Physopt 32-65] No nets found for high-fanout optimization.
INFO: [Physopt 32-232] Optimized 0 net. Created 0 new instance.
INFO: [Physopt 32-775] End 1 Pass. Optimized 0 net or cell. Created 0 new cell, deleted 0 existing cell and moved 0 existing cell
INFO: [Physopt 32-456] No candidate cells for DSP register optimization found in the design.
INFO: [Physopt 32-775] End 2 Pass. Optimized 0 net or cell. Created 0 new cell, deleted 0 existing cell and moved 0 existing cell
INFO: [Physopt 32-1123] No candidate cells found for Shift Register to Pipeline optimization
INFO: [Physopt 32-775] End 2 Pass. Optimized 0 net or cell. Created 0 new cell, deleted 0 existing cell and moved 0 existing cell
INFO: [Physopt 32-1401] No candidate cells found for Shift Register optimization.
INFO: [Physopt 32-677] No candidate cells for Shift Register optimization found in the design
INFO: [Physopt 32-775] End 1 Pass. Optimized 0 net or cell. Created 0 new cell, deleted 0 existing cell and moved 0 existing cell
INFO: [Physopt 32-526] No candidate cells for BRAM register optimization found in the design
INFO: [Physopt 32-775] End 1 Pass. Optimized 0 net or cell. Created 0 new cell, deleted 0 existing cell and moved 0 existing cell
INFO: [Physopt 32-846] No candidate cells for URAM register optimization found in the design
INFO: [Physopt 32-775] End 1 Pass. Optimized 0 net or cell. Created 0 new cell, deleted 0 existing cell and moved 0 existing cell
INFO: [Physopt 32-846] No candidate cells for URAM register optimization found in the design
INFO: [Physopt 32-775] End 1 Pass. Optimized 0 net or cell. Created 0 new cell, deleted 0 existing cell and moved 0 existing cell
INFO: [Physopt 32-949] No candidate nets found for dynamic/static region interface net replication
INFO: [Physopt 32-775] End 1 Pass. Optimized 0 net or cell. Created 0 new cell, deleted 0 existing cell and moved 0 existing cell
Netlist sorting complete. Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.004 . Memory (MB): peak = 2240.680 ; gain = 0.000

Summary of Physical Synthesis Optimizations
============================================


-----------------------------------------------------------------------------------------------------------------------------------------------------------
|  Optimization                                     |  Added Cells  |  Removed Cells  |  Optimized Cells/Nets  |  Dont Touch  |  Iterations  |  Elapsed   |
-----------------------------------------------------------------------------------------------------------------------------------------------------------
|  LUT Combining                                    |            0  |            145  |                   145  |           0  |           1  |  00:00:00  |
|  Retime                                           |            0  |              0  |                     0  |           0  |           1  |  00:00:00  |
|  Very High Fanout                                 |            0  |              0  |                     0  |           0  |           1  |  00:00:00  |
|  DSP Register                                     |            0  |              0  |                     0  |           0  |           1  |  00:00:00  |
|  Shift Register to Pipeline                       |            0  |              0  |                     0  |           0  |           1  |  00:00:00  |
|  Shift Register                                   |            0  |              0  |                     0  |           0  |           1  |  00:00:00  |
|  BRAM Register                                    |            0  |              0  |                     0  |           0  |           1  |  00:00:00  |
|  URAM Register                                    |            0  |              0  |                     0  |           0  |           1  |  00:00:00  |
|  Dynamic/Static Region Interface Net Replication  |            0  |              0  |                     0  |           0  |           1  |  00:00:00  |
|  Total                                            |            0  |            145  |                   145  |           0  |           9  |  00:00:00  |
-----------------------------------------------------------------------------------------------------------------------------------------------------------


Phase 2.5.2 Physical Synthesis In Placer | Checksum: 23f927638

Time (s): cpu = 00:00:05 ; elapsed = 00:00:08 . Memory (MB): peak = 2240.680 ; gain = 0.000
Phase 2.5 Global Place Phase2 | Checksum: 21badf51c

Time (s): cpu = 00:00:05 ; elapsed = 00:00:08 . Memory (MB): peak = 2240.680 ; gain = 0.000
Phase 2 Global Placement | Checksum: 21badf51c

Time (s): cpu = 00:00:05 ; elapsed = 00:00:08 . Memory (MB): peak = 2240.680 ; gain = 0.000

Phase 3 Detail Placement

Phase 3.1 Commit Multi Column Macros
Phase 3.1 Commit Multi Column Macros | Checksum: 23a761805

Time (s): cpu = 00:00:05 ; elapsed = 00:00:09 . Memory (MB): peak = 2240.680 ; gain = 0.000

Phase 3.2 Commit Most Macros & LUTRAMs
Phase 3.2 Commit Most Macros & LUTRAMs | Checksum: 1496b9420

Time (s): cpu = 00:00:05 ; elapsed = 00:00:09 . Memory (MB): peak = 2240.680 ; gain = 0.000

Phase 3.3 Area Swap Optimization
Phase 3.3 Area Swap Optimization | Checksum: 1ce3df08f

Time (s): cpu = 00:00:05 ; elapsed = 00:00:10 . Memory (MB): peak = 2240.680 ; gain = 0.000

Phase 3.4 Pipeline Register Optimization
Phase 3.4 Pipeline Register Optimization | Checksum: 18eec3229

Time (s): cpu = 00:00:05 ; elapsed = 00:00:10 . Memory (MB): peak = 2240.680 ; gain = 0.000

Phase 3.5 Fast Optimization
Phase 3.5 Fast Optimization | Checksum: 1bfe9f9c2

Time (s): cpu = 00:00:06 ; elapsed = 00:00:11 . Memory (MB): peak = 2240.680 ; gain = 0.000

Phase 3.6 Small Shape Detail Placement
Phase 3.6 Small Shape Detail Placement | Checksum: 1be84f264

Time (s): cpu = 00:00:07 ; elapsed = 00:00:12 . Memory (MB): peak = 2240.680 ; gain = 0.000

Phase 3.7 Re-assign LUT pins
Phase 3.7 Re-assign LUT pins | Checksum: 15a215f4f

Time (s): cpu = 00:00:07 ; elapsed = 00:00:13 . Memory (MB): peak = 2240.680 ; gain = 0.000

Phase 3.8 Pipeline Register Optimization
Phase 3.8 Pipeline Register Optimization | Checksum: 173948cde

Time (s): cpu = 00:00:07 ; elapsed = 00:00:13 . Memory (MB): peak = 2240.680 ; gain = 0.000

Phase 3.9 Fast Optimization
Phase 3.9 Fast Optimization | Checksum: 1b0b3f1dd

Time (s): cpu = 00:00:08 ; elapsed = 00:00:14 . Memory (MB): peak = 2240.680 ; gain = 0.000
Phase 3 Detail Placement | Checksum: 1b0b3f1dd

Time (s): cpu = 00:00:08 ; elapsed = 00:00:14 . Memory (MB): peak = 2240.680 ; gain = 0.000

Phase 4 Post Placement Optimization and Clean-Up

Phase 4.1 Post Commit Optimization
INFO: [Timing 38-35] Done setting XDC timing constraints.

Phase 4.1.1 Post Placement Optimization
Post Placement Optimization Initialization | Checksum: 1e6652f1c

Phase ******* BUFG Insertion

Starting Physical Synthesis Task

Phase 1 Physical Synthesis Initialization
INFO: [Physopt 32-721] Multithreading enabled for phys_opt_design using a maximum of 2 CPUs
INFO: [Physopt 32-619] Estimated Timing Summary | WNS=-1.530 | TNS=-32.483 |
Phase 1 Physical Synthesis Initialization | Checksum: 17fa06d25

Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.308 . Memory (MB): peak = 2240.680 ; gain = 0.000
INFO: [Place 46-33] Processed net i_pcileech_pcie_a7/i_pcileech_pcie_cfg_a7/rst_subsys, BUFG insertion was skipped due to placement/routing conflicts.
INFO: [Place 46-56] BUFG insertion identified 1 candidate nets. Inserted BUFG: 0, Replicated BUFG Driver: 0, Skipped due to Placement/Routing Conflicts: 1, Skipped due to Timing Degradation: 0, Skipped due to netlist editing failed: 0.
Ending Physical Synthesis Task | Checksum: 1d5d59a05

Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.421 . Memory (MB): peak = 2240.680 ; gain = 0.000
Phase ******* BUFG Insertion | Checksum: 1e6652f1c

Time (s): cpu = 00:00:10 ; elapsed = 00:00:16 . Memory (MB): peak = 2240.680 ; gain = 0.000

Phase ******* Post Placement Timing Optimization
INFO: [Place 30-746] Post Placement Timing Summary WNS=-1.493. For the most accurate timing information please run report_timing.
Phase ******* Post Placement Timing Optimization | Checksum: 18716b911

Time (s): cpu = 00:00:12 ; elapsed = 00:00:19 . Memory (MB): peak = 2240.680 ; gain = 0.000

Time (s): cpu = 00:00:12 ; elapsed = 00:00:19 . Memory (MB): peak = 2240.680 ; gain = 0.000
Phase 4.1 Post Commit Optimization | Checksum: 18716b911

Time (s): cpu = 00:00:12 ; elapsed = 00:00:19 . Memory (MB): peak = 2240.680 ; gain = 0.000

Phase 4.2 Post Placement Cleanup
Phase 4.2 Post Placement Cleanup | Checksum: 18716b911

Time (s): cpu = 00:00:12 ; elapsed = 00:00:19 . Memory (MB): peak = 2240.680 ; gain = 0.000

Phase 4.3 Placer Reporting

Phase 4.3.1 Print Estimated Congestion
INFO: [Place 30-612] Post-Placement Estimated Congestion 
 ____________________________________________________
|           | Global Congestion | Short Congestion  |
| Direction | Region Size       | Region Size       |
|___________|___________________|___________________|
|      North|                1x1|                2x2|
|___________|___________________|___________________|
|      South|                1x1|                2x2|
|___________|___________________|___________________|
|       East|                1x1|                1x1|
|___________|___________________|___________________|
|       West|                1x1|                1x1|
|___________|___________________|___________________|

Phase 4.3.1 Print Estimated Congestion | Checksum: 18716b911

Time (s): cpu = 00:00:12 ; elapsed = 00:00:19 . Memory (MB): peak = 2240.680 ; gain = 0.000
Phase 4.3 Placer Reporting | Checksum: 18716b911

Time (s): cpu = 00:00:12 ; elapsed = 00:00:19 . Memory (MB): peak = 2240.680 ; gain = 0.000

Phase 4.4 Final Placement Cleanup
Netlist sorting complete. Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.007 . Memory (MB): peak = 2240.680 ; gain = 0.000

Time (s): cpu = 00:00:12 ; elapsed = 00:00:19 . Memory (MB): peak = 2240.680 ; gain = 0.000
Phase 4 Post Placement Optimization and Clean-Up | Checksum: 1ea0707ea

Time (s): cpu = 00:00:12 ; elapsed = 00:00:19 . Memory (MB): peak = 2240.680 ; gain = 0.000
Ending Placer Task | Checksum: 1ca8ed3c5

Time (s): cpu = 00:00:12 ; elapsed = 00:00:19 . Memory (MB): peak = 2240.680 ; gain = 0.000
107 Infos, 10 Warnings, 5 Critical Warnings and 0 Errors encountered.
place_design completed successfully
place_design: Time (s): cpu = 00:00:14 ; elapsed = 00:00:21 . Memory (MB): peak = 2240.680 ; gain = 0.000
INFO: [Vivado 12-24838] Running report commands "report_control_sets, report_io, report_utilization" in parallel.
Running report generation with 2 threads.
INFO: [Vivado 12-24828] Executing command : report_io -file pcileech_100t484_x1_top_io_placed.rpt
report_io: Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.058 . Memory (MB): peak = 2240.680 ; gain = 0.000
INFO: [Vivado 12-24828] Executing command : report_utilization -file pcileech_100t484_x1_top_utilization_placed.rpt -pb pcileech_100t484_x1_top_utilization_placed.pb
INFO: [Vivado 12-24828] Executing command : report_control_sets -verbose -file pcileech_100t484_x1_top_control_sets_placed.rpt
report_control_sets: Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.022 . Memory (MB): peak = 2240.680 ; gain = 0.000
INFO: [Timing 38-480] Writing timing data to binary archive.
Write ShapeDB Complete: Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.023 . Memory (MB): peak = 2240.680 ; gain = 0.000
Wrote PlaceDB: Time (s): cpu = 00:00:01 ; elapsed = 00:00:00.598 . Memory (MB): peak = 2240.680 ; gain = 0.000
Wrote PulsedLatchDB: Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.001 . Memory (MB): peak = 2240.680 ; gain = 0.000
Writing XDEF routing.
Writing XDEF routing logical nets.
Writing XDEF routing special nets.
Wrote RouteStorage: Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.018 . Memory (MB): peak = 2240.680 ; gain = 0.000
Wrote Netlist Cache: Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.010 . Memory (MB): peak = 2240.680 ; gain = 0.000
Wrote Device Cache: Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.004 . Memory (MB): peak = 2240.680 ; gain = 0.000
Write Physdb Complete: Time (s): cpu = 00:00:01 ; elapsed = 00:00:00.638 . Memory (MB): peak = 2240.680 ; gain = 0.000
INFO: [Common 17-1381] The checkpoint 'G:/FPGA/********/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.runs/impl_1/pcileech_100t484_x1_top_placed.dcp' has been generated.
Command: phys_opt_design
Attempting to get a license for feature 'Implementation' and/or device 'xc7a100t'
INFO: [Common 17-349] Got license for feature 'Implementation' and/or device 'xc7a100t'

Starting Initial Update Timing Task

Time (s): cpu = 00:00:01 ; elapsed = 00:00:01 . Memory (MB): peak = 2240.680 ; gain = 0.000
INFO: [Vivado_Tcl 4-1435] PhysOpt_Tcl_Interface Runtime Before Starting Physical Synthesis Task | CPU: 1.00s |  WALL: 1.06s
Netlist sorting complete. Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.004 . Memory (MB): peak = 2240.680 ; gain = 0.000

Starting Physical Synthesis Task

Phase 1 Physical Synthesis Initialization
INFO: [Physopt 32-721] Multithreading enabled for phys_opt_design using a maximum of 2 CPUs
INFO: [Physopt 32-619] Estimated Timing Summary | WNS=-1.493 | TNS=-31.517 |
Phase 1 Physical Synthesis Initialization | Checksum: 1ac486932

Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.617 . Memory (MB): peak = 2240.680 ; gain = 0.000
INFO: [Physopt 32-619] Estimated Timing Summary | WNS=-1.493 | TNS=-31.517 |

Phase 2 DSP Register Optimization
INFO: [Physopt 32-456] No candidate cells for DSP register optimization found in the design.
INFO: [Physopt 32-775] End 2 Pass. Optimized 0 net or cell. Created 0 new cell, deleted 0 existing cell and moved 0 existing cell
Phase 2 DSP Register Optimization | Checksum: 1ac486932

Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.677 . Memory (MB): peak = 2240.680 ; gain = 0.000

Phase 3 Critical Path Optimization
INFO: [Physopt 32-619] Estimated Timing Summary | WNS=-1.493 | TNS=-31.517 |
INFO: [Physopt 32-81] Processed net i_pcileech_com/i_pcileech_ft601_optimized/ft601_data_TRI[0]. Replicated 4 times.
INFO: [Physopt 32-735] Processed net i_pcileech_com/i_pcileech_ft601_optimized/ft601_data_TRI[0]. Optimization improves timing on the net.
INFO: [Physopt 32-619] Estimated Timing Summary | WNS=-0.440 | TNS=-10.095 |
INFO: [Physopt 32-81] Processed net i_pcileech_com/i_pcileech_ft601_optimized/ft601_data_TRI[0]_repN_3. Replicated 1 times.
INFO: [Physopt 32-735] Processed net i_pcileech_com/i_pcileech_ft601_optimized/ft601_data_TRI[0]_repN_3. Optimization improves timing on the net.
INFO: [Physopt 32-619] Estimated Timing Summary | WNS=-0.422 | TNS=-9.680 |
INFO: [Physopt 32-81] Processed net i_pcileech_com/i_pcileech_ft601_optimized/ft601_data_TRI[0]_repN_4. Replicated 1 times.
INFO: [Physopt 32-735] Processed net i_pcileech_com/i_pcileech_ft601_optimized/ft601_data_TRI[0]_repN_4. Optimization improves timing on the net.
INFO: [Physopt 32-619] Estimated Timing Summary | WNS=-0.420 | TNS=-9.619 |
INFO: [Physopt 32-601] Processed net i_pcileech_com/i_pcileech_ft601_optimized/ft601_data_TRI[0]_repN_5. Net driver i_pcileech_com/i_pcileech_ft601_optimized/FT601_OE_N_reg_replica_5 was replaced.
INFO: [Physopt 32-735] Processed net i_pcileech_com/i_pcileech_ft601_optimized/ft601_data_TRI[0]_repN_5. Optimization improves timing on the net.
INFO: [Physopt 32-619] Estimated Timing Summary | WNS=-0.419 | TNS=-9.384 |
INFO: [Physopt 32-702] Processed net i_pcileech_com/i_pcileech_ft601_optimized/ft601_data_TRI[0]_repN_5. Optimizations did not improve timing on the net.
INFO: [Physopt 32-702] Processed net ft601_clk_IBUF. Optimizations did not improve timing on the net.
INFO: [Physopt 32-702] Processed net ft601_clk. Optimizations did not improve timing on the net.
INFO: [Physopt 32-702] Processed net i_pcileech_com/i_pcileech_ft601_optimized/ft601_data_TRI[0]_repN_5. Optimizations did not improve timing on the net.
INFO: [Physopt 32-702] Processed net ft601_clk_IBUF. Optimizations did not improve timing on the net.
INFO: [Physopt 32-702] Processed net ft601_clk. Optimizations did not improve timing on the net.
INFO: [Physopt 32-619] Estimated Timing Summary | WNS=-0.419 | TNS=-9.384 |
Netlist sorting complete. Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.008 . Memory (MB): peak = 2240.680 ; gain = 0.000
Phase 3 Critical Path Optimization | Checksum: 1ac486932

Time (s): cpu = 00:00:01 ; elapsed = 00:00:01 . Memory (MB): peak = 2240.680 ; gain = 0.000

Phase 4 Critical Path Optimization
INFO: [Physopt 32-619] Estimated Timing Summary | WNS=-0.419 | TNS=-9.384 |
INFO: [Physopt 32-702] Processed net i_pcileech_com/i_pcileech_ft601_optimized/ft601_data_TRI[0]_repN_5. Optimizations did not improve timing on the net.
INFO: [Physopt 32-702] Processed net ft601_clk_IBUF. Optimizations did not improve timing on the net.
INFO: [Physopt 32-702] Processed net ft601_clk. Optimizations did not improve timing on the net.
INFO: [Physopt 32-702] Processed net i_pcileech_com/i_pcileech_ft601_optimized/ft601_data_TRI[0]_repN_5. Optimizations did not improve timing on the net.
INFO: [Physopt 32-702] Processed net ft601_clk_IBUF. Optimizations did not improve timing on the net.
INFO: [Physopt 32-702] Processed net ft601_clk. Optimizations did not improve timing on the net.
INFO: [Physopt 32-619] Estimated Timing Summary | WNS=-0.419 | TNS=-9.384 |
Netlist sorting complete. Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.004 . Memory (MB): peak = 2240.680 ; gain = 0.000
Phase 4 Critical Path Optimization | Checksum: 1ac486932

Time (s): cpu = 00:00:01 ; elapsed = 00:00:01 . Memory (MB): peak = 2240.680 ; gain = 0.000
Netlist sorting complete. Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.003 . Memory (MB): peak = 2240.680 ; gain = 0.000
INFO: [Physopt 32-603] Post Physical Optimization Timing Summary | WNS=-0.419 | TNS=-9.384 |

Summary of Physical Synthesis Optimizations
============================================


-------------------------------------------------------------------------------------------------------------------------------------------------------------
|  Optimization   |  WNS Gain (ns)  |  TNS Gain (ns)  |  Added Cells  |  Removed Cells  |  Optimized Cells/Nets  |  Dont Touch  |  Iterations  |  Elapsed   |
-------------------------------------------------------------------------------------------------------------------------------------------------------------
|  DSP Register   |          0.000  |          0.000  |            0  |              0  |                     0  |           0  |           1  |  00:00:00  |
|  Critical Path  |          1.074  |         22.133  |            6  |              0  |                     4  |           0  |           2  |  00:00:01  |
|  Total          |          1.074  |         22.133  |            6  |              0  |                     4  |           0  |           3  |  00:00:01  |
-------------------------------------------------------------------------------------------------------------------------------------------------------------


Netlist sorting complete. Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.004 . Memory (MB): peak = 2240.680 ; gain = 0.000
Ending Physical Synthesis Task | Checksum: 2017f0d36

Time (s): cpu = 00:00:01 ; elapsed = 00:00:01 . Memory (MB): peak = 2240.680 ; gain = 0.000
INFO: [Common 17-83] Releasing license: Implementation
150 Infos, 10 Warnings, 5 Critical Warnings and 0 Errors encountered.
phys_opt_design completed successfully
INFO: [Timing 38-480] Writing timing data to binary archive.
Write ShapeDB Complete: Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.019 . Memory (MB): peak = 2240.680 ; gain = 0.000
Wrote PlaceDB: Time (s): cpu = 00:00:01 ; elapsed = 00:00:00.595 . Memory (MB): peak = 2240.680 ; gain = 0.000
Wrote PulsedLatchDB: Time (s): cpu = 00:00:00 ; elapsed = 00:00:00 . Memory (MB): peak = 2240.680 ; gain = 0.000
Writing XDEF routing.
Writing XDEF routing logical nets.
Writing XDEF routing special nets.
Wrote RouteStorage: Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.019 . Memory (MB): peak = 2240.680 ; gain = 0.000
Wrote Netlist Cache: Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.011 . Memory (MB): peak = 2240.680 ; gain = 0.000
Wrote Device Cache: Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.006 . Memory (MB): peak = 2240.680 ; gain = 0.000
Write Physdb Complete: Time (s): cpu = 00:00:01 ; elapsed = 00:00:00.637 . Memory (MB): peak = 2240.680 ; gain = 0.000
INFO: [Common 17-1381] The checkpoint 'G:/FPGA/********/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.runs/impl_1/pcileech_100t484_x1_top_physopt.dcp' has been generated.
Command: route_design
Attempting to get a license for feature 'Implementation' and/or device 'xc7a100t'
INFO: [Common 17-349] Got license for feature 'Implementation' and/or device 'xc7a100t'
INFO: [DRC 23-27] Running DRC with 2 threads
INFO: [Vivado_Tcl 4-198] DRC finished with 0 Errors
INFO: [Vivado_Tcl 4-199] Please refer to the DRC report (report_drc) for more information.


Starting Routing Task
INFO: [Route 35-254] Multithreading enabled for route_design using a maximum of 2 CPUs

Phase 1 Build RT Design
Checksum: PlaceDB: bfe44b6 ConstDB: 0 ShapeSum: c75b19b3 RouteDB: b1d25f96
Post Restoration Checksum: NetGraph: f60e5c2d | NumContArr: 1674d46f | Constraints: c2a8fa9d | Timing: c2a8fa9d
Phase 1 Build RT Design | Checksum: 291d525d6

Time (s): cpu = 00:00:17 ; elapsed = 00:00:20 . Memory (MB): peak = 2339.848 ; gain = 99.168

Phase 2 Router Initialization

Phase 2.1 Fix Topology Constraints
Phase 2.1 Fix Topology Constraints | Checksum: 291d525d6

Time (s): cpu = 00:00:18 ; elapsed = 00:00:20 . Memory (MB): peak = 2339.965 ; gain = 99.285

Phase 2.2 Pre Route Cleanup
Phase 2.2 Pre Route Cleanup | Checksum: 291d525d6

Time (s): cpu = 00:00:18 ; elapsed = 00:00:20 . Memory (MB): peak = 2339.965 ; gain = 99.285
 Number of Nodes with overlaps = 0

Phase 2.3 Update Timing
Phase 2.3 Update Timing | Checksum: 28bc7ef8f

Time (s): cpu = 00:00:19 ; elapsed = 00:00:22 . Memory (MB): peak = 2392.289 ; gain = 151.609
INFO: [Route 35-416] Intermediate Timing Summary | WNS=-0.388 | TNS=-8.393 | WHS=-0.712 | THS=-653.435|


Phase 2.4 Update Timing for Bus Skew

Phase 2.4.1 Update Timing
Phase 2.4.1 Update Timing | Checksum: 203258cfc

Time (s): cpu = 00:00:20 ; elapsed = 00:00:24 . Memory (MB): peak = 2392.289 ; gain = 151.609
INFO: [Route 35-416] Intermediate Timing Summary | WNS=-0.388 | TNS=-9.397 | WHS=-0.648 | THS=-38.187|

Phase 2.4 Update Timing for Bus Skew | Checksum: 1d7b1fe3b

Time (s): cpu = 00:00:20 ; elapsed = 00:00:24 . Memory (MB): peak = 2396.215 ; gain = 155.535

Router Utilization Summary
  Global Vertical Routing Utilization    = 0.********* %
  Global Horizontal Routing Utilization  = 0.********* %
  Routable Net Status*
  *Does not include unroutable nets such as driverless and loadless.
  Run report_route_status for detailed report.
  Number of Failed Nets               = 11047
    (Failed Nets is the sum of unrouted and partially routed nets)
  Number of Unrouted Nets             = 11047
  Number of Partially Routed Nets     = 0
  Number of Node Overlaps             = 0

Phase 2 Router Initialization | Checksum: 1bf9077bc

Time (s): cpu = 00:00:20 ; elapsed = 00:00:24 . Memory (MB): peak = 2401.570 ; gain = 160.891

Phase 3 Global Routing
Phase 3 Global Routing | Checksum: 1bf9077bc

Time (s): cpu = 00:00:20 ; elapsed = 00:00:24 . Memory (MB): peak = 2401.570 ; gain = 160.891

Phase 4 Initial Routing

Phase 4.1 Initial Net Routing Pass
Phase 4.1 Initial Net Routing Pass | Checksum: 24573fe72

Time (s): cpu = 00:00:27 ; elapsed = 00:00:28 . Memory (MB): peak = 2401.617 ; gain = 160.938
Phase 4 Initial Routing | Checksum: 24573fe72

Time (s): cpu = 00:00:27 ; elapsed = 00:00:28 . Memory (MB): peak = 2401.617 ; gain = 160.938

Phase 5 Rip-up And Reroute

Phase 5.1 Global Iteration 0
 Number of Nodes with overlaps = 933
 Number of Nodes with overlaps = 33
 Number of Nodes with overlaps = 9
 Number of Nodes with overlaps = 3
 Number of Nodes with overlaps = 1
 Number of Nodes with overlaps = 0
INFO: [Route 35-416] Intermediate Timing Summary | WNS=-0.452 | TNS=-10.611| WHS=N/A    | THS=N/A    |

Phase 5.1 Global Iteration 0 | Checksum: 2769d7386

Time (s): cpu = 00:00:31 ; elapsed = 00:00:34 . Memory (MB): peak = 2444.684 ; gain = 204.004

Phase 5.2 Global Iteration 1
 Number of Nodes with overlaps = 5
 Number of Nodes with overlaps = 4
 Number of Nodes with overlaps = 1
 Number of Nodes with overlaps = 0
INFO: [Route 35-416] Intermediate Timing Summary | WNS=-0.439 | TNS=-10.281| WHS=N/A    | THS=N/A    |

Phase 5.2 Global Iteration 1 | Checksum: 2ef887262

Time (s): cpu = 00:00:32 ; elapsed = 00:00:35 . Memory (MB): peak = 2444.695 ; gain = 204.016

Phase 5.3 Global Iteration 2
 Number of Nodes with overlaps = 2
 Number of Nodes with overlaps = 1
 Number of Nodes with overlaps = 0
INFO: [Route 35-416] Intermediate Timing Summary | WNS=-0.442 | TNS=-10.324| WHS=N/A    | THS=N/A    |

Phase 5.3 Global Iteration 2 | Checksum: 1cb7dd14a

Time (s): cpu = 00:00:32 ; elapsed = 00:00:35 . Memory (MB): peak = 2444.695 ; gain = 204.016
Phase 5 Rip-up And Reroute | Checksum: 1cb7dd14a

Time (s): cpu = 00:00:32 ; elapsed = 00:00:35 . Memory (MB): peak = 2444.695 ; gain = 204.016

Phase 6 Delay and Skew Optimization

Phase 6.1 Delay CleanUp

Phase 6.1.1 Update Timing
Phase 6.1.1 Update Timing | Checksum: 1b72d1e8e

Time (s): cpu = 00:00:32 ; elapsed = 00:00:35 . Memory (MB): peak = 2444.695 ; gain = 204.016
INFO: [Route 35-416] Intermediate Timing Summary | WNS=-0.439 | TNS=-10.281| WHS=N/A    | THS=N/A    |

Phase 6.1 Delay CleanUp | Checksum: 1ba118d56

Time (s): cpu = 00:00:32 ; elapsed = 00:00:35 . Memory (MB): peak = 2444.695 ; gain = 204.016

Phase 6.2 Clock Skew Optimization
Phase 6.2 Clock Skew Optimization | Checksum: 1ba118d56

Time (s): cpu = 00:00:32 ; elapsed = 00:00:35 . Memory (MB): peak = 2444.695 ; gain = 204.016
Phase 6 Delay and Skew Optimization | Checksum: 1ba118d56

Time (s): cpu = 00:00:32 ; elapsed = 00:00:35 . Memory (MB): peak = 2444.695 ; gain = 204.016

Phase 7 Post Hold Fix

Phase 7.1 Hold Fix Iter
INFO: [Route 35-416] Intermediate Timing Summary | WNS=-0.439 | TNS=-10.281| WHS=0.029  | THS=0.000  |

Phase 7.1 Hold Fix Iter | Checksum: 14425b188

Time (s): cpu = 00:00:32 ; elapsed = 00:00:36 . Memory (MB): peak = 2444.695 ; gain = 204.016
Phase 7 Post Hold Fix | Checksum: 14425b188

Time (s): cpu = 00:00:32 ; elapsed = 00:00:36 . Memory (MB): peak = 2444.695 ; gain = 204.016

Phase 8 Route finalize

Router Utilization Summary
  Global Vertical Routing Utilization    = 2.3987 %
  Global Horizontal Routing Utilization  = 3.07026 %
  Routable Net Status*
  *Does not include unroutable nets such as driverless and loadless.
  Run report_route_status for detailed report.
  Number of Failed Nets               = 0
    (Failed Nets is the sum of unrouted and partially routed nets)
  Number of Unrouted Nets             = 0
  Number of Partially Routed Nets     = 0
  Number of Node Overlaps             = 0


--GLOBAL Congestion:
Utilization threshold used for congestion level computation: 0.85
Congestion Report
North Dir 1x1 Area, Max Cong = 69.3694%, No Congested Regions.
South Dir 1x1 Area, Max Cong = 58.5586%, No Congested Regions.
East Dir 1x1 Area, Max Cong = 75%, No Congested Regions.
West Dir 1x1 Area, Max Cong = 58.8235%, No Congested Regions.

------------------------------
Reporting congestion hotspots
------------------------------
Direction: North
----------------
Congested clusters found at Level 0
Effective congestion level: 0 Aspect Ratio: 1 Sparse Ratio: 0
Direction: South
----------------
Congested clusters found at Level 0
Effective congestion level: 0 Aspect Ratio: 1 Sparse Ratio: 0
Direction: East
----------------
Congested clusters found at Level 0
Effective congestion level: 0 Aspect Ratio: 1 Sparse Ratio: 0
Direction: West
----------------
Congested clusters found at Level 0
Effective congestion level: 0 Aspect Ratio: 1 Sparse Ratio: 0

Phase 8 Route finalize | Checksum: 14425b188

Time (s): cpu = 00:00:32 ; elapsed = 00:00:36 . Memory (MB): peak = 2444.695 ; gain = 204.016

Phase 9 Verifying routed nets

 Verification completed successfully
Phase 9 Verifying routed nets | Checksum: 14425b188

Time (s): cpu = 00:00:32 ; elapsed = 00:00:36 . Memory (MB): peak = 2444.695 ; gain = 204.016

Phase 10 Depositing Routes
INFO: [Route 35-467] Router swapped GT pin i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/gt_top_i/pipe_wrapper_i/pipe_lane[0].pipe_quad.gt_common_enabled.gt_common_int.gt_common_i/qpll_wrapper_i/gtp_common.gtpe2_common_i/GTREFCLK0 to physical pin GTPE2_COMMON_X0Y1/GTREFCLK1
Phase 10 Depositing Routes | Checksum: 166b81468

Time (s): cpu = 00:00:32 ; elapsed = 00:00:36 . Memory (MB): peak = 2444.695 ; gain = 204.016

Phase 11 Post Process Routing
Phase 11 Post Process Routing | Checksum: 166b81468

Time (s): cpu = 00:00:32 ; elapsed = 00:00:36 . Memory (MB): peak = 2444.695 ; gain = 204.016

Phase 12 Post Router Timing
INFO: [Route 35-57] Estimated Timing Summary | WNS=-0.439 | TNS=-10.281| WHS=0.029  | THS=0.000  |

WARNING: [Route 35-328] Router estimated timing not met.
Resolution: For a complete and accurate timing signoff, report_timing_summary must be run after route_design. Alternatively, route_design can be run with the -timing_summary option to enable a complete timing signoff at the end of route_design.
Phase 12 Post Router Timing | Checksum: 166b81468

Time (s): cpu = 00:00:32 ; elapsed = 00:00:36 . Memory (MB): peak = 2444.695 ; gain = 204.016
Total Elapsed time in route_design: 36.493 secs

Phase 13 Post-Route Event Processing
Phase 13 Post-Route Event Processing | Checksum: 2225e7855

Time (s): cpu = 00:00:32 ; elapsed = 00:00:37 . Memory (MB): peak = 2444.695 ; gain = 204.016
INFO: [Route 35-16] Router Completed Successfully
Ending Routing Task | Checksum: 2225e7855

Time (s): cpu = 00:00:32 ; elapsed = 00:00:37 . Memory (MB): peak = 2444.695 ; gain = 204.016

Routing Is Done.
INFO: [Common 17-83] Releasing license: Implementation
168 Infos, 11 Warnings, 5 Critical Warnings and 0 Errors encountered.
route_design completed successfully
route_design: Time (s): cpu = 00:00:33 ; elapsed = 00:00:37 . Memory (MB): peak = 2444.695 ; gain = 204.016
INFO: [Vivado 12-24828] Executing command : report_drc -file pcileech_100t484_x1_top_drc_routed.rpt -pb pcileech_100t484_x1_top_drc_routed.pb -rpx pcileech_100t484_x1_top_drc_routed.rpx
Command: report_drc -file pcileech_100t484_x1_top_drc_routed.rpt -pb pcileech_100t484_x1_top_drc_routed.pb -rpx pcileech_100t484_x1_top_drc_routed.rpx
INFO: [IP_Flow 19-1839] IP Catalog is up to date.
INFO: [DRC 23-27] Running DRC with 2 threads
INFO: [Vivado_Tcl 2-168] The results of DRC are in file G:/FPGA/********/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.runs/impl_1/pcileech_100t484_x1_top_drc_routed.rpt.
report_drc completed successfully
INFO: [Vivado 12-24828] Executing command : report_methodology -file pcileech_100t484_x1_top_methodology_drc_routed.rpt -pb pcileech_100t484_x1_top_methodology_drc_routed.pb -rpx pcileech_100t484_x1_top_methodology_drc_routed.rpx
Command: report_methodology -file pcileech_100t484_x1_top_methodology_drc_routed.rpt -pb pcileech_100t484_x1_top_methodology_drc_routed.pb -rpx pcileech_100t484_x1_top_methodology_drc_routed.rpx
INFO: [Timing 38-35] Done setting XDC timing constraints.
INFO: [DRC 23-133] Running Methodology with 2 threads
INFO: [Vivado_Tcl 2-1520] The results of Report Methodology are in file G:/FPGA/********/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.runs/impl_1/pcileech_100t484_x1_top_methodology_drc_routed.rpt.
report_methodology completed successfully
INFO: [Vivado 12-24828] Executing command : report_timing_summary -max_paths 10 -report_unconstrained -file pcileech_100t484_x1_top_timing_summary_routed.rpt -pb pcileech_100t484_x1_top_timing_summary_routed.pb -rpx pcileech_100t484_x1_top_timing_summary_routed.rpx -warn_on_violation 
INFO: [Timing 38-35] Done setting XDC timing constraints.
INFO: [Timing 38-91] UpdateTimingParams: Speed grade: -2, Delay Type: min_max.
INFO: [Timing 38-191] Multithreading enabled for timing update using a maximum of 2 CPUs
CRITICAL WARNING: [Timing 38-282] The design failed to meet the timing requirements. Please see the timing summary report for details on the timing violations.
WARNING: [Timing 38-436] There are set_bus_skew constraint(s) in this design. Please run report_bus_skew to ensure that bus skew requirements are met.
INFO: [Vivado 12-24838] Running report commands "report_incremental_reuse, report_route_status" in parallel.
Running report generation with 2 threads.
INFO: [Vivado 12-24828] Executing command : report_incremental_reuse -file pcileech_100t484_x1_top_incremental_reuse_routed.rpt
INFO: [Vivado_Tcl 4-1062] Incremental flow is disabled. No incremental reuse Info to report.
INFO: [Vivado 12-24828] Executing command : report_route_status -file pcileech_100t484_x1_top_route_status.rpt -pb pcileech_100t484_x1_top_route_status.pb
INFO: [Vivado 12-24828] Executing command : report_power -file pcileech_100t484_x1_top_power_routed.rpt -pb pcileech_100t484_x1_top_power_summary_routed.pb -rpx pcileech_100t484_x1_top_power_routed.rpx
Command: report_power -file pcileech_100t484_x1_top_power_routed.rpt -pb pcileech_100t484_x1_top_power_summary_routed.pb -rpx pcileech_100t484_x1_top_power_routed.rpx
INFO: [Power 33-23] Power model is not available for i_STARTUPE2
Running Vector-less Activity Propagation...

Finished Running Vector-less Activity Propagation
WARNING: [Power 33-332] Found switching activity that implies high-fanout reset nets being asserted for excessive periods of time which may result in inaccurate power analysis.
Resolution: To review and fix problems, please run Power Constraints Advisor in the GUI from Tools > Power Constraints Advisor or run report_power with the -advisory option to generate a text report.
186 Infos, 13 Warnings, 6 Critical Warnings and 0 Errors encountered.
report_power completed successfully
INFO: [Vivado 12-24828] Executing command : report_clock_utilization -file pcileech_100t484_x1_top_clock_utilization_routed.rpt
INFO: [Vivado 12-24828] Executing command : report_bus_skew -warn_on_violation -file pcileech_100t484_x1_top_bus_skew_routed.rpt -pb pcileech_100t484_x1_top_bus_skew_routed.pb -rpx pcileech_100t484_x1_top_bus_skew_routed.rpx
INFO: [Timing 38-91] UpdateTimingParams: Speed grade: -2, Delay Type: min_max.
INFO: [Timing 38-191] Multithreading enabled for timing update using a maximum of 2 CPUs
generate_parallel_reports: Time (s): cpu = 00:00:12 ; elapsed = 00:00:11 . Memory (MB): peak = 2477.375 ; gain = 32.680
INFO: [Timing 38-480] Writing timing data to binary archive.
Write ShapeDB Complete: Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.017 . Memory (MB): peak = 2502.742 ; gain = 8.945
Wrote PlaceDB: Time (s): cpu = 00:00:01 ; elapsed = 00:00:00.590 . Memory (MB): peak = 2513.758 ; gain = 19.961
Wrote PulsedLatchDB: Time (s): cpu = 00:00:00 ; elapsed = 00:00:00 . Memory (MB): peak = 2513.758 ; gain = 0.000
Writing XDEF routing.
Writing XDEF routing logical nets.
Writing XDEF routing special nets.
Wrote RouteStorage: Time (s): cpu = 00:00:01 ; elapsed = 00:00:00.081 . Memory (MB): peak = 2513.758 ; gain = 0.000
Wrote Netlist Cache: Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.010 . Memory (MB): peak = 2513.758 ; gain = 0.000
Wrote Device Cache: Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.004 . Memory (MB): peak = 2513.758 ; gain = 0.000
Write Physdb Complete: Time (s): cpu = 00:00:02 ; elapsed = 00:00:00.692 . Memory (MB): peak = 2513.758 ; gain = 19.961
INFO: [Common 17-1381] The checkpoint 'G:/FPGA/********/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.runs/impl_1/pcileech_100t484_x1_top_routed.dcp' has been generated.
Command: write_bitstream -force pcileech_100t484_x1_top.bit -bin_file
Attempting to get a license for feature 'Implementation' and/or device 'xc7a100t'
INFO: [Common 17-349] Got license for feature 'Implementation' and/or device 'xc7a100t'
Running DRC as a precondition to command write_bitstream
INFO: [IP_Flow 19-1839] IP Catalog is up to date.
INFO: [DRC 23-27] Running DRC with 2 threads
INFO: [Vivado 12-3199] DRC finished with 0 Errors
INFO: [Vivado 12-3200] Please refer to the DRC report (report_drc) for more information.
INFO: [Designutils 20-2272] Running write_bitstream with 2 threads.
Loading data files...
Loading site data...
Loading route data...
Processing options...
WARNING: [Designutils 12-1515] STARTUP component has an active signal connected to CLK pin. Setting StartupClk to UserClk.
Creating bitmap...
Creating bitstream...
Bitstream compression saved 18562368 bits.
Writing bitstream ./pcileech_100t484_x1_top.bit...
Writing bitstream ./pcileech_100t484_x1_top.bin...
INFO: [Vivado 12-1842] Bitgen Completed Successfully.
INFO: [Common 17-83] Releasing license: Implementation
200 Infos, 14 Warnings, 6 Critical Warnings and 0 Errors encountered.
write_bitstream completed successfully
write_bitstream: Time (s): cpu = 00:00:12 ; elapsed = 00:00:11 . Memory (MB): peak = 3025.566 ; gain = 511.809
INFO: [Common 17-206] Exiting Vivado at Wed Jun 18 17:38:41 2025...
