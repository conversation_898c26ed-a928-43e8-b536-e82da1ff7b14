#-----------------------------------------------------------
# Vivado v2024.2.2 (64-bit)
# SW Build 6060944 on Thu Mar 06 19:10:01 MST 2025
# IP Build 6050500 on Thu Mar  6 23:33:39 MST 2025
# SharedData Build 6060542 on Thu Mar 06 10:31:07 MST 2025
# Start of session at: Wed Jun 18 17:34:48 2025
# Process ID         : 97076
# Current directory  : G:/FPGA/20250327/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.runs/synth_1
# Command line       : vivado.exe -log pcileech_100t484_x1_top.vds -product Vivado -mode batch -messageDb vivado.pb -notrace -source pcileech_100t484_x1_top.tcl
# Log file           : G:/FPGA/20250327/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.runs/synth_1/pcileech_100t484_x1_top.vds
# Journal file       : G:/FPGA/20250327/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.runs/synth_1\vivado.jou
# Running On         : DESKTOP-7T6N58Q
# Platform           : Windows Server 2016 or Windows 10
# Operating System   : 22631
# Processor Detail   : 13th Gen Intel(R) Core(TM) i7-13700F
# CPU Frequency      : 2112 MHz
# CPU Physical cores : 16
# CPU Logical cores  : 24
# Host memory        : 102877 MB
# Swap memory        : 10314 MB
# Total Virtual      : 113192 MB
# Available Virtual  : 33782 MB
#-----------------------------------------------------------
source pcileech_100t484_x1_top.tcl -notrace
create_project: Time (s): cpu = 00:00:04 ; elapsed = 00:00:05 . Memory (MB): peak = 623.660 ; gain = 195.785
WARNING: [filemgmt 56-12] File 'g:/FPGA/20250327/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.srcs/sources_1/ip/bram_bar_zero4k/pcileech_bar_zero4k.coe' cannot be added to the project because it already exists in the project, skipping this file
WARNING: [filemgmt 56-12] File 'g:/FPGA/20250327/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.srcs/sources_1/ip/drom_pcie_cfgspace_writemask/pcileech_cfgspace_writemask.coe' cannot be added to the project because it already exists in the project, skipping this file
Command: synth_design -top pcileech_100t484_x1_top -part xc7a100tfgg484-2
Starting synth_design
Attempting to get a license for feature 'Synthesis' and/or device 'xc7a100t'
INFO: [Common 17-349] Got license for feature 'Synthesis' and/or device 'xc7a100t'
INFO: [Device 21-403] Loading part xc7a100tfgg484-2
INFO: [Synth 8-7079] Multithreading enabled for synth_design using a maximum of 2 processes.
INFO: [Synth 8-7078] Launching helper process for spawning children vivado processes
INFO: [Synth 8-7075] Helper process launched with PID 82092
---------------------------------------------------------------------------------
Starting RTL Elaboration : Time (s): cpu = 00:00:03 ; elapsed = 00:00:04 . Memory (MB): peak = 1324.000 ; gain = 469.523
---------------------------------------------------------------------------------
INFO: [Synth 8-6157] synthesizing module 'pcileech_100t484_x1_top' [G:/FPGA/20250327/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.srcs/sources_1/imports/src/pcileech_100t484_x1_top.sv:13]
INFO: [Synth 8-6157] synthesizing module 'IfComToFifo' [G:/FPGA/20250327/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.srcs/sources_1/imports/src/pcileech_header.svh:19]
INFO: [Synth 8-6155] done synthesizing module 'IfComToFifo' (0#1) [G:/FPGA/20250327/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.srcs/sources_1/imports/src/pcileech_header.svh:19]
INFO: [Synth 8-6157] synthesizing module 'IfPCIeFifoCfg' [G:/FPGA/20250327/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.srcs/sources_1/imports/src/pcileech_header.svh:199]
INFO: [Synth 8-6155] done synthesizing module 'IfPCIeFifoCfg' (0#1) [G:/FPGA/20250327/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.srcs/sources_1/imports/src/pcileech_header.svh:199]
INFO: [Synth 8-6157] synthesizing module 'IfPCIeFifoTlp' [G:/FPGA/20250327/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.srcs/sources_1/imports/src/pcileech_header.svh:220]
INFO: [Synth 8-6155] done synthesizing module 'IfPCIeFifoTlp' (0#1) [G:/FPGA/20250327/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.srcs/sources_1/imports/src/pcileech_header.svh:220]
INFO: [Synth 8-6157] synthesizing module 'IfPCIeFifoCore' [G:/FPGA/20250327/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.srcs/sources_1/imports/src/pcileech_header.svh:244]
INFO: [Synth 8-6155] done synthesizing module 'IfPCIeFifoCore' (0#1) [G:/FPGA/20250327/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.srcs/sources_1/imports/src/pcileech_header.svh:244]
INFO: [Synth 8-6157] synthesizing module 'IfShadow2Fifo' [G:/FPGA/20250327/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.srcs/sources_1/imports/src/pcileech_header.svh:267]
INFO: [Synth 8-6155] done synthesizing module 'IfShadow2Fifo' (0#1) [G:/FPGA/20250327/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.srcs/sources_1/imports/src/pcileech_header.svh:267]
INFO: [Synth 8-6157] synthesizing module 'OBUF' [D:/xilinx/Vivado/2024.2/scripts/rt/data/unisim_comp.v:99428]
INFO: [Synth 8-6155] done synthesizing module 'OBUF' (0#1) [D:/xilinx/Vivado/2024.2/scripts/rt/data/unisim_comp.v:99428]
INFO: [Synth 8-6157] synthesizing module 'pcileech_com' [G:/FPGA/20250327/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.srcs/sources_1/imports/src/pcileech_com.sv:15]
INFO: [Synth 8-6157] synthesizing module 'fifo_64_64_clk2_comrx' [G:/FPGA/20250327/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.runs/synth_1/.Xil/Vivado-97076-DESKTOP-7T6N58Q/realtime/fifo_64_64_clk2_comrx_stub.v:6]
INFO: [Synth 8-6155] done synthesizing module 'fifo_64_64_clk2_comrx' (0#1) [G:/FPGA/20250327/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.runs/synth_1/.Xil/Vivado-97076-DESKTOP-7T6N58Q/realtime/fifo_64_64_clk2_comrx_stub.v:6]
INFO: [Synth 8-6157] synthesizing module 'fifo_32_32_clk1_comtx' [G:/FPGA/20250327/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.runs/synth_1/.Xil/Vivado-97076-DESKTOP-7T6N58Q/realtime/fifo_32_32_clk1_comtx_stub.v:6]
INFO: [Synth 8-6155] done synthesizing module 'fifo_32_32_clk1_comtx' (0#1) [G:/FPGA/20250327/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.runs/synth_1/.Xil/Vivado-97076-DESKTOP-7T6N58Q/realtime/fifo_32_32_clk1_comtx_stub.v:6]
INFO: [Synth 8-6157] synthesizing module 'fifo_256_32_clk2_comtx' [G:/FPGA/20250327/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.runs/synth_1/.Xil/Vivado-97076-DESKTOP-7T6N58Q/realtime/fifo_256_32_clk2_comtx_stub.v:6]
INFO: [Synth 8-6155] done synthesizing module 'fifo_256_32_clk2_comtx' (0#1) [G:/FPGA/20250327/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.runs/synth_1/.Xil/Vivado-97076-DESKTOP-7T6N58Q/realtime/fifo_256_32_clk2_comtx_stub.v:6]
INFO: [Synth 8-6157] synthesizing module 'pcileech_ft601_optimized' [G:/FPGA/20250327/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.srcs/sources_1/imports/src/pcileech_ft601_optimized.sv:16]
INFO: [Synth 8-155] case statement is not full and has no default [G:/FPGA/20250327/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.srcs/sources_1/imports/src/pcileech_ft601_optimized.sv:127]
INFO: [Synth 8-6155] done synthesizing module 'pcileech_ft601_optimized' (0#1) [G:/FPGA/20250327/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.srcs/sources_1/imports/src/pcileech_ft601_optimized.sv:16]
INFO: [Synth 8-6155] done synthesizing module 'pcileech_com' (0#1) [G:/FPGA/20250327/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.srcs/sources_1/imports/src/pcileech_com.sv:15]
INFO: [Synth 8-6157] synthesizing module 'pcileech_fifo' [G:/FPGA/20250327/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.srcs/sources_1/imports/src/pcileech_fifo.sv:16]
	Parameter PARAM_DEVICE_ID bound to: 9 - type: integer 
	Parameter PARAM_VERSION_NUMBER_MAJOR bound to: 4 - type: integer 
	Parameter PARAM_VERSION_NUMBER_MINOR bound to: 14 - type: integer 
	Parameter PARAM_CUSTOM_VALUE bound to: -1 - type: integer 
INFO: [Synth 8-6157] synthesizing module 'fifo_34_34' [G:/FPGA/20250327/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.runs/synth_1/.Xil/Vivado-97076-DESKTOP-7T6N58Q/realtime/fifo_34_34_stub.v:6]
INFO: [Synth 8-6155] done synthesizing module 'fifo_34_34' (0#1) [G:/FPGA/20250327/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.runs/synth_1/.Xil/Vivado-97076-DESKTOP-7T6N58Q/realtime/fifo_34_34_stub.v:6]
INFO: [Synth 8-6157] synthesizing module 'pcileech_mux' [G:/FPGA/20250327/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.srcs/sources_1/imports/src/pcileech_mux.sv:14]
INFO: [Synth 8-6155] done synthesizing module 'pcileech_mux' (0#1) [G:/FPGA/20250327/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.srcs/sources_1/imports/src/pcileech_mux.sv:14]
INFO: [Synth 8-6157] synthesizing module 'fifo_64_64_clk1_fifocmd' [G:/FPGA/20250327/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.runs/synth_1/.Xil/Vivado-97076-DESKTOP-7T6N58Q/realtime/fifo_64_64_clk1_fifocmd_stub.v:6]
INFO: [Synth 8-6155] done synthesizing module 'fifo_64_64_clk1_fifocmd' (0#1) [G:/FPGA/20250327/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.runs/synth_1/.Xil/Vivado-97076-DESKTOP-7T6N58Q/realtime/fifo_64_64_clk1_fifocmd_stub.v:6]
WARNING: [Synth 8-4446] all outputs are unconnected for this instance and logic may be removed [G:/FPGA/20250327/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.srcs/sources_1/imports/src/pcileech_fifo.sv:441]
INFO: [Synth 8-6157] synthesizing module 'STARTUPE2' [D:/xilinx/Vivado/2024.2/scripts/rt/data/unisim_comp.v:149215]
	Parameter PROG_USR bound to: FALSE - type: string 
	Parameter SIM_CCLK_FREQ bound to: 0.000000 - type: double 
INFO: [Synth 8-6155] done synthesizing module 'STARTUPE2' (0#1) [D:/xilinx/Vivado/2024.2/scripts/rt/data/unisim_comp.v:149215]
INFO: [Synth 8-6155] done synthesizing module 'pcileech_fifo' (0#1) [G:/FPGA/20250327/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.srcs/sources_1/imports/src/pcileech_fifo.sv:16]
INFO: [Synth 8-6157] synthesizing module 'pcileech_pcie_a7' [G:/FPGA/20250327/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.srcs/sources_1/imports/src/pcileech_pcie_a7.sv:13]
INFO: [Synth 8-6157] synthesizing module 'IfPCIeSignals' [G:/FPGA/20250327/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.srcs/sources_1/imports/src/pcileech_header.svh:40]
INFO: [Synth 8-6155] done synthesizing module 'IfPCIeSignals' (0#1) [G:/FPGA/20250327/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.srcs/sources_1/imports/src/pcileech_header.svh:40]
INFO: [Synth 8-6157] synthesizing module 'IfPCIeTlpRxTx' [G:/FPGA/20250327/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.srcs/sources_1/imports/src/pcileech_header.svh:300]
INFO: [Synth 8-6155] done synthesizing module 'IfPCIeTlpRxTx' (0#1) [G:/FPGA/20250327/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.srcs/sources_1/imports/src/pcileech_header.svh:300]
INFO: [Synth 8-6157] synthesizing module 'IfPCIeTlpRxTx' [G:/FPGA/20250327/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.srcs/sources_1/imports/src/pcileech_header.svh:300]
INFO: [Synth 8-6155] done synthesizing module 'IfPCIeTlpRxTx' (0#1) [G:/FPGA/20250327/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.srcs/sources_1/imports/src/pcileech_header.svh:300]
INFO: [Synth 8-6157] synthesizing module 'IfAXIS128' [G:/FPGA/20250327/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.srcs/sources_1/imports/src/pcileech_header.svh:165]
INFO: [Synth 8-6155] done synthesizing module 'IfAXIS128' (0#1) [G:/FPGA/20250327/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.srcs/sources_1/imports/src/pcileech_header.svh:165]
INFO: [Synth 8-6157] synthesizing module 'IfAXIS128' [G:/FPGA/20250327/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.srcs/sources_1/imports/src/pcileech_header.svh:165]
INFO: [Synth 8-6155] done synthesizing module 'IfAXIS128' (0#1) [G:/FPGA/20250327/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.srcs/sources_1/imports/src/pcileech_header.svh:165]
INFO: [Synth 8-6157] synthesizing module 'IfAXIS128' [G:/FPGA/20250327/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.srcs/sources_1/imports/src/pcileech_header.svh:165]
INFO: [Synth 8-6155] done synthesizing module 'IfAXIS128' (0#1) [G:/FPGA/20250327/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.srcs/sources_1/imports/src/pcileech_header.svh:165]
INFO: [Synth 8-6157] synthesizing module 'IBUFDS_GTE2' [D:/xilinx/Vivado/2024.2/scripts/rt/data/unisim_comp.v:75979]
INFO: [Synth 8-6155] done synthesizing module 'IBUFDS_GTE2' (0#1) [D:/xilinx/Vivado/2024.2/scripts/rt/data/unisim_comp.v:75979]
INFO: [Synth 8-6157] synthesizing module 'pcileech_pcie_cfg_a7' [G:/FPGA/20250327/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.srcs/sources_1/imports/src/pcileech_pcie_cfg_a7.sv:13]
INFO: [Synth 8-6157] synthesizing module 'fifo_64_64' [G:/FPGA/20250327/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.runs/synth_1/.Xil/Vivado-97076-DESKTOP-7T6N58Q/realtime/fifo_64_64_stub.v:6]
INFO: [Synth 8-6155] done synthesizing module 'fifo_64_64' (0#1) [G:/FPGA/20250327/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.runs/synth_1/.Xil/Vivado-97076-DESKTOP-7T6N58Q/realtime/fifo_64_64_stub.v:6]
INFO: [Synth 8-6157] synthesizing module 'fifo_32_32_clk2' [G:/FPGA/20250327/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.runs/synth_1/.Xil/Vivado-97076-DESKTOP-7T6N58Q/realtime/fifo_32_32_clk2_stub.v:6]
INFO: [Synth 8-6155] done synthesizing module 'fifo_32_32_clk2' (0#1) [G:/FPGA/20250327/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.runs/synth_1/.Xil/Vivado-97076-DESKTOP-7T6N58Q/realtime/fifo_32_32_clk2_stub.v:6]
INFO: [Synth 8-6155] done synthesizing module 'pcileech_pcie_cfg_a7' (0#1) [G:/FPGA/20250327/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.srcs/sources_1/imports/src/pcileech_pcie_cfg_a7.sv:13]
INFO: [Synth 8-6157] synthesizing module 'pcileech_tlps128_src64' [G:/FPGA/20250327/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.srcs/sources_1/imports/src/pcileech_pcie_a7.sv:303]
INFO: [Synth 8-6155] done synthesizing module 'pcileech_tlps128_src64' (0#1) [G:/FPGA/20250327/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.srcs/sources_1/imports/src/pcileech_pcie_a7.sv:303]
INFO: [Synth 8-6157] synthesizing module 'pcileech_pcie_tlp_a7' [G:/FPGA/20250327/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.srcs/sources_1/imports/src/pcileech_pcie_tlp_a7.sv:13]
INFO: [Synth 8-6157] synthesizing module 'IfAXIS128' [G:/FPGA/20250327/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.srcs/sources_1/imports/src/pcileech_header.svh:165]
INFO: [Synth 8-6155] done synthesizing module 'IfAXIS128' (0#1) [G:/FPGA/20250327/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.srcs/sources_1/imports/src/pcileech_header.svh:165]
INFO: [Synth 8-6157] synthesizing module 'IfAXIS128' [G:/FPGA/20250327/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.srcs/sources_1/imports/src/pcileech_header.svh:165]
INFO: [Synth 8-6155] done synthesizing module 'IfAXIS128' (0#1) [G:/FPGA/20250327/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.srcs/sources_1/imports/src/pcileech_header.svh:165]
INFO: [Synth 8-6157] synthesizing module 'IfAXIS128' [G:/FPGA/20250327/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.srcs/sources_1/imports/src/pcileech_header.svh:165]
INFO: [Synth 8-6155] done synthesizing module 'IfAXIS128' (0#1) [G:/FPGA/20250327/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.srcs/sources_1/imports/src/pcileech_header.svh:165]
INFO: [Synth 8-6157] synthesizing module 'IfAXIS128' [G:/FPGA/20250327/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.srcs/sources_1/imports/src/pcileech_header.svh:165]
INFO: [Synth 8-6155] done synthesizing module 'IfAXIS128' (0#1) [G:/FPGA/20250327/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.srcs/sources_1/imports/src/pcileech_header.svh:165]
INFO: [Synth 8-6157] synthesizing module 'pcileech_tlps128_bar_controller' [G:/FPGA/20250327/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.srcs/sources_1/imports/src/pcileech_tlps128_bar_controller.sv:42]
INFO: [Synth 8-6157] synthesizing module 'pcileech_tlps128_bar_rdengine' [G:/FPGA/20250327/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.srcs/sources_1/imports/src/pcileech_tlps128_bar_controller.sv:395]
INFO: [Synth 8-6157] synthesizing module 'fifo_74_74_clk1_bar_rd1' [G:/FPGA/20250327/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.runs/synth_1/.Xil/Vivado-97076-DESKTOP-7T6N58Q/realtime/fifo_74_74_clk1_bar_rd1_stub.v:6]
INFO: [Synth 8-6155] done synthesizing module 'fifo_74_74_clk1_bar_rd1' (0#1) [G:/FPGA/20250327/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.runs/synth_1/.Xil/Vivado-97076-DESKTOP-7T6N58Q/realtime/fifo_74_74_clk1_bar_rd1_stub.v:6]
INFO: [Synth 8-6157] synthesizing module 'fifo_134_134_clk1_bar_rdrsp' [G:/FPGA/20250327/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.runs/synth_1/.Xil/Vivado-97076-DESKTOP-7T6N58Q/realtime/fifo_134_134_clk1_bar_rdrsp_stub.v:6]
INFO: [Synth 8-6155] done synthesizing module 'fifo_134_134_clk1_bar_rdrsp' (0#1) [G:/FPGA/20250327/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.runs/synth_1/.Xil/Vivado-97076-DESKTOP-7T6N58Q/realtime/fifo_134_134_clk1_bar_rdrsp_stub.v:6]
INFO: [Synth 8-6155] done synthesizing module 'pcileech_tlps128_bar_rdengine' (0#1) [G:/FPGA/20250327/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.srcs/sources_1/imports/src/pcileech_tlps128_bar_controller.sv:395]
INFO: [Synth 8-6157] synthesizing module 'pcileech_tlps128_bar_wrengine' [G:/FPGA/20250327/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.srcs/sources_1/imports/src/pcileech_tlps128_bar_controller.sv:255]
INFO: [Synth 8-6157] synthesizing module 'fifo_141_141_clk1_bar_wr' [G:/FPGA/20250327/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.runs/synth_1/.Xil/Vivado-97076-DESKTOP-7T6N58Q/realtime/fifo_141_141_clk1_bar_wr_stub.v:6]
INFO: [Synth 8-6155] done synthesizing module 'fifo_141_141_clk1_bar_wr' (0#1) [G:/FPGA/20250327/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.runs/synth_1/.Xil/Vivado-97076-DESKTOP-7T6N58Q/realtime/fifo_141_141_clk1_bar_wr_stub.v:6]
INFO: [Synth 8-155] case statement is not full and has no default [G:/FPGA/20250327/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.srcs/sources_1/imports/src/pcileech_tlps128_bar_controller.sv:325]
INFO: [Synth 8-6155] done synthesizing module 'pcileech_tlps128_bar_wrengine' (0#1) [G:/FPGA/20250327/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.srcs/sources_1/imports/src/pcileech_tlps128_bar_controller.sv:255]
INFO: [Synth 8-6157] synthesizing module 'pcileech_bar_impl_zerowrite4k' [G:/FPGA/20250327/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.srcs/sources_1/imports/src/pcileech_tlps128_bar_controller.sv:749]
INFO: [Synth 8-6157] synthesizing module 'bram_bar_zero4k' [G:/FPGA/20250327/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.runs/synth_1/.Xil/Vivado-97076-DESKTOP-7T6N58Q/realtime/bram_bar_zero4k_stub.v:6]
INFO: [Synth 8-6155] done synthesizing module 'bram_bar_zero4k' (0#1) [G:/FPGA/20250327/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.runs/synth_1/.Xil/Vivado-97076-DESKTOP-7T6N58Q/realtime/bram_bar_zero4k_stub.v:6]
INFO: [Synth 8-6155] done synthesizing module 'pcileech_bar_impl_zerowrite4k' (0#1) [G:/FPGA/20250327/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.srcs/sources_1/imports/src/pcileech_tlps128_bar_controller.sv:749]
INFO: [Synth 8-6157] synthesizing module 'pcileech_bar_impl_loopaddr' [G:/FPGA/20250327/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.srcs/sources_1/imports/src/pcileech_tlps128_bar_controller.sv:710]
INFO: [Synth 8-6155] done synthesizing module 'pcileech_bar_impl_loopaddr' (0#1) [G:/FPGA/20250327/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.srcs/sources_1/imports/src/pcileech_tlps128_bar_controller.sv:710]
INFO: [Synth 8-6157] synthesizing module 'pcileech_bar_impl_none' [G:/FPGA/20250327/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.srcs/sources_1/imports/src/pcileech_tlps128_bar_controller.sv:678]
INFO: [Synth 8-6155] done synthesizing module 'pcileech_bar_impl_none' (0#1) [G:/FPGA/20250327/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.srcs/sources_1/imports/src/pcileech_tlps128_bar_controller.sv:678]
INFO: [Synth 8-6155] done synthesizing module 'pcileech_tlps128_bar_controller' (0#1) [G:/FPGA/20250327/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.srcs/sources_1/imports/src/pcileech_tlps128_bar_controller.sv:42]
INFO: [Synth 8-6157] synthesizing module 'pcileech_tlps128_cfgspace_shadow' [G:/FPGA/20250327/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.srcs/sources_1/imports/src/pcileech_tlps128_cfgspace_shadow.sv:15]
INFO: [Synth 8-6157] synthesizing module 'fifo_49_49_clk2' [G:/FPGA/20250327/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.runs/synth_1/.Xil/Vivado-97076-DESKTOP-7T6N58Q/realtime/fifo_49_49_clk2_stub.v:6]
INFO: [Synth 8-6155] done synthesizing module 'fifo_49_49_clk2' (0#1) [G:/FPGA/20250327/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.runs/synth_1/.Xil/Vivado-97076-DESKTOP-7T6N58Q/realtime/fifo_49_49_clk2_stub.v:6]
INFO: [Synth 8-6157] synthesizing module 'pcileech_mem_wrap' [G:/FPGA/20250327/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.srcs/sources_1/imports/src/pcileech_tlps128_cfgspace_shadow.sv:194]
INFO: [Synth 8-6157] synthesizing module 'bram_pcie_cfgspace' [G:/FPGA/20250327/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.runs/synth_1/.Xil/Vivado-97076-DESKTOP-7T6N58Q/realtime/bram_pcie_cfgspace_stub.v:6]
INFO: [Synth 8-6155] done synthesizing module 'bram_pcie_cfgspace' (0#1) [G:/FPGA/20250327/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.runs/synth_1/.Xil/Vivado-97076-DESKTOP-7T6N58Q/realtime/bram_pcie_cfgspace_stub.v:6]
INFO: [Synth 8-6157] synthesizing module 'drom_pcie_cfgspace_writemask' [G:/FPGA/20250327/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.runs/synth_1/.Xil/Vivado-97076-DESKTOP-7T6N58Q/realtime/drom_pcie_cfgspace_writemask_stub.v:6]
INFO: [Synth 8-6155] done synthesizing module 'drom_pcie_cfgspace_writemask' (0#1) [G:/FPGA/20250327/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.runs/synth_1/.Xil/Vivado-97076-DESKTOP-7T6N58Q/realtime/drom_pcie_cfgspace_writemask_stub.v:6]
INFO: [Synth 8-6155] done synthesizing module 'pcileech_mem_wrap' (0#1) [G:/FPGA/20250327/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.srcs/sources_1/imports/src/pcileech_tlps128_cfgspace_shadow.sv:194]
INFO: [Synth 8-6157] synthesizing module 'pcileech_cfgspace_pcie_tx' [G:/FPGA/20250327/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.srcs/sources_1/imports/src/pcileech_tlps128_cfgspace_shadow.sv:147]
INFO: [Synth 8-6157] synthesizing module 'fifo_129_129_clk1' [G:/FPGA/20250327/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.runs/synth_1/.Xil/Vivado-97076-DESKTOP-7T6N58Q/realtime/fifo_129_129_clk1_stub.v:6]
INFO: [Synth 8-6155] done synthesizing module 'fifo_129_129_clk1' (0#1) [G:/FPGA/20250327/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.runs/synth_1/.Xil/Vivado-97076-DESKTOP-7T6N58Q/realtime/fifo_129_129_clk1_stub.v:6]
INFO: [Synth 8-6155] done synthesizing module 'pcileech_cfgspace_pcie_tx' (0#1) [G:/FPGA/20250327/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.srcs/sources_1/imports/src/pcileech_tlps128_cfgspace_shadow.sv:147]
INFO: [Synth 8-6157] synthesizing module 'fifo_43_43_clk2' [G:/FPGA/20250327/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.runs/synth_1/.Xil/Vivado-97076-DESKTOP-7T6N58Q/realtime/fifo_43_43_clk2_stub.v:6]
INFO: [Synth 8-6155] done synthesizing module 'fifo_43_43_clk2' (0#1) [G:/FPGA/20250327/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.runs/synth_1/.Xil/Vivado-97076-DESKTOP-7T6N58Q/realtime/fifo_43_43_clk2_stub.v:6]
INFO: [Synth 8-6155] done synthesizing module 'pcileech_tlps128_cfgspace_shadow' (0#1) [G:/FPGA/20250327/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.srcs/sources_1/imports/src/pcileech_tlps128_cfgspace_shadow.sv:15]
INFO: [Synth 8-6157] synthesizing module 'pcileech_tlps128_filter' [G:/FPGA/20250327/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.srcs/sources_1/imports/src/pcileech_pcie_tlp_a7.sv:156]
INFO: [Synth 8-6155] done synthesizing module 'pcileech_tlps128_filter' (0#1) [G:/FPGA/20250327/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.srcs/sources_1/imports/src/pcileech_pcie_tlp_a7.sv:156]
INFO: [Synth 8-6157] synthesizing module 'pcileech_tlps128_dst_fifo' [G:/FPGA/20250327/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.srcs/sources_1/imports/src/pcileech_pcie_tlp_a7.sv:104]
INFO: [Synth 8-6157] synthesizing module 'fifo_134_134_clk2' [G:/FPGA/20250327/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.runs/synth_1/.Xil/Vivado-97076-DESKTOP-7T6N58Q/realtime/fifo_134_134_clk2_stub.v:6]
INFO: [Synth 8-6155] done synthesizing module 'fifo_134_134_clk2' (0#1) [G:/FPGA/20250327/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.runs/synth_1/.Xil/Vivado-97076-DESKTOP-7T6N58Q/realtime/fifo_134_134_clk2_stub.v:6]
INFO: [Synth 8-6155] done synthesizing module 'pcileech_tlps128_dst_fifo' (0#1) [G:/FPGA/20250327/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.srcs/sources_1/imports/src/pcileech_pcie_tlp_a7.sv:104]
INFO: [Synth 8-6157] synthesizing module 'pcileech_tlps128_src_fifo' [G:/FPGA/20250327/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.srcs/sources_1/imports/src/pcileech_pcie_tlp_a7.sv:206]
INFO: [Synth 8-6157] synthesizing module 'fifo_1_1_clk2' [G:/FPGA/20250327/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.runs/synth_1/.Xil/Vivado-97076-DESKTOP-7T6N58Q/realtime/fifo_1_1_clk2_stub.v:6]
INFO: [Synth 8-6155] done synthesizing module 'fifo_1_1_clk2' (0#1) [G:/FPGA/20250327/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.runs/synth_1/.Xil/Vivado-97076-DESKTOP-7T6N58Q/realtime/fifo_1_1_clk2_stub.v:6]
INFO: [Synth 8-6157] synthesizing module 'fifo_134_134_clk2_rxfifo' [G:/FPGA/20250327/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.runs/synth_1/.Xil/Vivado-97076-DESKTOP-7T6N58Q/realtime/fifo_134_134_clk2_rxfifo_stub.v:6]
INFO: [Synth 8-6155] done synthesizing module 'fifo_134_134_clk2_rxfifo' (0#1) [G:/FPGA/20250327/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.runs/synth_1/.Xil/Vivado-97076-DESKTOP-7T6N58Q/realtime/fifo_134_134_clk2_rxfifo_stub.v:6]
INFO: [Synth 8-6155] done synthesizing module 'pcileech_tlps128_src_fifo' (0#1) [G:/FPGA/20250327/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.srcs/sources_1/imports/src/pcileech_pcie_tlp_a7.sv:206]
INFO: [Synth 8-6157] synthesizing module 'pcileech_tlps128_sink_mux1' [G:/FPGA/20250327/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.srcs/sources_1/imports/src/pcileech_pcie_tlp_a7.sv:294]
INFO: [Synth 8-6155] done synthesizing module 'pcileech_tlps128_sink_mux1' (0#1) [G:/FPGA/20250327/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.srcs/sources_1/imports/src/pcileech_pcie_tlp_a7.sv:294]
INFO: [Synth 8-6155] done synthesizing module 'pcileech_pcie_tlp_a7' (0#1) [G:/FPGA/20250327/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.srcs/sources_1/imports/src/pcileech_pcie_tlp_a7.sv:13]
INFO: [Synth 8-6157] synthesizing module 'pcileech_tlps128_dst64' [G:/FPGA/20250327/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.srcs/sources_1/imports/src/pcileech_pcie_a7.sv:269]
INFO: [Synth 8-6155] done synthesizing module 'pcileech_tlps128_dst64' (0#1) [G:/FPGA/20250327/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.srcs/sources_1/imports/src/pcileech_pcie_a7.sv:269]
INFO: [Synth 8-6157] synthesizing module 'pcie_7x_0' [G:/FPGA/20250327/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.runs/synth_1/.Xil/Vivado-97076-DESKTOP-7T6N58Q/realtime/pcie_7x_0_stub.v:6]
INFO: [Synth 8-6155] done synthesizing module 'pcie_7x_0' (0#1) [G:/FPGA/20250327/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.runs/synth_1/.Xil/Vivado-97076-DESKTOP-7T6N58Q/realtime/pcie_7x_0_stub.v:6]
INFO: [Synth 8-6155] done synthesizing module 'pcileech_pcie_a7' (0#1) [G:/FPGA/20250327/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.srcs/sources_1/imports/src/pcileech_pcie_a7.sv:13]
INFO: [Synth 8-6155] done synthesizing module 'pcileech_100t484_x1_top' (0#1) [G:/FPGA/20250327/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.srcs/sources_1/imports/src/pcileech_100t484_x1_top.sv:13]
WARNING: [Synth 8-6014] Unused sequential element ft601_data_reg_reg was removed.  [G:/FPGA/20250327/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.srcs/sources_1/imports/src/pcileech_ft601_optimized.sv:190]
WARNING: [Synth 8-6014] Unused sequential element ft601_data_valid_reg_reg was removed.  [G:/FPGA/20250327/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.srcs/sources_1/imports/src/pcileech_ft601_optimized.sv:191]
WARNING: [Synth 8-6014] Unused sequential element tx_queue_a_reg was removed. 
WARNING: [Synth 8-6014] Unused sequential element tx_queue_b_reg was removed. 
WARNING: [Synth 8-3848] Net ft601_data_out_reg in module/entity pcileech_ft601_optimized does not have driver. [G:/FPGA/20250327/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.srcs/sources_1/imports/src/pcileech_ft601_optimized.sv:77]
WARNING: [Synth 8-3848] Net din_req_data in module/entity pcileech_ft601_optimized does not have driver. [G:/FPGA/20250327/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.srcs/sources_1/imports/src/pcileech_ft601_optimized.sv:33]
WARNING: [Synth 8-3848] Net tlps_filtered\.tready in module/entity pcileech_pcie_tlp_a7 does not have driver. [G:/FPGA/20250327/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.srcs/sources_1/imports/src/pcileech_header.svh:174]
WARNING: [Synth 8-3848] Net tlps_filtered\.has_data in module/entity pcileech_pcie_tlp_a7 does not have driver. [G:/FPGA/20250327/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.srcs/sources_1/imports/src/pcileech_header.svh:175]
WARNING: [Synth 8-3848] Net tlp_tx\.user in module/entity pcileech_tlps128_dst64 does not have driver. [G:/FPGA/20250327/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.srcs/sources_1/imports/src/pcileech_pcie_a7.sv:272]
WARNING: [Synth 8-3848] Net tlps_rx\.tready in module/entity pcileech_pcie_a7 does not have driver. [G:/FPGA/20250327/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.srcs/sources_1/imports/src/pcileech_header.svh:174]
WARNING: [Synth 8-3848] Net tlps_rx\.has_data in module/entity pcileech_pcie_a7 does not have driver. [G:/FPGA/20250327/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.srcs/sources_1/imports/src/pcileech_header.svh:175]
WARNING: [Synth 8-3917] design pcileech_100t484_x1_top has port pcie_wake_n driven by constant 1
WARNING: [Synth 8-7129] Port tlp_tx\.user[21] in module pcileech_tlps128_dst64 is either unconnected or has no load
WARNING: [Synth 8-7129] Port tlp_tx\.user[20] in module pcileech_tlps128_dst64 is either unconnected or has no load
WARNING: [Synth 8-7129] Port tlp_tx\.user[19] in module pcileech_tlps128_dst64 is either unconnected or has no load
WARNING: [Synth 8-7129] Port tlp_tx\.user[18] in module pcileech_tlps128_dst64 is either unconnected or has no load
WARNING: [Synth 8-7129] Port tlp_tx\.user[17] in module pcileech_tlps128_dst64 is either unconnected or has no load
WARNING: [Synth 8-7129] Port tlp_tx\.user[16] in module pcileech_tlps128_dst64 is either unconnected or has no load
WARNING: [Synth 8-7129] Port tlp_tx\.user[15] in module pcileech_tlps128_dst64 is either unconnected or has no load
WARNING: [Synth 8-7129] Port tlp_tx\.user[14] in module pcileech_tlps128_dst64 is either unconnected or has no load
WARNING: [Synth 8-7129] Port tlp_tx\.user[13] in module pcileech_tlps128_dst64 is either unconnected or has no load
WARNING: [Synth 8-7129] Port tlp_tx\.user[12] in module pcileech_tlps128_dst64 is either unconnected or has no load
WARNING: [Synth 8-7129] Port tlp_tx\.user[11] in module pcileech_tlps128_dst64 is either unconnected or has no load
WARNING: [Synth 8-7129] Port tlp_tx\.user[10] in module pcileech_tlps128_dst64 is either unconnected or has no load
WARNING: [Synth 8-7129] Port tlp_tx\.user[9] in module pcileech_tlps128_dst64 is either unconnected or has no load
WARNING: [Synth 8-7129] Port tlp_tx\.user[8] in module pcileech_tlps128_dst64 is either unconnected or has no load
WARNING: [Synth 8-7129] Port tlp_tx\.user[7] in module pcileech_tlps128_dst64 is either unconnected or has no load
WARNING: [Synth 8-7129] Port tlp_tx\.user[6] in module pcileech_tlps128_dst64 is either unconnected or has no load
WARNING: [Synth 8-7129] Port tlp_tx\.user[5] in module pcileech_tlps128_dst64 is either unconnected or has no load
WARNING: [Synth 8-7129] Port tlp_tx\.user[4] in module pcileech_tlps128_dst64 is either unconnected or has no load
WARNING: [Synth 8-7129] Port tlp_tx\.user[3] in module pcileech_tlps128_dst64 is either unconnected or has no load
WARNING: [Synth 8-7129] Port tlp_tx\.user[2] in module pcileech_tlps128_dst64 is either unconnected or has no load
WARNING: [Synth 8-7129] Port tlp_tx\.user[1] in module pcileech_tlps128_dst64 is either unconnected or has no load
WARNING: [Synth 8-7129] Port tlp_tx\.user[0] in module pcileech_tlps128_dst64 is either unconnected or has no load
WARNING: [Synth 8-7129] Port tlps_in\.tkeepdw[0] in module pcileech_tlps128_dst64 is either unconnected or has no load
WARNING: [Synth 8-7129] Port tlps_in\.tuser[8] in module pcileech_tlps128_dst64 is either unconnected or has no load
WARNING: [Synth 8-7129] Port tlps_in\.tuser[7] in module pcileech_tlps128_dst64 is either unconnected or has no load
WARNING: [Synth 8-7129] Port tlps_in\.tuser[6] in module pcileech_tlps128_dst64 is either unconnected or has no load
WARNING: [Synth 8-7129] Port tlps_in\.tuser[5] in module pcileech_tlps128_dst64 is either unconnected or has no load
WARNING: [Synth 8-7129] Port tlps_in\.tuser[4] in module pcileech_tlps128_dst64 is either unconnected or has no load
WARNING: [Synth 8-7129] Port tlps_in\.tuser[3] in module pcileech_tlps128_dst64 is either unconnected or has no load
WARNING: [Synth 8-7129] Port tlps_in\.tuser[2] in module pcileech_tlps128_dst64 is either unconnected or has no load
WARNING: [Synth 8-7129] Port tlps_in\.tuser[1] in module pcileech_tlps128_dst64 is either unconnected or has no load
WARNING: [Synth 8-7129] Port tlps_in\.tuser[0] in module pcileech_tlps128_dst64 is either unconnected or has no load
WARNING: [Synth 8-7129] Port tlps_in\.has_data in module pcileech_tlps128_dst64 is either unconnected or has no load
WARNING: [Synth 8-7129] Port tlps_out\.tuser[8] in module pcileech_tlps128_src_fifo is either unconnected or has no load
WARNING: [Synth 8-7129] Port tlps_out\.tuser[7] in module pcileech_tlps128_src_fifo is either unconnected or has no load
WARNING: [Synth 8-7129] Port tlps_out\.tuser[6] in module pcileech_tlps128_src_fifo is either unconnected or has no load
WARNING: [Synth 8-7129] Port tlps_out\.tuser[5] in module pcileech_tlps128_src_fifo is either unconnected or has no load
WARNING: [Synth 8-7129] Port tlps_out\.tuser[4] in module pcileech_tlps128_src_fifo is either unconnected or has no load
WARNING: [Synth 8-7129] Port tlps_out\.tuser[3] in module pcileech_tlps128_src_fifo is either unconnected or has no load
WARNING: [Synth 8-7129] Port tlps_out\.tuser[2] in module pcileech_tlps128_src_fifo is either unconnected or has no load
WARNING: [Synth 8-7129] Port tlps_out\.tuser[1] in module pcileech_tlps128_src_fifo is either unconnected or has no load
WARNING: [Synth 8-7129] Port tlps_in\.tuser[8] in module pcileech_tlps128_dst_fifo is either unconnected or has no load
WARNING: [Synth 8-7129] Port tlps_in\.tuser[7] in module pcileech_tlps128_dst_fifo is either unconnected or has no load
WARNING: [Synth 8-7129] Port tlps_in\.tuser[6] in module pcileech_tlps128_dst_fifo is either unconnected or has no load
WARNING: [Synth 8-7129] Port tlps_in\.tuser[5] in module pcileech_tlps128_dst_fifo is either unconnected or has no load
WARNING: [Synth 8-7129] Port tlps_in\.tuser[4] in module pcileech_tlps128_dst_fifo is either unconnected or has no load
WARNING: [Synth 8-7129] Port tlps_in\.tuser[3] in module pcileech_tlps128_dst_fifo is either unconnected or has no load
WARNING: [Synth 8-7129] Port tlps_in\.tuser[2] in module pcileech_tlps128_dst_fifo is either unconnected or has no load
WARNING: [Synth 8-7129] Port tlps_in\.tuser[1] in module pcileech_tlps128_dst_fifo is either unconnected or has no load
WARNING: [Synth 8-7129] Port tlps_in\.tready in module pcileech_tlps128_dst_fifo is either unconnected or has no load
WARNING: [Synth 8-7129] Port tlps_in\.has_data in module pcileech_tlps128_dst_fifo is either unconnected or has no load
WARNING: [Synth 8-7129] Port dfifo\.tx_data[31] in module pcileech_tlps128_dst_fifo is either unconnected or has no load
WARNING: [Synth 8-7129] Port dfifo\.tx_data[30] in module pcileech_tlps128_dst_fifo is either unconnected or has no load
WARNING: [Synth 8-7129] Port dfifo\.tx_data[29] in module pcileech_tlps128_dst_fifo is either unconnected or has no load
WARNING: [Synth 8-7129] Port dfifo\.tx_data[28] in module pcileech_tlps128_dst_fifo is either unconnected or has no load
WARNING: [Synth 8-7129] Port dfifo\.tx_data[27] in module pcileech_tlps128_dst_fifo is either unconnected or has no load
WARNING: [Synth 8-7129] Port dfifo\.tx_data[26] in module pcileech_tlps128_dst_fifo is either unconnected or has no load
WARNING: [Synth 8-7129] Port dfifo\.tx_data[25] in module pcileech_tlps128_dst_fifo is either unconnected or has no load
WARNING: [Synth 8-7129] Port dfifo\.tx_data[24] in module pcileech_tlps128_dst_fifo is either unconnected or has no load
WARNING: [Synth 8-7129] Port dfifo\.tx_data[23] in module pcileech_tlps128_dst_fifo is either unconnected or has no load
WARNING: [Synth 8-7129] Port dfifo\.tx_data[22] in module pcileech_tlps128_dst_fifo is either unconnected or has no load
WARNING: [Synth 8-7129] Port dfifo\.tx_data[21] in module pcileech_tlps128_dst_fifo is either unconnected or has no load
WARNING: [Synth 8-7129] Port dfifo\.tx_data[20] in module pcileech_tlps128_dst_fifo is either unconnected or has no load
WARNING: [Synth 8-7129] Port dfifo\.tx_data[19] in module pcileech_tlps128_dst_fifo is either unconnected or has no load
WARNING: [Synth 8-7129] Port dfifo\.tx_data[18] in module pcileech_tlps128_dst_fifo is either unconnected or has no load
WARNING: [Synth 8-7129] Port dfifo\.tx_data[17] in module pcileech_tlps128_dst_fifo is either unconnected or has no load
WARNING: [Synth 8-7129] Port dfifo\.tx_data[16] in module pcileech_tlps128_dst_fifo is either unconnected or has no load
WARNING: [Synth 8-7129] Port dfifo\.tx_data[15] in module pcileech_tlps128_dst_fifo is either unconnected or has no load
WARNING: [Synth 8-7129] Port dfifo\.tx_data[14] in module pcileech_tlps128_dst_fifo is either unconnected or has no load
WARNING: [Synth 8-7129] Port dfifo\.tx_data[13] in module pcileech_tlps128_dst_fifo is either unconnected or has no load
WARNING: [Synth 8-7129] Port dfifo\.tx_data[12] in module pcileech_tlps128_dst_fifo is either unconnected or has no load
WARNING: [Synth 8-7129] Port dfifo\.tx_data[11] in module pcileech_tlps128_dst_fifo is either unconnected or has no load
WARNING: [Synth 8-7129] Port dfifo\.tx_data[10] in module pcileech_tlps128_dst_fifo is either unconnected or has no load
WARNING: [Synth 8-7129] Port dfifo\.tx_data[9] in module pcileech_tlps128_dst_fifo is either unconnected or has no load
WARNING: [Synth 8-7129] Port dfifo\.tx_data[8] in module pcileech_tlps128_dst_fifo is either unconnected or has no load
WARNING: [Synth 8-7129] Port dfifo\.tx_data[7] in module pcileech_tlps128_dst_fifo is either unconnected or has no load
WARNING: [Synth 8-7129] Port dfifo\.tx_data[6] in module pcileech_tlps128_dst_fifo is either unconnected or has no load
WARNING: [Synth 8-7129] Port dfifo\.tx_data[5] in module pcileech_tlps128_dst_fifo is either unconnected or has no load
WARNING: [Synth 8-7129] Port dfifo\.tx_data[4] in module pcileech_tlps128_dst_fifo is either unconnected or has no load
WARNING: [Synth 8-7129] Port dfifo\.tx_data[3] in module pcileech_tlps128_dst_fifo is either unconnected or has no load
WARNING: [Synth 8-7129] Port dfifo\.tx_data[2] in module pcileech_tlps128_dst_fifo is either unconnected or has no load
WARNING: [Synth 8-7129] Port dfifo\.tx_data[1] in module pcileech_tlps128_dst_fifo is either unconnected or has no load
WARNING: [Synth 8-7129] Port dfifo\.tx_data[0] in module pcileech_tlps128_dst_fifo is either unconnected or has no load
WARNING: [Synth 8-7129] Port dfifo\.tx_last in module pcileech_tlps128_dst_fifo is either unconnected or has no load
WARNING: [Synth 8-7129] Port dfifo\.tx_valid in module pcileech_tlps128_dst_fifo is either unconnected or has no load
WARNING: [Synth 8-7129] Port tlps_in\.tready in module pcileech_tlps128_filter is either unconnected or has no load
WARNING: [Synth 8-7129] Port tlps_in\.has_data in module pcileech_tlps128_filter is either unconnected or has no load
WARNING: [Synth 8-7129] Port tlps_out\.tready in module pcileech_tlps128_filter is either unconnected or has no load
WARNING: [Synth 8-7129] Port tlps_out\.has_data in module pcileech_tlps128_filter is either unconnected or has no load
WARNING: [Synth 8-7129] Port tlps_in\.tdata[95] in module pcileech_tlps128_cfgspace_shadow is either unconnected or has no load
WARNING: [Synth 8-7129] Port tlps_in\.tdata[94] in module pcileech_tlps128_cfgspace_shadow is either unconnected or has no load
WARNING: [Synth 8-7129] Port tlps_in\.tdata[93] in module pcileech_tlps128_cfgspace_shadow is either unconnected or has no load
WARNING: [Synth 8-7129] Port tlps_in\.tdata[92] in module pcileech_tlps128_cfgspace_shadow is either unconnected or has no load
WARNING: [Synth 8-7129] Port tlps_in\.tdata[91] in module pcileech_tlps128_cfgspace_shadow is either unconnected or has no load
WARNING: [Synth 8-7129] Port tlps_in\.tdata[90] in module pcileech_tlps128_cfgspace_shadow is either unconnected or has no load
WARNING: [Synth 8-7129] Port tlps_in\.tdata[89] in module pcileech_tlps128_cfgspace_shadow is either unconnected or has no load
WARNING: [Synth 8-7129] Port tlps_in\.tdata[88] in module pcileech_tlps128_cfgspace_shadow is either unconnected or has no load
WARNING: [Synth 8-7129] Port tlps_in\.tdata[87] in module pcileech_tlps128_cfgspace_shadow is either unconnected or has no load
WARNING: [Synth 8-7129] Port tlps_in\.tdata[86] in module pcileech_tlps128_cfgspace_shadow is either unconnected or has no load
WARNING: [Synth 8-7129] Port tlps_in\.tdata[85] in module pcileech_tlps128_cfgspace_shadow is either unconnected or has no load
INFO: [Common 17-14] Message 'Synth 8-7129' appears 100 times and further instances of the messages will be disabled. Use the Tcl command set_msg_config to change the current settings.
---------------------------------------------------------------------------------
Finished RTL Elaboration : Time (s): cpu = 00:00:04 ; elapsed = 00:00:06 . Memory (MB): peak = 1511.809 ; gain = 657.332
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Start Handling Custom Attributes
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Finished Handling Custom Attributes : Time (s): cpu = 00:00:04 ; elapsed = 00:00:06 . Memory (MB): peak = 1511.809 ; gain = 657.332
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Finished RTL Optimization Phase 1 : Time (s): cpu = 00:00:04 ; elapsed = 00:00:06 . Memory (MB): peak = 1511.809 ; gain = 657.332
---------------------------------------------------------------------------------
Netlist sorting complete. Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.243 . Memory (MB): peak = 1511.809 ; gain = 0.000
INFO: [Netlist 29-17] Analyzing 1 Unisim elements for replacement
INFO: [Netlist 29-28] Unisim Transformation completed in 0 CPU seconds
INFO: [Project 1-570] Preparing netlist for logic optimization

Processing XDC Constraints
Initializing timing engine
Parsing XDC File [g:/FPGA/20250327/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.gen/sources_1/ip/bram_pcie_cfgspace/bram_pcie_cfgspace/bram_pcie_cfgspace_in_context.xdc] for cell 'i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_cfgspace_shadow/i_pcileech_mem_wrap/i_bram_pcie_cfgspace'
Finished Parsing XDC File [g:/FPGA/20250327/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.gen/sources_1/ip/bram_pcie_cfgspace/bram_pcie_cfgspace/bram_pcie_cfgspace_in_context.xdc] for cell 'i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_cfgspace_shadow/i_pcileech_mem_wrap/i_bram_pcie_cfgspace'
Parsing XDC File [g:/FPGA/20250327/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.gen/sources_1/ip/fifo_64_64_clk1_fifocmd/fifo_64_64_clk1_fifocmd/fifo_64_64_clk1_fifocmd_in_context.xdc] for cell 'i_pcileech_fifo/i_fifo_cmd_rx'
Finished Parsing XDC File [g:/FPGA/20250327/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.gen/sources_1/ip/fifo_64_64_clk1_fifocmd/fifo_64_64_clk1_fifocmd/fifo_64_64_clk1_fifocmd_in_context.xdc] for cell 'i_pcileech_fifo/i_fifo_cmd_rx'
Parsing XDC File [g:/FPGA/20250327/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.gen/sources_1/ip/fifo_34_34/fifo_34_34/fifo_34_34_in_context.xdc] for cell 'i_pcileech_fifo/i_fifo_loop_tx'
Finished Parsing XDC File [g:/FPGA/20250327/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.gen/sources_1/ip/fifo_34_34/fifo_34_34/fifo_34_34_in_context.xdc] for cell 'i_pcileech_fifo/i_fifo_loop_tx'
Parsing XDC File [g:/FPGA/20250327/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.gen/sources_1/ip/fifo_34_34/fifo_34_34/fifo_34_34_in_context.xdc] for cell 'i_pcileech_fifo/i_fifo_cmd_tx'
Finished Parsing XDC File [g:/FPGA/20250327/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.gen/sources_1/ip/fifo_34_34/fifo_34_34/fifo_34_34_in_context.xdc] for cell 'i_pcileech_fifo/i_fifo_cmd_tx'
Parsing XDC File [g:/FPGA/20250327/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.gen/sources_1/ip/pcie_7x_0/pcie_7x_0/pcie_7x_0_in_context.xdc] for cell 'i_pcileech_pcie_a7/i_pcie_7x_0'
Finished Parsing XDC File [g:/FPGA/20250327/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.gen/sources_1/ip/pcie_7x_0/pcie_7x_0/pcie_7x_0_in_context.xdc] for cell 'i_pcileech_pcie_a7/i_pcie_7x_0'
Parsing XDC File [g:/FPGA/20250327/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.gen/sources_1/ip/fifo_134_134_clk1_bar_rdrsp/fifo_134_134_clk1_bar_rdrsp/fifo_134_134_clk1_bar_rdrsp_in_context.xdc] for cell 'i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_bar_controller/i_pcileech_tlps128_bar_rdengine/i_fifo_134_134_clk1_bar_rdrsp'
Finished Parsing XDC File [g:/FPGA/20250327/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.gen/sources_1/ip/fifo_134_134_clk1_bar_rdrsp/fifo_134_134_clk1_bar_rdrsp/fifo_134_134_clk1_bar_rdrsp_in_context.xdc] for cell 'i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_bar_controller/i_pcileech_tlps128_bar_rdengine/i_fifo_134_134_clk1_bar_rdrsp'
Parsing XDC File [g:/FPGA/20250327/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.gen/sources_1/ip/fifo_43_43_clk2/fifo_43_43_clk2/fifo_43_43_clk2_in_context.xdc] for cell 'i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_cfgspace_shadow/i_fifo_43_43_clk2'
Finished Parsing XDC File [g:/FPGA/20250327/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.gen/sources_1/ip/fifo_43_43_clk2/fifo_43_43_clk2/fifo_43_43_clk2_in_context.xdc] for cell 'i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_cfgspace_shadow/i_fifo_43_43_clk2'
Parsing XDC File [g:/FPGA/20250327/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.gen/sources_1/ip/drom_pcie_cfgspace_writemask/drom_pcie_cfgspace_writemask/drom_pcie_cfgspace_writemask_in_context.xdc] for cell 'i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_cfgspace_shadow/i_pcileech_mem_wrap/i_drom_pcie_cfgspace_writemask'
Finished Parsing XDC File [g:/FPGA/20250327/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.gen/sources_1/ip/drom_pcie_cfgspace_writemask/drom_pcie_cfgspace_writemask/drom_pcie_cfgspace_writemask_in_context.xdc] for cell 'i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_cfgspace_shadow/i_pcileech_mem_wrap/i_drom_pcie_cfgspace_writemask'
Parsing XDC File [g:/FPGA/20250327/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.gen/sources_1/ip/fifo_134_134_clk2_rxfifo/fifo_134_134_clk2_rxfifo/fifo_134_134_clk2_rxfifo_in_context.xdc] for cell 'i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_src_fifo/i_fifo_134_134_clk2_rxfifo'
Finished Parsing XDC File [g:/FPGA/20250327/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.gen/sources_1/ip/fifo_134_134_clk2_rxfifo/fifo_134_134_clk2_rxfifo/fifo_134_134_clk2_rxfifo_in_context.xdc] for cell 'i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_src_fifo/i_fifo_134_134_clk2_rxfifo'
Parsing XDC File [g:/FPGA/20250327/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.gen/sources_1/ip/fifo_64_64_clk2_comrx/fifo_64_64_clk2_comrx/fifo_64_64_clk2_comrx_in_context.xdc] for cell 'i_pcileech_com/i_fifo_64_64_clk2_comrx'
Finished Parsing XDC File [g:/FPGA/20250327/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.gen/sources_1/ip/fifo_64_64_clk2_comrx/fifo_64_64_clk2_comrx/fifo_64_64_clk2_comrx_in_context.xdc] for cell 'i_pcileech_com/i_fifo_64_64_clk2_comrx'
Parsing XDC File [g:/FPGA/20250327/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.gen/sources_1/ip/fifo_141_141_clk1_bar_wr/fifo_141_141_clk1_bar_wr/fifo_141_141_clk1_bar_wr_in_context.xdc] for cell 'i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_bar_controller/i_pcileech_tlps128_bar_wrengine/i_fifo_141_141_clk1_bar_wr'
Finished Parsing XDC File [g:/FPGA/20250327/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.gen/sources_1/ip/fifo_141_141_clk1_bar_wr/fifo_141_141_clk1_bar_wr/fifo_141_141_clk1_bar_wr_in_context.xdc] for cell 'i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_bar_controller/i_pcileech_tlps128_bar_wrengine/i_fifo_141_141_clk1_bar_wr'
Parsing XDC File [g:/FPGA/20250327/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.gen/sources_1/ip/fifo_64_64/fifo_64_64/fifo_64_64_in_context.xdc] for cell 'i_pcileech_pcie_a7/i_pcileech_pcie_cfg_a7/i_fifo_pcie_cfg_tx'
Finished Parsing XDC File [g:/FPGA/20250327/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.gen/sources_1/ip/fifo_64_64/fifo_64_64/fifo_64_64_in_context.xdc] for cell 'i_pcileech_pcie_a7/i_pcileech_pcie_cfg_a7/i_fifo_pcie_cfg_tx'
Parsing XDC File [g:/FPGA/20250327/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.gen/sources_1/ip/fifo_134_134_clk2/fifo_134_134_clk2/fifo_134_134_clk2_in_context.xdc] for cell 'i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_dst_fifo/i_fifo_134_134_clk2'
Finished Parsing XDC File [g:/FPGA/20250327/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.gen/sources_1/ip/fifo_134_134_clk2/fifo_134_134_clk2/fifo_134_134_clk2_in_context.xdc] for cell 'i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_dst_fifo/i_fifo_134_134_clk2'
Parsing XDC File [g:/FPGA/20250327/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.gen/sources_1/ip/fifo_49_49_clk2/fifo_49_49_clk2/fifo_49_49_clk2_in_context.xdc] for cell 'i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_cfgspace_shadow/i_fifo_49_49_clk2'
Finished Parsing XDC File [g:/FPGA/20250327/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.gen/sources_1/ip/fifo_49_49_clk2/fifo_49_49_clk2/fifo_49_49_clk2_in_context.xdc] for cell 'i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_cfgspace_shadow/i_fifo_49_49_clk2'
Parsing XDC File [g:/FPGA/20250327/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.gen/sources_1/ip/fifo_74_74_clk1_bar_rd1/fifo_74_74_clk1_bar_rd1/fifo_74_74_clk1_bar_rd1_in_context.xdc] for cell 'i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_bar_controller/i_pcileech_tlps128_bar_rdengine/i_fifo_74_74_clk1_bar_rd1'
Finished Parsing XDC File [g:/FPGA/20250327/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.gen/sources_1/ip/fifo_74_74_clk1_bar_rd1/fifo_74_74_clk1_bar_rd1/fifo_74_74_clk1_bar_rd1_in_context.xdc] for cell 'i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_bar_controller/i_pcileech_tlps128_bar_rdengine/i_fifo_74_74_clk1_bar_rd1'
Parsing XDC File [g:/FPGA/20250327/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.gen/sources_1/ip/bram_bar_zero4k/bram_bar_zero4k/bram_bar_zero4k_in_context.xdc] for cell 'i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_bar_controller/i_bar0/i_bram_bar_zero4k'
Finished Parsing XDC File [g:/FPGA/20250327/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.gen/sources_1/ip/bram_bar_zero4k/bram_bar_zero4k/bram_bar_zero4k_in_context.xdc] for cell 'i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_bar_controller/i_bar0/i_bram_bar_zero4k'
Parsing XDC File [g:/FPGA/20250327/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.gen/sources_1/ip/fifo_32_32_clk1_comtx/fifo_32_32_clk1_comtx/fifo_32_32_clk1_comtx_in_context.xdc] for cell 'i_pcileech_com/i_fifo_32_32_clk2_comtx'
Finished Parsing XDC File [g:/FPGA/20250327/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.gen/sources_1/ip/fifo_32_32_clk1_comtx/fifo_32_32_clk1_comtx/fifo_32_32_clk1_comtx_in_context.xdc] for cell 'i_pcileech_com/i_fifo_32_32_clk2_comtx'
Parsing XDC File [g:/FPGA/20250327/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.gen/sources_1/ip/fifo_32_32_clk2/fifo_32_32_clk2/fifo_32_32_clk2_in_context.xdc] for cell 'i_pcileech_pcie_a7/i_pcileech_pcie_cfg_a7/i_fifo_pcie_cfg_rx'
Finished Parsing XDC File [g:/FPGA/20250327/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.gen/sources_1/ip/fifo_32_32_clk2/fifo_32_32_clk2/fifo_32_32_clk2_in_context.xdc] for cell 'i_pcileech_pcie_a7/i_pcileech_pcie_cfg_a7/i_fifo_pcie_cfg_rx'
Parsing XDC File [g:/FPGA/20250327/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.gen/sources_1/ip/fifo_256_32_clk2_comtx/fifo_256_32_clk2_comtx/fifo_256_32_clk2_comtx_in_context.xdc] for cell 'i_pcileech_com/i_fifo_256_32_clk2_comtx'
Finished Parsing XDC File [g:/FPGA/20250327/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.gen/sources_1/ip/fifo_256_32_clk2_comtx/fifo_256_32_clk2_comtx/fifo_256_32_clk2_comtx_in_context.xdc] for cell 'i_pcileech_com/i_fifo_256_32_clk2_comtx'
Parsing XDC File [g:/FPGA/20250327/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.gen/sources_1/ip/fifo_129_129_clk1/fifo_129_129_clk1/fifo_129_129_clk1_in_context.xdc] for cell 'i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_cfgspace_shadow/i_pcileech_cfgspace_pcie_tx/i_fifo_129_129_clk1'
Finished Parsing XDC File [g:/FPGA/20250327/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.gen/sources_1/ip/fifo_129_129_clk1/fifo_129_129_clk1/fifo_129_129_clk1_in_context.xdc] for cell 'i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_cfgspace_shadow/i_pcileech_cfgspace_pcie_tx/i_fifo_129_129_clk1'
Parsing XDC File [g:/FPGA/20250327/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.gen/sources_1/ip/fifo_1_1_clk2/fifo_1_1_clk2/fifo_1_1_clk2_in_context.xdc] for cell 'i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_src_fifo/i_fifo_1_1_clk2'
Finished Parsing XDC File [g:/FPGA/20250327/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.gen/sources_1/ip/fifo_1_1_clk2/fifo_1_1_clk2/fifo_1_1_clk2_in_context.xdc] for cell 'i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_src_fifo/i_fifo_1_1_clk2'
Parsing XDC File [G:/FPGA/20250327/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.srcs/constrs_1/imports/src/pcileech_100t484_x1_captaindma_100t.xdc]
WARNING: [Vivado 12-180] No cells matched 'i_pcileech_com/i_pcileech_ft601/FT601_OE_N_reg'. [G:/FPGA/20250327/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.srcs/constrs_1/imports/src/pcileech_100t484_x1_captaindma_100t.xdc:78]
WARNING: [Vivado 12-180] No cells matched 'i_pcileech_com/i_pcileech_ft601/FT601_RD_N_reg'. [G:/FPGA/20250327/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.srcs/constrs_1/imports/src/pcileech_100t484_x1_captaindma_100t.xdc:79]
WARNING: [Vivado 12-180] No cells matched 'i_pcileech_com/i_pcileech_ft601/FT601_WR_N_reg'. [G:/FPGA/20250327/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.srcs/constrs_1/imports/src/pcileech_100t484_x1_captaindma_100t.xdc:80]
WARNING: [Vivado 12-180] No cells matched 'i_pcileech_com/i_pcileech_ft601/FT601_DATA_OUT_reg[0][*]'. [G:/FPGA/20250327/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.srcs/constrs_1/imports/src/pcileech_100t484_x1_captaindma_100t.xdc:81]
WARNING: [Vivado 12-508] No pins matched 'i_pcileech_com/i_pcileech_ft601/OE_reg/C'. [G:/FPGA/20250327/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.srcs/constrs_1/imports/src/pcileech_100t484_x1_captaindma_100t.xdc:83]
WARNING: [Vivado 12-508] No pins matched 'i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/user_lnk_up_int_reg/C'. [G:/FPGA/20250327/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.srcs/constrs_1/imports/src/pcileech_100t484_x1_captaindma_100t.xdc:86]
WARNING: [Vivado 12-508] No pins matched 'i_pcileech_pcie_a7/i_pcie_7x_0/inst/inst/user_reset_out_reg/C'. [G:/FPGA/20250327/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.srcs/constrs_1/imports/src/pcileech_100t484_x1_captaindma_100t.xdc:87]
Finished Parsing XDC File [G:/FPGA/20250327/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.srcs/constrs_1/imports/src/pcileech_100t484_x1_captaindma_100t.xdc]
WARNING: [Project 1-498] One or more constraints failed evaluation while reading constraint file [G:/FPGA/20250327/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.srcs/constrs_1/imports/src/pcileech_100t484_x1_captaindma_100t.xdc] and the design contains unresolved black boxes. These constraints will be read post-synthesis (as long as their source constraint file is marked as used_in_implementation) and should be applied correctly then. You should review the constraints listed in the file [.Xil/pcileech_100t484_x1_top_propImpl.xdc] and check the run log file to verify that these constraints were correctly applied.
INFO: [Project 1-236] Implementation specific constraints were found while reading constraint file [G:/FPGA/20250327/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.srcs/constrs_1/imports/src/pcileech_100t484_x1_captaindma_100t.xdc]. These constraints will be ignored for synthesis but will be used in implementation. Impacted constraints are listed in the file [.Xil/pcileech_100t484_x1_top_propImpl.xdc].
Resolution: To avoid this warning, move constraints listed in [.Xil/pcileech_100t484_x1_top_propImpl.xdc] to another XDC file and exclude this new file from synthesis with the used_in_synthesis property (File Properties dialog in GUI) and re-run elaboration/synthesis.
Completed Processing XDC Constraints

Netlist sorting complete. Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.001 . Memory (MB): peak = 1561.711 ; gain = 0.000
INFO: [Project 1-111] Unisim Transformation Summary:
No Unisim elements were transformed.

Constraint Validation Runtime : Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.028 . Memory (MB): peak = 1561.711 ; gain = 0.000
WARNING: [Timing 38-316] Clock period '20.000' specified during out-of-context synthesis of instance 'i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_bar_controller/i_bar0/i_bram_bar_zero4k' at clock pin 'clka' is different from the actual clock period '16.000', this can lead to different synthesis results.
WARNING: [Timing 38-316] Clock period '20.000' specified during out-of-context synthesis of instance 'i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_cfgspace_shadow/i_pcileech_mem_wrap/i_bram_pcie_cfgspace' at clock pin 'clka' is different from the actual clock period '16.000', this can lead to different synthesis results.
---------------------------------------------------------------------------------
Finished Constraint Validation : Time (s): cpu = 00:00:09 ; elapsed = 00:00:15 . Memory (MB): peak = 1571.070 ; gain = 716.594
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Start Loading Part and Timing Information
---------------------------------------------------------------------------------
Loading part: xc7a100tfgg484-2
---------------------------------------------------------------------------------
Finished Loading Part and Timing Information : Time (s): cpu = 00:00:09 ; elapsed = 00:00:15 . Memory (MB): peak = 1571.070 ; gain = 716.594
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Start Applying 'set_property' XDC Constraints
---------------------------------------------------------------------------------
Applied set_property KEEP_HIERARCHY = SOFT for i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_cfgspace_shadow/i_pcileech_mem_wrap/i_bram_pcie_cfgspace. (constraint file  auto generated constraint).
Applied set_property KEEP_HIERARCHY = SOFT for i_pcileech_fifo/i_fifo_cmd_rx. (constraint file  auto generated constraint).
Applied set_property KEEP_HIERARCHY = SOFT for i_pcileech_fifo/i_fifo_cmd_tx. (constraint file  auto generated constraint).
Applied set_property KEEP_HIERARCHY = SOFT for i_pcileech_fifo/i_fifo_loop_tx. (constraint file  auto generated constraint).
Applied set_property KEEP_HIERARCHY = SOFT for i_pcileech_pcie_a7/i_pcie_7x_0. (constraint file  auto generated constraint).
Applied set_property KEEP_HIERARCHY = SOFT for i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_bar_controller/i_pcileech_tlps128_bar_rdengine/i_fifo_134_134_clk1_bar_rdrsp. (constraint file  auto generated constraint).
Applied set_property KEEP_HIERARCHY = SOFT for i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_cfgspace_shadow/i_fifo_43_43_clk2. (constraint file  auto generated constraint).
Applied set_property KEEP_HIERARCHY = SOFT for i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_cfgspace_shadow/i_pcileech_mem_wrap/i_drom_pcie_cfgspace_writemask. (constraint file  auto generated constraint).
Applied set_property KEEP_HIERARCHY = SOFT for i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_src_fifo/i_fifo_134_134_clk2_rxfifo. (constraint file  auto generated constraint).
Applied set_property KEEP_HIERARCHY = SOFT for i_pcileech_com/i_fifo_64_64_clk2_comrx. (constraint file  auto generated constraint).
Applied set_property KEEP_HIERARCHY = SOFT for i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_bar_controller/i_pcileech_tlps128_bar_wrengine/i_fifo_141_141_clk1_bar_wr. (constraint file  auto generated constraint).
Applied set_property KEEP_HIERARCHY = SOFT for i_pcileech_pcie_a7/i_pcileech_pcie_cfg_a7/i_fifo_pcie_cfg_tx. (constraint file  auto generated constraint).
Applied set_property KEEP_HIERARCHY = SOFT for i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_dst_fifo/i_fifo_134_134_clk2. (constraint file  auto generated constraint).
Applied set_property KEEP_HIERARCHY = SOFT for i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_cfgspace_shadow/i_fifo_49_49_clk2. (constraint file  auto generated constraint).
Applied set_property KEEP_HIERARCHY = SOFT for i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_bar_controller/i_pcileech_tlps128_bar_rdengine/i_fifo_74_74_clk1_bar_rd1. (constraint file  auto generated constraint).
Applied set_property KEEP_HIERARCHY = SOFT for i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_bar_controller/i_bar0/i_bram_bar_zero4k. (constraint file  auto generated constraint).
Applied set_property KEEP_HIERARCHY = SOFT for i_pcileech_com/i_fifo_32_32_clk2_comtx. (constraint file  auto generated constraint).
Applied set_property KEEP_HIERARCHY = SOFT for i_pcileech_pcie_a7/i_pcileech_pcie_cfg_a7/i_fifo_pcie_cfg_rx. (constraint file  auto generated constraint).
Applied set_property KEEP_HIERARCHY = SOFT for i_pcileech_com/i_fifo_256_32_clk2_comtx. (constraint file  auto generated constraint).
Applied set_property KEEP_HIERARCHY = SOFT for i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_cfgspace_shadow/i_pcileech_cfgspace_pcie_tx/i_fifo_129_129_clk1. (constraint file  auto generated constraint).
Applied set_property KEEP_HIERARCHY = SOFT for i_pcileech_pcie_a7/i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_src_fifo/i_fifo_1_1_clk2. (constraint file  auto generated constraint).
---------------------------------------------------------------------------------
Finished applying 'set_property' XDC Constraints : Time (s): cpu = 00:00:09 ; elapsed = 00:00:15 . Memory (MB): peak = 1571.070 ; gain = 716.594
---------------------------------------------------------------------------------
INFO: [Synth 8-802] inferred FSM for state register 'state_reg' in module 'pcileech_ft601_optimized'
INFO: [Synth 8-802] inferred FSM for state register 'com_rx_valid64_dw_reg' in module 'pcileech_com'
INFO: [Synth 8-802] inferred FSM for state register 'state_reg' in module 'pcileech_tlps128_bar_wrengine'
---------------------------------------------------------------------------------------------------
                   State |                     New Encoding |                Previous Encoding 
---------------------------------------------------------------------------------------------------
                  S_IDLE |                              000 |                             0000
               S_RX_PREP |                              001 |                             0001
             S_RX_ACTIVE |                              010 |                             0010
              S_RX_BURST |                              011 |                             0011
               S_TX_PREP |                              100 |                             0100
             S_TX_ACTIVE |                              101 |                             0101
              S_TX_BURST |                              110 |                             0110
              S_COOLDOWN |                              111 |                             0111
---------------------------------------------------------------------------------------------------
INFO: [Synth 8-3354] encoded FSM with state register 'state_reg' using encoding 'sequential' in module 'pcileech_ft601_optimized'
---------------------------------------------------------------------------------------------------
                   State |                     New Encoding |                Previous Encoding 
---------------------------------------------------------------------------------------------------
                 iSTATE1 |                              001 |                               00
*
                 iSTATE0 |                              010 |                               01
                  iSTATE |                              100 |                               11
---------------------------------------------------------------------------------------------------
INFO: [Synth 8-3354] encoded FSM with state register 'com_rx_valid64_dw_reg' using encoding 'one-hot' in module 'pcileech_com'
INFO: [Synth 8-6159] Found Keep on FSM register 'state_reg' in module 'pcileech_tlps128_bar_wrengine', re-encoding will not be performed
---------------------------------------------------------------------------------------------------
                   State |                     New Encoding |                Previous Encoding 
---------------------------------------------------------------------------------------------------
*
                  iSTATE |                             0000 |                             0000
                 iSTATE0 |                             0001 |                             0001
                 iSTATE1 |                             0010 |                             0010
                 iSTATE2 |                             0100 |                             0100
                 iSTATE3 |                             0101 |                             0101
                 iSTATE4 |                             0110 |                             0110
                 iSTATE5 |                             0111 |                             0111
---------------------------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Finished RTL Optimization Phase 2 : Time (s): cpu = 00:00:10 ; elapsed = 00:00:18 . Memory (MB): peak = 1571.070 ; gain = 716.594
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Start RTL Component Statistics 
---------------------------------------------------------------------------------
Detailed RTL Component Info : 
+---Adders : 
	   3 Input   65 Bit       Adders := 1     
	   2 Input   64 Bit       Adders := 1     
	   2 Input   32 Bit       Adders := 3     
	   2 Input   19 Bit       Adders := 30    
	   2 Input   12 Bit       Adders := 2     
	   4 Input   11 Bit       Adders := 2     
	   2 Input   11 Bit       Adders := 1     
	   3 Input   11 Bit       Adders := 1     
	   2 Input   10 Bit       Adders := 1     
	   2 Input    6 Bit       Adders := 1     
	   2 Input    5 Bit       Adders := 5     
	  12 Input    4 Bit       Adders := 1     
	   9 Input    4 Bit       Adders := 1     
	   2 Input    4 Bit       Adders := 5     
	   3 Input    4 Bit       Adders := 2     
	   4 Input    4 Bit       Adders := 1     
	   5 Input    4 Bit       Adders := 1     
	   6 Input    4 Bit       Adders := 1     
	   7 Input    4 Bit       Adders := 1     
	   8 Input    4 Bit       Adders := 1     
+---XORs : 
	   2 Input      1 Bit         XORs := 2     
+---Registers : 
	              704 Bit    Registers := 1     
	              256 Bit    Registers := 1     
	              240 Bit    Registers := 1     
	              128 Bit    Registers := 5     
	               88 Bit    Registers := 4     
	               86 Bit    Registers := 3     
	               80 Bit    Registers := 1     
	               64 Bit    Registers := 3     
	               34 Bit    Registers := 1     
	               32 Bit    Registers := 23    
	               16 Bit    Registers := 2     
	               11 Bit    Registers := 3     
	               10 Bit    Registers := 3     
	                9 Bit    Registers := 1     
	                8 Bit    Registers := 1     
	                7 Bit    Registers := 2     
	                5 Bit    Registers := 4     
	                4 Bit    Registers := 28    
	                3 Bit    Registers := 1     
	                2 Bit    Registers := 1     
	                1 Bit    Registers := 45    
+---Muxes : 
	   2 Input  704 Bit        Muxes := 38    
	   5 Input  704 Bit        Muxes := 2     
	   2 Input  256 Bit        Muxes := 1     
	   2 Input  240 Bit        Muxes := 40    
	   4 Input  232 Bit        Muxes := 1     
	   2 Input  129 Bit        Muxes := 1     
	   2 Input  128 Bit        Muxes := 9     
	   2 Input   86 Bit        Muxes := 3     
	   3 Input   86 Bit        Muxes := 1     
	   2 Input   64 Bit        Muxes := 2     
	   6 Input   64 Bit        Muxes := 1     
	   5 Input   32 Bit        Muxes := 1     
	   2 Input   32 Bit        Muxes := 238   
	   8 Input   32 Bit        Muxes := 1     
	   2 Input   30 Bit        Muxes := 1     
	   2 Input   16 Bit        Muxes := 6     
	   4 Input   16 Bit        Muxes := 1     
	   2 Input   11 Bit        Muxes := 3     
	   2 Input   10 Bit        Muxes := 3     
	   2 Input    9 Bit        Muxes := 1     
	   2 Input    8 Bit        Muxes := 2     
	   2 Input    7 Bit        Muxes := 1     
	   2 Input    6 Bit        Muxes := 1     
	   2 Input    5 Bit        Muxes := 3     
	   2 Input    4 Bit        Muxes := 251   
	   8 Input    4 Bit        Muxes := 2     
	   3 Input    4 Bit        Muxes := 3     
	   2 Input    3 Bit        Muxes := 15    
	   6 Input    3 Bit        Muxes := 2     
	   8 Input    3 Bit        Muxes := 1     
	   2 Input    2 Bit        Muxes := 5     
	   3 Input    2 Bit        Muxes := 2     
	   2 Input    1 Bit        Muxes := 324   
	   8 Input    1 Bit        Muxes := 8     
	   3 Input    1 Bit        Muxes := 2     
	   5 Input    1 Bit        Muxes := 3     
---------------------------------------------------------------------------------
Finished RTL Component Statistics 
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Start Part Resource Summary
---------------------------------------------------------------------------------
Part Resources:
DSPs: 240 (col length:80)
BRAMs: 270 (col length: RAMB18 80 RAMB36 40)
---------------------------------------------------------------------------------
Finished Part Resource Summary
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Start Cross Boundary and Area Optimization
---------------------------------------------------------------------------------
WARNING: [Synth 8-3917] design pcileech_100t484_x1_top has port pcie_wake_n driven by constant 1
INFO: [Synth 8-3333] propagating constant 0 across sequential element (i_pcileech_mux/\dout_buf_data_reg[228] )
INFO: [Synth 8-3886] merging instance 'i_pcileech_mux/dout_buf_data_reg[229]' (FDSE) to 'i_pcileech_mux/dout_buf_data_reg[230]'
INFO: [Synth 8-3886] merging instance 'i_pcileech_mux/dout_buf_data_reg[230]' (FDSE) to 'i_pcileech_mux/dout_buf_data_reg[231]'
INFO: [Synth 8-4471] merging register 'i_bar1/rd_req_ctx_1_reg[87:0]' into 'i_bar0/drd_req_ctx_reg[87:0]' [G:/FPGA/20250327/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.srcs/sources_1/imports/src/pcileech_tlps128_bar_controller.sv:733]
INFO: [Synth 8-4471] merging register 'i_bar1/rd_rsp_ctx_reg[87:0]' into 'i_bar0/rd_rsp_ctx_reg[87:0]' [G:/FPGA/20250327/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.srcs/sources_1/imports/src/pcileech_tlps128_bar_controller.sv:736]
WARNING: [Synth 8-3936] Found unconnected internal register 'i_pcileech_tlps128_filter/tuser_reg' and it is trimmed from '9' to '1' bits. [G:/FPGA/20250327/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.srcs/sources_1/imports/src/pcileech_pcie_tlp_a7.sv:174]
INFO: [Synth 8-3886] merging instance 'i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_bar_controller/i_pcileech_tlps128_bar_rdengine/rd2_pkt2_reg[74]' (FDE) to 'i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_bar_controller/i_pcileech_tlps128_bar_rdengine/rd2_pkt2_reg[75]'
INFO: [Synth 8-3333] propagating constant 0 across sequential element (\i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_bar_controller/i_pcileech_tlps128_bar_rdengine/rd2_pkt2_reg[75] )
INFO: [Synth 8-3886] merging instance 'i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_bar_controller/i_bar0/drd_req_ctx_reg[24]' (FD) to 'i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_bar_controller/i_bar1/rd_req_addr_1_reg[24]'
INFO: [Synth 8-3886] merging instance 'i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_bar_controller/i_bar0/rd_rsp_ctx_reg[24]' (FD) to 'i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_bar_controller/i_bar1/rd_rsp_data_reg[24]'
INFO: [Synth 8-3886] merging instance 'i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_bar_controller/i_bar0/drd_req_ctx_reg[25]' (FD) to 'i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_bar_controller/i_bar1/rd_req_addr_1_reg[25]'
INFO: [Synth 8-3886] merging instance 'i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_bar_controller/i_bar0/rd_rsp_ctx_reg[25]' (FD) to 'i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_bar_controller/i_bar1/rd_rsp_data_reg[25]'
INFO: [Synth 8-3886] merging instance 'i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_bar_controller/i_bar0/drd_req_ctx_reg[26]' (FD) to 'i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_bar_controller/i_bar1/rd_req_addr_1_reg[26]'
INFO: [Synth 8-3886] merging instance 'i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_bar_controller/i_bar0/rd_rsp_ctx_reg[26]' (FD) to 'i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_bar_controller/i_bar1/rd_rsp_data_reg[26]'
INFO: [Synth 8-3886] merging instance 'i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_bar_controller/i_bar0/drd_req_ctx_reg[27]' (FD) to 'i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_bar_controller/i_bar1/rd_req_addr_1_reg[27]'
INFO: [Synth 8-3886] merging instance 'i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_bar_controller/i_bar0/rd_rsp_ctx_reg[27]' (FD) to 'i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_bar_controller/i_bar1/rd_rsp_data_reg[27]'
INFO: [Synth 8-3886] merging instance 'i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_bar_controller/i_bar0/drd_req_ctx_reg[28]' (FD) to 'i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_bar_controller/i_bar1/rd_req_addr_1_reg[28]'
INFO: [Synth 8-3886] merging instance 'i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_bar_controller/i_bar0/rd_rsp_ctx_reg[28]' (FD) to 'i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_bar_controller/i_bar1/rd_rsp_data_reg[28]'
INFO: [Synth 8-3886] merging instance 'i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_bar_controller/i_bar0/drd_req_ctx_reg[29]' (FD) to 'i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_bar_controller/i_bar1/rd_req_addr_1_reg[29]'
INFO: [Synth 8-3886] merging instance 'i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_bar_controller/i_bar0/rd_rsp_ctx_reg[29]' (FD) to 'i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_bar_controller/i_bar1/rd_rsp_data_reg[29]'
INFO: [Synth 8-3886] merging instance 'i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_bar_controller/i_bar0/drd_req_ctx_reg[30]' (FD) to 'i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_bar_controller/i_bar1/rd_req_addr_1_reg[30]'
INFO: [Synth 8-3886] merging instance 'i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_bar_controller/i_bar0/rd_rsp_ctx_reg[30]' (FD) to 'i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_bar_controller/i_bar1/rd_rsp_data_reg[30]'
INFO: [Synth 8-3886] merging instance 'i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_bar_controller/i_bar0/drd_req_ctx_reg[31]' (FD) to 'i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_bar_controller/i_bar1/rd_req_addr_1_reg[31]'
INFO: [Synth 8-3886] merging instance 'i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_bar_controller/i_bar0/rd_rsp_ctx_reg[31]' (FD) to 'i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_bar_controller/i_bar1/rd_rsp_data_reg[31]'
INFO: [Synth 8-3886] merging instance 'i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_bar_controller/i_bar0/drd_req_ctx_reg[16]' (FD) to 'i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_bar_controller/i_bar1/rd_req_addr_1_reg[16]'
INFO: [Synth 8-3886] merging instance 'i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_bar_controller/i_bar0/rd_rsp_ctx_reg[16]' (FD) to 'i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_bar_controller/i_bar1/rd_rsp_data_reg[16]'
INFO: [Synth 8-3886] merging instance 'i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_bar_controller/i_bar0/drd_req_ctx_reg[17]' (FD) to 'i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_bar_controller/i_bar1/rd_req_addr_1_reg[17]'
INFO: [Synth 8-3886] merging instance 'i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_bar_controller/i_bar0/rd_rsp_ctx_reg[17]' (FD) to 'i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_bar_controller/i_bar1/rd_rsp_data_reg[17]'
INFO: [Synth 8-3886] merging instance 'i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_bar_controller/i_bar0/drd_req_ctx_reg[18]' (FD) to 'i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_bar_controller/i_bar1/rd_req_addr_1_reg[18]'
INFO: [Synth 8-3886] merging instance 'i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_bar_controller/i_bar0/rd_rsp_ctx_reg[18]' (FD) to 'i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_bar_controller/i_bar1/rd_rsp_data_reg[18]'
INFO: [Synth 8-3886] merging instance 'i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_bar_controller/i_bar0/drd_req_ctx_reg[19]' (FD) to 'i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_bar_controller/i_bar1/rd_req_addr_1_reg[19]'
INFO: [Synth 8-3886] merging instance 'i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_bar_controller/i_bar0/rd_rsp_ctx_reg[19]' (FD) to 'i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_bar_controller/i_bar1/rd_rsp_data_reg[19]'
INFO: [Synth 8-3886] merging instance 'i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_bar_controller/i_bar0/drd_req_ctx_reg[20]' (FD) to 'i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_bar_controller/i_bar1/rd_req_addr_1_reg[20]'
INFO: [Synth 8-3886] merging instance 'i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_bar_controller/i_bar0/rd_rsp_ctx_reg[20]' (FD) to 'i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_bar_controller/i_bar1/rd_rsp_data_reg[20]'
INFO: [Synth 8-3886] merging instance 'i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_bar_controller/i_bar0/drd_req_ctx_reg[21]' (FD) to 'i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_bar_controller/i_bar1/rd_req_addr_1_reg[21]'
INFO: [Synth 8-3886] merging instance 'i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_bar_controller/i_bar0/rd_rsp_ctx_reg[21]' (FD) to 'i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_bar_controller/i_bar1/rd_rsp_data_reg[21]'
INFO: [Synth 8-3886] merging instance 'i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_bar_controller/i_bar0/drd_req_ctx_reg[22]' (FD) to 'i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_bar_controller/i_bar1/rd_req_addr_1_reg[22]'
INFO: [Synth 8-3886] merging instance 'i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_bar_controller/i_bar0/rd_rsp_ctx_reg[22]' (FD) to 'i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_bar_controller/i_bar1/rd_rsp_data_reg[22]'
INFO: [Synth 8-3886] merging instance 'i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_bar_controller/i_bar0/drd_req_ctx_reg[23]' (FD) to 'i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_bar_controller/i_bar1/rd_req_addr_1_reg[23]'
INFO: [Synth 8-3886] merging instance 'i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_bar_controller/i_bar0/rd_rsp_ctx_reg[23]' (FD) to 'i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_bar_controller/i_bar1/rd_rsp_data_reg[23]'
INFO: [Synth 8-3886] merging instance 'i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_bar_controller/i_bar0/drd_req_ctx_reg[12]' (FD) to 'i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_bar_controller/i_bar1/rd_req_addr_1_reg[12]'
INFO: [Synth 8-3886] merging instance 'i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_bar_controller/i_bar0/rd_rsp_ctx_reg[12]' (FD) to 'i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_bar_controller/i_bar1/rd_rsp_data_reg[12]'
INFO: [Synth 8-3886] merging instance 'i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_bar_controller/i_bar0/drd_req_ctx_reg[13]' (FD) to 'i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_bar_controller/i_bar1/rd_req_addr_1_reg[13]'
INFO: [Synth 8-3886] merging instance 'i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_bar_controller/i_bar0/rd_rsp_ctx_reg[13]' (FD) to 'i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_bar_controller/i_bar1/rd_rsp_data_reg[13]'
INFO: [Synth 8-3886] merging instance 'i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_bar_controller/i_bar0/drd_req_ctx_reg[14]' (FD) to 'i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_bar_controller/i_bar1/rd_req_addr_1_reg[14]'
INFO: [Synth 8-3886] merging instance 'i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_bar_controller/i_bar0/rd_rsp_ctx_reg[14]' (FD) to 'i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_bar_controller/i_bar1/rd_rsp_data_reg[14]'
INFO: [Synth 8-3886] merging instance 'i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_bar_controller/i_bar0/drd_req_ctx_reg[15]' (FD) to 'i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_bar_controller/i_bar1/rd_req_addr_1_reg[15]'
INFO: [Synth 8-3886] merging instance 'i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_bar_controller/i_bar0/rd_rsp_ctx_reg[15]' (FD) to 'i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_bar_controller/i_bar1/rd_rsp_data_reg[15]'
INFO: [Synth 8-3886] merging instance 'i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_bar_controller/i_bar1/rd_req_addr_1_reg[0]' (FD) to 'i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_bar_controller/i_bar1/rd_req_addr_1_reg[1]'
INFO: [Synth 8-3886] merging instance 'i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_bar_controller/i_bar1/rd_rsp_data_reg[0]' (FD) to 'i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_bar_controller/i_bar1/rd_rsp_data_reg[1]'
INFO: [Synth 8-3333] propagating constant 0 across sequential element (\i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_bar_controller/i_bar1/rd_req_addr_1_reg[1] )
INFO: [Synth 8-3886] merging instance 'i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_bar_controller/i_bar1/rd_rsp_data_reg[1]' (FD) to 'i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_bar_controller/i_bar1/rd_req_addr_1_reg[1]'
INFO: [Synth 8-3333] propagating constant 1 across sequential element (\i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_filter/tkeepdw_reg[0] )
INFO: [Synth 8-3886] merging instance 'i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_bar_controller/i_pcileech_tlps128_bar_rdengine/rd2_out_data_reg[74]' (FDE) to 'i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_bar_controller/i_pcileech_tlps128_bar_rdengine/rd2_out_data_reg[75]'
INFO: [Synth 8-3886] merging instance 'i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_bar_controller/i_pcileech_tlps128_bar_rdengine/rd3_process_data_reg[74]' (FDE) to 'i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_bar_controller/i_pcileech_tlps128_bar_rdengine/rd3_process_data_reg[75]'
INFO: [Synth 8-3886] merging instance 'i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_bar_controller/i_bar0/drd_req_ctx_reg[74]' (FD) to 'i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_bar_controller/i_bar0/drd_req_ctx_reg[75]'
INFO: [Synth 8-3886] merging instance 'i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_bar_controller/i_bar0/rd_rsp_ctx_reg[74]' (FD) to 'i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_bar_controller/i_bar0/rd_rsp_ctx_reg[75]'
INFO: [Synth 8-3333] propagating constant 0 across sequential element (\i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_bar_controller/i_pcileech_tlps128_bar_rdengine/rd2_out_data_reg[75] )
INFO: [Synth 8-3333] propagating constant 0 across sequential element (\i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_bar_controller/i_bar1/rd_req_addr_1_reg[1] )
INFO: [Synth 8-3333] propagating constant 0 across sequential element (\i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_bar_controller/i_pcileech_tlps128_bar_rdengine/rd3_process_data_reg[75] )
INFO: [Synth 8-3886] merging instance 'i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_bar_controller/i_bar1/rd_req_addr_1_reg[1]' (FD) to 'i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_bar_controller/i_bar0/drd_req_ctx_reg[75]'
INFO: [Synth 8-3333] propagating constant 0 across sequential element (\i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_bar_controller/i_bar0/drd_req_ctx_reg[75] )
INFO: [Synth 8-3886] merging instance 'i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_bar_controller/i_bar0/rd_rsp_ctx_reg[75]' (FD) to 'i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_bar_controller/i_bar0/drd_req_ctx_reg[75]'
INFO: [Synth 8-3333] propagating constant 0 across sequential element (\i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_bar_controller/i_bar0/drd_req_ctx_reg[75] )
INFO: [Synth 8-3886] merging instance 'i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_bar_controller/i_pcileech_tlps128_bar_rdengine/rd2_total_dwlen_reg[5]' (FDE) to 'i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_bar_controller/i_pcileech_tlps128_bar_rdengine/rd2_pkt2_reg[81]'
INFO: [Synth 8-3886] merging instance 'i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_bar_controller/i_pcileech_tlps128_bar_rdengine/rd2_total_dwlen_reg[0]' (FDE) to 'i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_bar_controller/i_pcileech_tlps128_bar_rdengine/rd2_pkt2_reg[76]'
INFO: [Synth 8-3886] merging instance 'i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_bar_controller/i_pcileech_tlps128_bar_rdengine/rd2_total_dwlen_reg[4]' (FDE) to 'i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_bar_controller/i_pcileech_tlps128_bar_rdengine/rd2_pkt2_reg[80]'
INFO: [Synth 8-3886] merging instance 'i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_bar_controller/i_pcileech_tlps128_bar_rdengine/rd2_total_dwlen_reg[3]' (FDE) to 'i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_bar_controller/i_pcileech_tlps128_bar_rdengine/rd2_pkt2_reg[79]'
INFO: [Synth 8-3886] merging instance 'i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_bar_controller/i_pcileech_tlps128_bar_rdengine/rd2_total_dwlen_reg[2]' (FDE) to 'i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_bar_controller/i_pcileech_tlps128_bar_rdengine/rd2_pkt2_reg[78]'
INFO: [Synth 8-3886] merging instance 'i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_bar_controller/i_pcileech_tlps128_bar_rdengine/rd2_total_dwlen_reg[1]' (FDE) to 'i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_bar_controller/i_pcileech_tlps128_bar_rdengine/rd2_pkt2_reg[77]'
INFO: [Synth 8-3886] merging instance 'i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_bar_controller/i_pcileech_tlps128_bar_rdengine/rd2_total_dwlen_reg[6]' (FDE) to 'i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_bar_controller/i_pcileech_tlps128_bar_rdengine/rd2_pkt2_reg[82]'
INFO: [Synth 8-3886] merging instance 'i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_bar_controller/i_pcileech_tlps128_bar_rdengine/rd2_total_dwlen_reg[7]' (FDE) to 'i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_bar_controller/i_pcileech_tlps128_bar_rdengine/rd2_pkt2_reg[83]'
INFO: [Synth 8-3886] merging instance 'i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_bar_controller/i_pcileech_tlps128_bar_rdengine/rd2_total_dwlen_reg[8]' (FDE) to 'i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_bar_controller/i_pcileech_tlps128_bar_rdengine/rd2_pkt2_reg[84]'
INFO: [Synth 8-3886] merging instance 'i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_bar_controller/i_pcileech_tlps128_bar_rdengine/rd2_total_dwlen_reg[9]' (FDE) to 'i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_bar_controller/i_pcileech_tlps128_bar_rdengine/rd2_pkt2_reg[85]'
INFO: [Synth 8-3886] merging instance 'i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_bar_controller/i_pcileech_tlps128_bar_rdengine/rd2_pkt2_reg[69]' (FDE) to 'i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_bar_controller/i_pcileech_tlps128_bar_rdengine/rd2_pkt2_reg[70]'
INFO: [Synth 8-3886] merging instance 'i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_bar_controller/i_pcileech_tlps128_bar_rdengine/rd2_pkt2_reg[70]' (FDE) to 'i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_bar_controller/i_pcileech_tlps128_bar_rdengine/rd2_pkt2_reg[71]'
INFO: [Synth 8-3886] merging instance 'i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_bar_controller/i_pcileech_tlps128_bar_rdengine/rd2_pkt2_reg[71]' (FDE) to 'i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_bar_controller/i_pcileech_tlps128_bar_rdengine/rd2_pkt2_reg[72]'
INFO: [Synth 8-3333] propagating constant 0 across sequential element (\i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_bar_controller/i_pcileech_tlps128_bar_rdengine/rd2_pkt2_reg[72] )
INFO: [Synth 8-3333] propagating constant 0 across sequential element (\i_pcileech_tlps128_src64/len_reg[3] )
INFO: [Synth 8-3886] merging instance 'i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_bar_controller/i_pcileech_tlps128_bar_rdengine/rd2_out_data_reg[69]' (FDE) to 'i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_bar_controller/i_pcileech_tlps128_bar_rdengine/rd2_out_data_reg[70]'
INFO: [Synth 8-3886] merging instance 'i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_bar_controller/i_pcileech_tlps128_bar_rdengine/rd3_process_data_reg[69]' (FDE) to 'i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_bar_controller/i_pcileech_tlps128_bar_rdengine/rd3_process_data_reg[70]'
INFO: [Synth 8-3886] merging instance 'i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_bar_controller/i_bar0/drd_req_ctx_reg[69]' (FD) to 'i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_bar_controller/i_bar0/drd_req_ctx_reg[70]'
INFO: [Synth 8-3886] merging instance 'i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_bar_controller/i_bar0/rd_rsp_ctx_reg[69]' (FD) to 'i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_bar_controller/i_bar0/rd_rsp_ctx_reg[70]'
INFO: [Synth 8-3886] merging instance 'i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_bar_controller/i_pcileech_tlps128_bar_rdengine/rd2_out_data_reg[70]' (FDE) to 'i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_bar_controller/i_pcileech_tlps128_bar_rdengine/rd2_out_data_reg[71]'
INFO: [Synth 8-3886] merging instance 'i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_bar_controller/i_pcileech_tlps128_bar_rdengine/rd3_process_data_reg[70]' (FDE) to 'i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_bar_controller/i_pcileech_tlps128_bar_rdengine/rd3_process_data_reg[71]'
INFO: [Synth 8-3886] merging instance 'i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_bar_controller/i_bar0/drd_req_ctx_reg[70]' (FD) to 'i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_bar_controller/i_bar0/drd_req_ctx_reg[71]'
INFO: [Synth 8-3886] merging instance 'i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_bar_controller/i_bar0/rd_rsp_ctx_reg[70]' (FD) to 'i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_bar_controller/i_bar0/rd_rsp_ctx_reg[71]'
INFO: [Synth 8-3886] merging instance 'i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_bar_controller/i_pcileech_tlps128_bar_rdengine/rd2_out_data_reg[71]' (FDE) to 'i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_bar_controller/i_pcileech_tlps128_bar_rdengine/rd2_out_data_reg[72]'
INFO: [Synth 8-3886] merging instance 'i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_bar_controller/i_pcileech_tlps128_bar_rdengine/rd3_process_data_reg[71]' (FDE) to 'i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_bar_controller/i_pcileech_tlps128_bar_rdengine/rd3_process_data_reg[72]'
INFO: [Synth 8-3886] merging instance 'i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_bar_controller/i_bar0/drd_req_ctx_reg[71]' (FD) to 'i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_bar_controller/i_bar0/drd_req_ctx_reg[72]'
INFO: [Synth 8-3886] merging instance 'i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_bar_controller/i_bar0/rd_rsp_ctx_reg[71]' (FD) to 'i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_bar_controller/i_bar0/rd_rsp_ctx_reg[72]'
INFO: [Synth 8-3333] propagating constant 0 across sequential element (\i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_bar_controller/i_pcileech_tlps128_bar_rdengine/rd2_out_data_reg[72] )
INFO: [Synth 8-3333] propagating constant 0 across sequential element (\i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_bar_controller/i_pcileech_tlps128_bar_rdengine/rd3_process_data_reg[72] )
INFO: [Synth 8-3333] propagating constant 0 across sequential element (\i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_bar_controller/i_bar0/drd_req_ctx_reg[72] )
INFO: [Synth 8-3333] propagating constant 0 across sequential element (\i_pcileech_pcie_tlp_a7/i_pcileech_tlps128_bar_controller/i_bar0/rd_rsp_ctx_reg[72] )
---------------------------------------------------------------------------------
Finished Cross Boundary and Area Optimization : Time (s): cpu = 00:00:26 ; elapsed = 00:01:09 . Memory (MB): peak = 1598.906 ; gain = 744.430
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Start Applying XDC Timing Constraints
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Finished Applying XDC Timing Constraints : Time (s): cpu = 00:00:30 ; elapsed = 00:01:14 . Memory (MB): peak = 1743.273 ; gain = 888.797
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Start Timing Optimization
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Finished Timing Optimization : Time (s): cpu = 00:00:32 ; elapsed = 00:01:17 . Memory (MB): peak = 1794.918 ; gain = 940.441
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Start Technology Mapping
---------------------------------------------------------------------------------
WARNING: [Synth 8-7080] Parallel synthesis criteria is not met
---------------------------------------------------------------------------------
Finished Technology Mapping : Time (s): cpu = 00:00:34 ; elapsed = 00:01:21 . Memory (MB): peak = 1865.438 ; gain = 1010.961
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Start IO Insertion
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Start Flattening Before IO Insertion
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Finished Flattening Before IO Insertion
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Start Final Netlist Cleanup
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Finished Final Netlist Cleanup
---------------------------------------------------------------------------------
WARNING: [Synth 8-3295] tying undriven pin i_8917:a to constant 0
WARNING: [Synth 8-3295] tying undriven pin i_8918:a to constant 0
WARNING: [Synth 8-3295] tying undriven pin i_8919:a to constant 0
WARNING: [Synth 8-3295] tying undriven pin i_8920:a to constant 0
WARNING: [Synth 8-3295] tying undriven pin i_8921:a to constant 0
WARNING: [Synth 8-3295] tying undriven pin i_8922:a to constant 0
WARNING: [Synth 8-3295] tying undriven pin i_8923:a to constant 0
WARNING: [Synth 8-3295] tying undriven pin i_8924:a to constant 0
WARNING: [Synth 8-3295] tying undriven pin i_8925:a to constant 0
WARNING: [Synth 8-3295] tying undriven pin i_8926:a to constant 0
WARNING: [Synth 8-3295] tying undriven pin i_8927:a to constant 0
WARNING: [Synth 8-3295] tying undriven pin i_8928:a to constant 0
WARNING: [Synth 8-3295] tying undriven pin i_8929:a to constant 0
WARNING: [Synth 8-3295] tying undriven pin i_8930:a to constant 0
WARNING: [Synth 8-3295] tying undriven pin i_8931:a to constant 0
WARNING: [Synth 8-3295] tying undriven pin i_8932:a to constant 0
WARNING: [Synth 8-3295] tying undriven pin i_8933:a to constant 0
WARNING: [Synth 8-3295] tying undriven pin i_8934:a to constant 0
WARNING: [Synth 8-3295] tying undriven pin i_8935:a to constant 0
WARNING: [Synth 8-3295] tying undriven pin i_8936:a to constant 0
WARNING: [Synth 8-3295] tying undriven pin i_8937:a to constant 0
WARNING: [Synth 8-3295] tying undriven pin i_8938:a to constant 0
WARNING: [Synth 8-3295] tying undriven pin i_8939:a to constant 0
WARNING: [Synth 8-3295] tying undriven pin i_8940:a to constant 0
WARNING: [Synth 8-3295] tying undriven pin i_8941:a to constant 0
WARNING: [Synth 8-3295] tying undriven pin i_8942:a to constant 0
WARNING: [Synth 8-3295] tying undriven pin i_8943:a to constant 0
WARNING: [Synth 8-3295] tying undriven pin i_8944:a to constant 0
WARNING: [Synth 8-3295] tying undriven pin i_8945:a to constant 0
WARNING: [Synth 8-3295] tying undriven pin i_8946:a to constant 0
WARNING: [Synth 8-3295] tying undriven pin i_8947:a to constant 0
WARNING: [Synth 8-3295] tying undriven pin i_8948:a to constant 0
---------------------------------------------------------------------------------
Finished IO Insertion : Time (s): cpu = 00:00:36 ; elapsed = 00:01:24 . Memory (MB): peak = 1958.684 ; gain = 1104.207
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Start Renaming Generated Instances
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Finished Renaming Generated Instances : Time (s): cpu = 00:00:36 ; elapsed = 00:01:24 . Memory (MB): peak = 1958.684 ; gain = 1104.207
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Start Rebuilding User Hierarchy
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Finished Rebuilding User Hierarchy : Time (s): cpu = 00:00:36 ; elapsed = 00:01:25 . Memory (MB): peak = 1958.684 ; gain = 1104.207
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Start Renaming Generated Ports
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Finished Renaming Generated Ports : Time (s): cpu = 00:00:36 ; elapsed = 00:01:25 . Memory (MB): peak = 1958.684 ; gain = 1104.207
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Start Handling Custom Attributes
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Finished Handling Custom Attributes : Time (s): cpu = 00:00:37 ; elapsed = 00:01:26 . Memory (MB): peak = 1958.684 ; gain = 1104.207
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Start Renaming Generated Nets
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Finished Renaming Generated Nets : Time (s): cpu = 00:00:37 ; elapsed = 00:01:26 . Memory (MB): peak = 1958.684 ; gain = 1104.207
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Start Writing Synthesis Report
---------------------------------------------------------------------------------

Report BlackBoxes: 
+------+-----------------------------+----------+
|      |BlackBox name                |Instances |
+------+-----------------------------+----------+
|1     |fifo_64_64_clk2_comrx        |         1|
|2     |fifo_32_32_clk1_comtx        |         1|
|3     |fifo_256_32_clk2_comtx       |         1|
|4     |fifo_64_64_clk1_fifocmd      |         1|
|5     |fifo_34_34                   |         2|
|6     |pcie_7x_0                    |         1|
|7     |fifo_64_64                   |         1|
|8     |fifo_32_32_clk2              |         1|
|9     |bram_bar_zero4k              |         1|
|10    |fifo_74_74_clk1_bar_rd1      |         1|
|11    |fifo_134_134_clk1_bar_rdrsp  |         1|
|12    |fifo_141_141_clk1_bar_wr     |         1|
|13    |fifo_49_49_clk2              |         1|
|14    |fifo_43_43_clk2              |         1|
|15    |fifo_129_129_clk1            |         1|
|16    |bram_pcie_cfgspace           |         1|
|17    |drom_pcie_cfgspace_writemask |         1|
|18    |fifo_134_134_clk2            |         1|
|19    |fifo_1_1_clk2                |         1|
|20    |fifo_134_134_clk2_rxfifo     |         1|
+------+-----------------------------+----------+

Report Cell Usage: 
+------+-----------------------------+------+
|      |Cell                         |Count |
+------+-----------------------------+------+
|1     |bram_bar_zero4k              |     1|
|2     |bram_pcie_cfgspace           |     1|
|3     |drom_pcie_cfgspace_writemask |     1|
|4     |fifo_129_129_clk1            |     1|
|5     |fifo_134_134_clk1_bar_rdrsp  |     1|
|6     |fifo_134_134_clk2            |     1|
|7     |fifo_134_134_clk2_rxfifo     |     1|
|8     |fifo_141_141_clk1_bar_wr     |     1|
|9     |fifo_1_1_clk2                |     1|
|10    |fifo_256_32_clk2_comtx       |     1|
|11    |fifo_32_32_clk1_comtx        |     1|
|12    |fifo_32_32_clk2              |     1|
|13    |fifo_34                      |     1|
|14    |fifo_34_34                   |     1|
|15    |fifo_43_43_clk2              |     1|
|16    |fifo_49_49_clk2              |     1|
|17    |fifo_64                      |     1|
|18    |fifo_64_64_clk1_fifocmd      |     1|
|19    |fifo_64_64_clk2_comrx        |     1|
|20    |fifo_74_74_clk1_bar_rd1      |     1|
|21    |pcie_7x                      |     1|
|22    |BUFG                         |     2|
|23    |CARRY4                       |   301|
|24    |IBUFDS_GTE2                  |     1|
|25    |LUT1                         |    92|
|26    |LUT2                         |  1272|
|27    |LUT3                         |   948|
|28    |LUT4                         |   807|
|29    |LUT5                         |  2277|
|30    |LUT6                         |  4195|
|31    |MUXF7                        |   124|
|32    |MUXF8                        |    34|
|33    |STARTUPE2                    |     1|
|34    |FDRE                         |  3365|
|35    |FDSE                         |   291|
|36    |IBUF                         |    12|
|37    |OBUF                         |    14|
|38    |OBUFT                        |    32|
+------+-----------------------------+------+
---------------------------------------------------------------------------------
Finished Writing Synthesis Report : Time (s): cpu = 00:00:37 ; elapsed = 00:01:26 . Memory (MB): peak = 1958.684 ; gain = 1104.207
---------------------------------------------------------------------------------
Synthesis finished with 0 errors, 0 critical warnings and 110 warnings.
Synthesis Optimization Runtime : Time (s): cpu = 00:00:31 ; elapsed = 00:01:22 . Memory (MB): peak = 1958.684 ; gain = 1044.945
Synthesis Optimization Complete : Time (s): cpu = 00:00:37 ; elapsed = 00:01:26 . Memory (MB): peak = 1958.684 ; gain = 1104.207
INFO: [Project 1-571] Translating synthesized netlist
Netlist sorting complete. Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.121 . Memory (MB): peak = 1958.684 ; gain = 0.000
INFO: [Netlist 29-17] Analyzing 460 Unisim elements for replacement
INFO: [Netlist 29-28] Unisim Transformation completed in 0 CPU seconds
INFO: [Project 1-570] Preparing netlist for logic optimization
INFO: [Opt 31-138] Pushed 0 inverter(s) to 0 load pin(s).
Netlist sorting complete. Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.001 . Memory (MB): peak = 1977.910 ; gain = 0.000
INFO: [Project 1-111] Unisim Transformation Summary:
No Unisim elements were transformed.

Synth Design complete | Checksum: 73186e5
INFO: [Common 17-83] Releasing license: Synthesis
242 Infos, 160 Warnings, 0 Critical Warnings and 0 Errors encountered.
synth_design completed successfully
synth_design: Time (s): cpu = 00:00:40 ; elapsed = 00:01:32 . Memory (MB): peak = 1977.910 ; gain = 1341.996
Write ShapeDB Complete: Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.013 . Memory (MB): peak = 1977.910 ; gain = 0.000
INFO: [Common 17-1381] The checkpoint 'G:/FPGA/20250327/100t20250618/100t484-1/pcileech_100t484_x1/pcileech_100t484_x1.runs/synth_1/pcileech_100t484_x1_top.dcp' has been generated.
INFO: [Vivado 12-24828] Executing command : report_utilization -file pcileech_100t484_x1_top_utilization_synth.rpt -pb pcileech_100t484_x1_top_utilization_synth.pb
INFO: [Common 17-206] Exiting Vivado at Wed Jun 18 17:36:30 2025...
