Copyright 1986-2022 Xilinx, Inc. All Rights Reserved. Copyright 2022-2025 Advanced Micro Devices, Inc. All Rights Reserved.
---------------------------------------------------------------------------------------------------------------------------------------------
| Tool Version : Vivado v.2024.2.2 (win64) Build 6060944 Thu Mar 06 19:10:01 MST 2025
| Date         : Wed Jun 18 17:36:30 2025
| Host         : DESKTOP-7T6N58Q running 64-bit major release  (build 9200)
| Command      : report_utilization -file pcileech_100t484_x1_top_utilization_synth.rpt -pb pcileech_100t484_x1_top_utilization_synth.pb
| Design       : pcileech_100t484_x1_top
| Device       : xc7a100tfgg484-2
| Speed File   : -2
| Design State : Synthesized
---------------------------------------------------------------------------------------------------------------------------------------------

Utilization Design Information

Table of Contents
-----------------
1. Slice Logic
1.1 Summary of Registers by Type
2. Memory
3. DSP
4. IO and GT Specific
5. Clocking
6. Specific Feature
7. Primitives
8. Black Boxes
9. Instantiated Netlists

1. Slice Logic
--------------

+-------------------------+------+-------+------------+-----------+-------+
|        Site Type        | Used | Fixed | Prohibited | Available | Util% |
+-------------------------+------+-------+------------+-----------+-------+
| Slice LUTs*             | 8429 |     0 |          0 |     63400 | 13.29 |
|   LUT as Logic          | 8429 |     0 |          0 |     63400 | 13.29 |
|   LUT as Memory         |    0 |     0 |          0 |     19000 |  0.00 |
| Slice Registers         | 3656 |     0 |          0 |    126800 |  2.88 |
|   Register as Flip Flop | 3656 |     0 |          0 |    126800 |  2.88 |
|   Register as Latch     |    0 |     0 |          0 |    126800 |  0.00 |
| F7 Muxes                |  124 |     0 |          0 |     31700 |  0.39 |
| F8 Muxes                |   34 |     0 |          0 |     15850 |  0.21 |
+-------------------------+------+-------+------------+-----------+-------+
* Warning! The Final LUT count, after physical optimizations and full implementation, is typically lower. Run opt_design after synthesis, if not already completed, for a more realistic count.
Warning! LUT value is adjusted to account for LUT combining.
Warning! For any ECO changes, please run place_design if there are unplaced instances


1.1 Summary of Registers by Type
--------------------------------

+-------+--------------+-------------+--------------+
| Total | Clock Enable | Synchronous | Asynchronous |
+-------+--------------+-------------+--------------+
| 0     |            _ |           - |            - |
| 0     |            _ |           - |          Set |
| 0     |            _ |           - |        Reset |
| 0     |            _ |         Set |            - |
| 0     |            _ |       Reset |            - |
| 0     |          Yes |           - |            - |
| 0     |          Yes |           - |          Set |
| 0     |          Yes |           - |        Reset |
| 291   |          Yes |         Set |            - |
| 3365  |          Yes |       Reset |            - |
+-------+--------------+-------------+--------------+


2. Memory
---------

+----------------+------+-------+------------+-----------+-------+
|    Site Type   | Used | Fixed | Prohibited | Available | Util% |
+----------------+------+-------+------------+-----------+-------+
| Block RAM Tile |    0 |     0 |          0 |       135 |  0.00 |
|   RAMB36/FIFO* |    0 |     0 |          0 |       135 |  0.00 |
|   RAMB18       |    0 |     0 |          0 |       270 |  0.00 |
+----------------+------+-------+------------+-----------+-------+
* Note: Each Block RAM Tile only has one FIFO logic available and therefore can accommodate only one FIFO36E1 or one FIFO18E1. However, if a FIFO18E1 occupies a Block RAM Tile, that tile can still accommodate a RAMB18E1


3. DSP
------

+-----------+------+-------+------------+-----------+-------+
| Site Type | Used | Fixed | Prohibited | Available | Util% |
+-----------+------+-------+------------+-----------+-------+
| DSPs      |    0 |     0 |          0 |       240 |  0.00 |
+-----------+------+-------+------------+-----------+-------+


4. IO and GT Specific
---------------------

+-----------------------------+------+-------+------------+-----------+-------+
|          Site Type          | Used | Fixed | Prohibited | Available | Util% |
+-----------------------------+------+-------+------------+-----------+-------+
| Bonded IOB                  |   56 |     0 |          0 |       285 | 19.65 |
| Bonded IPADs                |    2 |     0 |          0 |        14 | 14.29 |
| Bonded OPADs                |    0 |     0 |          0 |         8 |  0.00 |
| PHY_CONTROL                 |    0 |     0 |          0 |         6 |  0.00 |
| PHASER_REF                  |    0 |     0 |          0 |         6 |  0.00 |
| OUT_FIFO                    |    0 |     0 |          0 |        24 |  0.00 |
| IN_FIFO                     |    0 |     0 |          0 |        24 |  0.00 |
| IDELAYCTRL                  |    0 |     0 |          0 |         6 |  0.00 |
| IBUFDS                      |    0 |     0 |          0 |       274 |  0.00 |
| GTPE2_CHANNEL               |    0 |     0 |          0 |         4 |  0.00 |
| PHASER_OUT/PHASER_OUT_PHY   |    0 |     0 |          0 |        24 |  0.00 |
| PHASER_IN/PHASER_IN_PHY     |    0 |     0 |          0 |        24 |  0.00 |
| IDELAYE2/IDELAYE2_FINEDELAY |    0 |     0 |          0 |       300 |  0.00 |
| IBUFDS_GTE2                 |    1 |     0 |          0 |         2 | 50.00 |
| ILOGIC                      |    0 |     0 |          0 |       285 |  0.00 |
| OLOGIC                      |    0 |     0 |          0 |       285 |  0.00 |
+-----------------------------+------+-------+------------+-----------+-------+


5. Clocking
-----------

+------------+------+-------+------------+-----------+-------+
|  Site Type | Used | Fixed | Prohibited | Available | Util% |
+------------+------+-------+------------+-----------+-------+
| BUFGCTRL   |    2 |     0 |          0 |        32 |  6.25 |
| BUFIO      |    0 |     0 |          0 |        24 |  0.00 |
| MMCME2_ADV |    0 |     0 |          0 |         6 |  0.00 |
| PLLE2_ADV  |    0 |     0 |          0 |         6 |  0.00 |
| BUFMRCE    |    0 |     0 |          0 |        12 |  0.00 |
| BUFHCE     |    0 |     0 |          0 |        96 |  0.00 |
| BUFR       |    0 |     0 |          0 |        24 |  0.00 |
+------------+------+-------+------------+-----------+-------+


6. Specific Feature
-------------------

+-------------+------+-------+------------+-----------+--------+
|  Site Type  | Used | Fixed | Prohibited | Available |  Util% |
+-------------+------+-------+------------+-----------+--------+
| BSCANE2     |    0 |     0 |          0 |         4 |   0.00 |
| CAPTUREE2   |    0 |     0 |          0 |         1 |   0.00 |
| DNA_PORT    |    0 |     0 |          0 |         1 |   0.00 |
| EFUSE_USR   |    0 |     0 |          0 |         1 |   0.00 |
| FRAME_ECCE2 |    0 |     0 |          0 |         1 |   0.00 |
| ICAPE2      |    0 |     0 |          0 |         2 |   0.00 |
| PCIE_2_1    |    0 |     0 |          0 |         1 |   0.00 |
| STARTUPE2   |    1 |     0 |          0 |         1 | 100.00 |
| XADC        |    0 |     0 |          0 |         1 |   0.00 |
+-------------+------+-------+------------+-----------+--------+


7. Primitives
-------------

+-------------+------+---------------------+
|   Ref Name  | Used | Functional Category |
+-------------+------+---------------------+
| LUT6        | 4195 |                 LUT |
| FDRE        | 3365 |        Flop & Latch |
| LUT5        | 2277 |                 LUT |
| LUT2        | 1272 |                 LUT |
| LUT3        |  948 |                 LUT |
| LUT4        |  807 |                 LUT |
| CARRY4      |  301 |          CarryLogic |
| FDSE        |  291 |        Flop & Latch |
| MUXF7       |  124 |               MuxFx |
| LUT1        |   92 |                 LUT |
| MUXF8       |   34 |               MuxFx |
| OBUFT       |   32 |                  IO |
| OBUF        |   14 |                  IO |
| IBUF        |   12 |                  IO |
| BUFG        |    2 |               Clock |
| STARTUPE2   |    1 |              Others |
| IBUFDS_GTE2 |    1 |                  IO |
+-------------+------+---------------------+


8. Black Boxes
--------------

+------------------------------+------+
|           Ref Name           | Used |
+------------------------------+------+
| fifo_34_34                   |    2 |
| pcie_7x_0                    |    1 |
| fifo_74_74_clk1_bar_rd1      |    1 |
| fifo_64_64_clk2_comrx        |    1 |
| fifo_64_64_clk1_fifocmd      |    1 |
| fifo_64_64                   |    1 |
| fifo_49_49_clk2              |    1 |
| fifo_43_43_clk2              |    1 |
| fifo_32_32_clk2              |    1 |
| fifo_32_32_clk1_comtx        |    1 |
| fifo_256_32_clk2_comtx       |    1 |
| fifo_1_1_clk2                |    1 |
| fifo_141_141_clk1_bar_wr     |    1 |
| fifo_134_134_clk2_rxfifo     |    1 |
| fifo_134_134_clk2            |    1 |
| fifo_134_134_clk1_bar_rdrsp  |    1 |
| fifo_129_129_clk1            |    1 |
| drom_pcie_cfgspace_writemask |    1 |
| bram_pcie_cfgspace           |    1 |
| bram_bar_zero4k              |    1 |
+------------------------------+------+


9. Instantiated Netlists
------------------------

+----------+------+
| Ref Name | Used |
+----------+------+


