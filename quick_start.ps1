# FT601优化快速开始脚本
# 一键应用所有优化并运行基础测试

param(
    [Parameter(Mandatory=$false)]
    [switch]$SkipFPGA = $false,
    
    [Parameter(Mandatory=$false)]
    [switch]$SkipHost = $false,
    
    [Parameter(Mandatory=$false)]
    [switch]$SkipTest = $false
)

$ErrorActionPreference = "Stop"

Write-Host "=== FT601优化快速开始 ===" -ForegroundColor Green

# 设置项目路径
$ProjectPath = "G:\FPGA\20250327\100t20250618\100t484-1"
Set-Location $ProjectPath

Write-Host "项目路径: $ProjectPath" -ForegroundColor Yellow

# 第一步：应用优化
Write-Host "`n[1/4] 应用优化方案..." -ForegroundColor Cyan

$deployParams = @()
if ($SkipFPGA) { $deployParams += "-OnlyHost" }
if ($SkipHost) { $deployParams += "-OnlyFPGA" }

try {
    & ".\deploy_ft601_optimization.ps1" @deployParams
    Write-Host "  优化方案应用完成" -ForegroundColor Green
} catch {
    Write-Error "  优化方案应用失败: $($_.Exception.Message)"
}

# 第二步：编译项目
if (-not $SkipHost) {
    Write-Host "`n[2/4] 编译上位机代码..." -ForegroundColor Cyan
    
    try {
        $msbuildPath = "${env:ProgramFiles}\Microsoft Visual Studio\2022\Professional\MSBuild\Current\Bin\MSBuild.exe"
        if (-not (Test-Path $msbuildPath)) {
            $msbuildPath = "${env:ProgramFiles}\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\MSBuild.exe"
        }
        
        if (Test-Path $msbuildPath) {
            & $msbuildPath "pcileech-master\pcileech.sln" /p:Configuration=Release /p:Platform=x64 /verbosity:minimal
            
            if ($LASTEXITCODE -eq 0) {
                Write-Host "  编译成功" -ForegroundColor Green
            } else {
                Write-Warning "  编译可能存在问题，请检查输出"
            }
        } else {
            Write-Warning "  未找到MSBuild，请手动编译"
        }
    } catch {
        Write-Warning "  编译过程出错: $($_.Exception.Message)"
    }
} else {
    Write-Host "`n[2/4] 跳过上位机编译" -ForegroundColor Gray
}

# 第三步：FPGA综合提示
if (-not $SkipFPGA) {
    Write-Host "`n[3/4] FPGA综合指导..." -ForegroundColor Cyan
    Write-Host "  请在Vivado中执行以下步骤:" -ForegroundColor Yellow
    Write-Host "  1. 打开项目: vivado_generate_project_captaindma_100t.tcl" -ForegroundColor White
    Write-Host "  2. 运行综合 (Run Synthesis)" -ForegroundColor White
    Write-Host "  3. 运行实现 (Run Implementation)" -ForegroundColor White
    Write-Host "  4. 生成比特流 (Generate Bitstream)" -ForegroundColor White
    Write-Host "  5. 烧录到FPGA (Program Device)" -ForegroundColor White
    
    $response = Read-Host "`n  是否已完成FPGA综合和烧录? (y/n)"
    if ($response -eq 'y' -or $response -eq 'Y') {
        Write-Host "  FPGA更新确认" -ForegroundColor Green
    } else {
        Write-Host "  请完成FPGA更新后再运行测试" -ForegroundColor Yellow
        $SkipTest = $true
    }
} else {
    Write-Host "`n[3/4] 跳过FPGA综合" -ForegroundColor Gray
}

# 第四步：运行测试
if (-not $SkipTest) {
    Write-Host "`n[4/4] 运行性能测试..." -ForegroundColor Cyan
    
    try {
        & ".\test_ft601_optimization.ps1" -TestSize "100MB" -TestDuration 30
        Write-Host "  性能测试完成" -ForegroundColor Green
    } catch {
        Write-Warning "  性能测试执行错误: $($_.Exception.Message)"
    }
} else {
    Write-Host "`n[4/4] 跳过性能测试" -ForegroundColor Gray
}

# 完成总结
Write-Host "`n=== 快速开始完成 ===" -ForegroundColor Green

Write-Host "`n下一步建议:" -ForegroundColor Yellow
Write-Host "1. 查看测试结果文件夹中的性能报告" -ForegroundColor White
Write-Host "2. 根据实际性能调整配置参数" -ForegroundColor White
Write-Host "3. 在实际应用中验证优化效果" -ForegroundColor White

if (-not $SkipTest) {
    # 显示最新的测试结果
    $testResults = Get-ChildItem -Path $ProjectPath -Filter "test_results_*" -Directory | Sort-Object CreationTime -Descending | Select-Object -First 1
    if ($testResults) {
        Write-Host "`n最新测试结果: $($testResults.FullName)" -ForegroundColor Cyan
        
        $reportFile = Join-Path $testResults.FullName "test_report.txt"
        if (Test-Path $reportFile) {
            Write-Host "`n=== 测试结果摘要 ===" -ForegroundColor Yellow
            $reportContent = Get-Content $reportFile
            $reportContent | Select-Object -First 20 | ForEach-Object { Write-Host $_ -ForegroundColor White }
            
            if ($reportContent.Count -gt 20) {
                Write-Host "..." -ForegroundColor Gray
                Write-Host "查看完整报告: $reportFile" -ForegroundColor Cyan
            }
        }
    }
}

Write-Host "`n感谢使用FT601优化方案！" -ForegroundColor Green
