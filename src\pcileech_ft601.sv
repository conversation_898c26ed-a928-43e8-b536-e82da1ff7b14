//
// PCILeech FPGA - 优化版FT601控制器
//
// 性能优化特性:
// 1. 深度流水线设计
// 2. 双缓冲机制
// 3. 自适应队列管理
// 4. 减少等待周期
//
// (c) 优化版本, 2025
//

`timescale 1ns / 1ps
`include "pcileech_header.svh"

module pcileech_ft601_optimized(
    input               clk,
    input               rst,
    // TO/FROM PADS
    output [3:0]        FT601_BE,
    inout [31:0]        FT601_DATA,
    input               FT601_RXF_N,
    input               FT601_TXE_N,
    output bit          FT601_OE_N,
    output bit          FT601_RD_N,
    output bit          FT601_WR_N,
    output bit          FT601_SIWU_N,
    // TO/FROM FIFO - 扩展接口
    output bit [63:0]   dout,          // 扩展到64位
    output bit          dout_valid,
    input [63:0]        din,           // 扩展到64位
    input               din_wr_en,
    output              din_req_data,
    // 性能监控接口
    output [31:0]       perf_tx_count,
    output [31:0]       perf_rx_count,
    output [15:0]       perf_queue_depth
    );
    
    // 优化参数
    localparam QUEUE_DEPTH = 16;        // 增加队列深度
    localparam BURST_SIZE = 8;          // 突发传输大小
    localparam COOLDOWN_CYCLES = 4;     // 减少冷却周期
    
    // 状态机定义 - 优化版
    typedef enum logic [3:0] {
        S_IDLE          = 4'h0,
        S_RX_PREP       = 4'h1,
        S_RX_ACTIVE     = 4'h2,
        S_RX_BURST      = 4'h3,
        S_TX_PREP       = 4'h4,
        S_TX_ACTIVE     = 4'h5,
        S_TX_BURST      = 4'h6,
        S_COOLDOWN      = 4'h7
    } state_t;
    
    state_t state, next_state;
    
    // 双缓冲队列
    logic [63:0] tx_queue_a [0:QUEUE_DEPTH-1];
    logic [63:0] tx_queue_b [0:QUEUE_DEPTH-1];
    logic [63:0] rx_queue_a [0:QUEUE_DEPTH-1];
    logic [63:0] rx_queue_b [0:QUEUE_DEPTH-1];
    
    // 队列控制信号
    logic [4:0] tx_queue_count_a, tx_queue_count_b;
    logic [4:0] rx_queue_count_a, rx_queue_count_b;
    logic tx_buffer_select, rx_buffer_select;
    
    // 性能计数器
    logic [31:0] tx_counter, rx_counter;
    logic [3:0] cooldown_counter;
    logic [3:0] burst_counter;
    
    // 流水线寄存器
    logic [31:0] ft601_data_reg;
    logic [31:0] ft601_data_out_reg;
    logic ft601_data_valid_reg;
    
    initial begin
        FT601_OE_N   <= 1'b1;
        FT601_RD_N   <= 1'b1;
        FT601_WR_N   <= 1'b1;
        FT601_SIWU_N <= 1'b1;
        dout_valid   <= 1'b0;
        dout         <= 64'h0;
        state        <= S_IDLE;
        tx_counter   <= 32'h0;
        rx_counter   <= 32'h0;
        tx_buffer_select <= 1'b0;
        rx_buffer_select <= 1'b0;
    end
    
    // 输出性能监控信号
    assign perf_tx_count = tx_counter;
    assign perf_rx_count = rx_counter;
    assign perf_queue_depth = tx_buffer_select ? tx_queue_count_a : tx_queue_count_b;
    
    // FT601数据总线控制
    assign FT601_DATA = FT601_OE_N ? 32'hzzzzzzzz : ft601_data_out_reg;
    assign FT601_BE = 4'hf;
    
    // 优化的状态机 - 减少等待周期
    always_ff @(posedge clk) begin
        if (rst) begin
            state <= S_IDLE;
            cooldown_counter <= 4'h0;
            burst_counter <= 4'h0;
        end else begin
            state <= next_state;
            
            case (state)
                S_COOLDOWN: cooldown_counter <= cooldown_counter + 1'b1;
                S_RX_BURST, S_TX_BURST: burst_counter <= burst_counter + 1'b1;
                default: begin
                    cooldown_counter <= 4'h0;
                    burst_counter <= 4'h0;
                end
            endcase
        end
    end
    
    // 下一状态逻辑 - 优化版
    always_comb begin
        next_state = state;
        
        case (state)
            S_IDLE: begin
                if (!FT601_RXF_N && (rx_buffer_select ? rx_queue_count_a : rx_queue_count_b) < (QUEUE_DEPTH - BURST_SIZE))
                    next_state = S_RX_PREP;
                else if (!FT601_TXE_N && (tx_buffer_select ? tx_queue_count_a : tx_queue_count_b) > 0)
                    next_state = S_TX_PREP;
            end
            
            S_RX_PREP: next_state = S_RX_ACTIVE;
            
            S_RX_ACTIVE: begin
                if (FT601_RXF_N)
                    next_state = S_COOLDOWN;
                else if (burst_counter >= BURST_SIZE - 1)
                    next_state = S_RX_BURST;
            end
            
            S_RX_BURST: begin
                if (burst_counter >= BURST_SIZE - 1)
                    next_state = S_COOLDOWN;
            end
            
            S_TX_PREP: next_state = S_TX_ACTIVE;
            
            S_TX_ACTIVE: begin
                if (FT601_TXE_N)
                    next_state = S_COOLDOWN;
                else if (burst_counter >= BURST_SIZE - 1)
                    next_state = S_TX_BURST;
            end
            
            S_TX_BURST: begin
                if (burst_counter >= BURST_SIZE - 1)
                    next_state = S_COOLDOWN;
            end
            
            S_COOLDOWN: begin
                if (cooldown_counter >= COOLDOWN_CYCLES - 1)
                    next_state = S_IDLE;
            end
        endcase
    end
    
    // 控制信号生成 - 流水线优化
    always_ff @(posedge clk) begin
        if (rst) begin
            FT601_OE_N <= 1'b1;
            FT601_RD_N <= 1'b1;
            FT601_WR_N <= 1'b1;
            dout_valid <= 1'b0;
        end else begin
            case (next_state)
                S_RX_PREP: begin
                    FT601_OE_N <= 1'b1;
                    FT601_RD_N <= 1'b0;  // 提前准备读取
                    FT601_WR_N <= 1'b1;
                end
                
                S_RX_ACTIVE, S_RX_BURST: begin
                    FT601_OE_N <= 1'b1;
                    FT601_RD_N <= 1'b0;
                    FT601_WR_N <= 1'b1;
                    // 流水线读取数据
                    ft601_data_reg <= FT601_DATA;
                    ft601_data_valid_reg <= 1'b1;
                end
                
                S_TX_PREP: begin
                    FT601_OE_N <= 1'b0;  // 提前准备输出
                    FT601_RD_N <= 1'b1;
                    FT601_WR_N <= 1'b1;
                end
                
                S_TX_ACTIVE, S_TX_BURST: begin
                    FT601_OE_N <= 1'b0;
                    FT601_RD_N <= 1'b1;
                    FT601_WR_N <= 1'b0;
                end
                
                default: begin
                    FT601_OE_N <= 1'b1;
                    FT601_RD_N <= 1'b1;
                    FT601_WR_N <= 1'b1;
                    ft601_data_valid_reg <= 1'b0;
                end
            endcase
        end
    end
    
    // 双缓冲队列管理 - 优化版
    always_ff @(posedge clk) begin
        if (rst) begin
            tx_queue_count_a <= 5'h0;
            tx_queue_count_b <= 5'h0;
            rx_queue_count_a <= 5'h0;
            rx_queue_count_b <= 5'h0;
            tx_buffer_select <= 1'b0;
            rx_buffer_select <= 1'b0;
        end else begin
            // 发送队列管理
            if (din_wr_en) begin
                if (tx_buffer_select) begin
                    if (tx_queue_count_a < QUEUE_DEPTH) begin
                        tx_queue_a[tx_queue_count_a] <= din;
                        tx_queue_count_a <= tx_queue_count_a + 1'b1;
                    end
                end else begin
                    if (tx_queue_count_b < QUEUE_DEPTH) begin
                        tx_queue_b[tx_queue_count_b] <= din;
                        tx_queue_count_b <= tx_queue_count_b + 1'b1;
                    end
                end
            end
            
            // 缓冲区切换逻辑
            if (tx_buffer_select && tx_queue_count_a == 0 && tx_queue_count_b > 0) begin
                tx_buffer_select <= 1'b0;
            end else if (!tx_buffer_select && tx_queue_count_b == 0 && tx_queue_count_a > 0) begin
                tx_buffer_select <= 1'b1;
            end
        end
    end

endmodule
