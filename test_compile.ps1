# 简化的编译测试脚本
# 专门用于测试PCILeech优化版的编译问题

param(
    [Parameter(Mandatory=$false)]
    [string]$ProjectPath = "G:\FPGA\20250327\100t20250618\100t484-1"
)

Write-Host "=== PCILeech 编译测试 ===" -ForegroundColor Green
Write-Host "项目路径: $ProjectPath" -ForegroundColor Yellow

Set-Location $ProjectPath

# 查找MSBuild
$msbuildPaths = @(
    "${env:ProgramFiles}\Microsoft Visual Studio\2022\Professional\MSBuild\Current\Bin\MSBuild.exe",
    "${env:ProgramFiles}\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\MSBuild.exe",
    "${env:ProgramFiles(x86)}\Microsoft Visual Studio\2019\Professional\MSBuild\Current\Bin\MSBuild.exe",
    "${env:ProgramFiles(x86)}\Microsoft Visual Studio\2019\Community\MSBuild\Current\Bin\MSBuild.exe"
)

$msbuildPath = $null
foreach ($path in $msbuildPaths) {
    if (Test-Path $path) {
        $msbuildPath = $path
        Write-Host "找到MSBuild: $path" -ForegroundColor Green
        break
    }
}

if (-not $msbuildPath) {
    Write-Host "未找到MSBuild" -ForegroundColor Red
    exit 1
}

# 检查项目文件
$projectFile = "pcileech-master\pcileech\pcileech.vcxproj"
if (-not (Test-Path $projectFile)) {
    Write-Host "项目文件不存在: $projectFile" -ForegroundColor Red
    exit 1
}

Write-Host "项目文件: $projectFile" -ForegroundColor Green

# 清理项目
Write-Host "`n清理项目..." -ForegroundColor Cyan
try {
    & $msbuildPath $projectFile /t:Clean /p:Configuration=Release /p:Platform=x64 /verbosity:minimal /nologo
    Write-Host "清理完成" -ForegroundColor Green
} catch {
    Write-Host "清理失败: $($_.Exception.Message)" -ForegroundColor Yellow
}

# 尝试编译
Write-Host "`n开始编译..." -ForegroundColor Cyan
try {
    $output = & $msbuildPath $projectFile /p:Configuration=Release /p:Platform=x64 /verbosity:detailed /nologo 2>&1
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "编译成功!" -ForegroundColor Green
        
        # 检查输出文件
        $exePaths = @(
            "pcileech-master\pcileech\x64\Release\pcileech.exe",
            "pcileech-master\files\pcileech.exe"
        )
        
        foreach ($exePath in $exePaths) {
            if (Test-Path $exePath) {
                $exe = Get-Item $exePath
                Write-Host "可执行文件: $exePath" -ForegroundColor Green
                Write-Host "  大小: $($exe.Length) 字节" -ForegroundColor White
                Write-Host "  时间: $($exe.LastWriteTime)" -ForegroundColor White
                break
            }
        }
        
        # 复制到标准位置
        if ((Test-Path "pcileech-master\pcileech\x64\Release\pcileech.exe") -and (-not (Test-Path "pcileech-master\files\pcileech.exe"))) {
            if (-not (Test-Path "pcileech-master\files")) {
                New-Item -ItemType Directory -Path "pcileech-master\files" -Force | Out-Null
            }
            Copy-Item "pcileech-master\pcileech\x64\Release\pcileech.exe" "pcileech-master\files\pcileech.exe" -Force
            Write-Host "已复制到标准位置" -ForegroundColor Green
        }
        
    } else {
        Write-Host "编译失败，退出代码: $LASTEXITCODE" -ForegroundColor Red
        Write-Host "`n编译输出:" -ForegroundColor Yellow
        
        # 分析错误
        $errors = $output | Where-Object { $_ -match "error" -or $_ -match "fatal error" }
        $warnings = $output | Where-Object { $_ -match "warning" }
        
        if ($errors.Count -gt 0) {
            Write-Host "`n错误 ($($errors.Count)):" -ForegroundColor Red
            $errors | ForEach-Object { Write-Host "  $_" -ForegroundColor Red }
        }
        
        if ($warnings.Count -gt 0) {
            Write-Host "`n警告 ($($warnings.Count)):" -ForegroundColor Yellow
            $warnings | ForEach-Object { Write-Host "  $_" -ForegroundColor Yellow }
        }
        
        # 显示完整输出（限制行数）
        Write-Host "`n完整输出 (最后50行):" -ForegroundColor Gray
        $output | Select-Object -Last 50 | ForEach-Object { Write-Host "  $_" -ForegroundColor Gray }
    }
} catch {
    Write-Host "编译过程出错: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n=== 编译测试完成 ===" -ForegroundColor Green
