# FT601优化测试和验证脚本
# PowerShell脚本用于测试优化后的性能

param(
    [Parameter(Mandatory=$false)]
    [string]$ProjectPath = "G:\FPGA\20250327\100t20250618\100t484-1",
    
    [Parameter(Mandatory=$false)]
    [string]$TestSize = "1GB",
    
    [Parameter(Mandatory=$false)]
    [int]$TestDuration = 60,
    
    [Parameter(Mandatory=$false)]
    [switch]$DetailedReport = $false
)

$ErrorActionPreference = "Stop"

Write-Host "=== FT601优化性能测试脚本 ===" -ForegroundColor Green
Write-Host "项目路径: $ProjectPath" -ForegroundColor Yellow
Write-Host "测试大小: $TestSize" -ForegroundColor Yellow
Write-Host "测试时长: $TestDuration 秒" -ForegroundColor Yellow

Set-Location $ProjectPath

# 检查可执行文件
$exePath = Join-Path $ProjectPath "pcileech-master\files\pcileech.exe"
if (-not (Test-Path $exePath)) {
    Write-Error "PCILeech可执行文件不存在: $exePath"
    exit 1
}

# 创建测试结果目录
$testResultsPath = Join-Path $ProjectPath "test_results_$(Get-Date -Format 'yyyyMMdd_HHmmss')"
New-Item -ItemType Directory -Path $testResultsPath -Force | Out-Null

Write-Host "`n[1/5] 基础连接测试..." -ForegroundColor Cyan

try {
    # 测试设备连接
    $output = & $exePath "info" 2>&1
    if ($LASTEXITCODE -eq 0) {
        Write-Host "  设备连接正常" -ForegroundColor Green
        $output | Out-File (Join-Path $testResultsPath "device_info.txt")
    } else {
        Write-Warning "  设备连接可能存在问题"
        $output | Out-File (Join-Path $testResultsPath "device_info_error.txt")
    }
} catch {
    Write-Error "  无法执行设备信息查询: $($_.Exception.Message)"
}

Write-Host "`n[2/5] 性能基准测试..." -ForegroundColor Cyan

try {
    # 运行基准测试
    Write-Host "  执行基准测试..." -ForegroundColor Yellow
    $benchmarkOutput = & $exePath "benchmark" 2>&1
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "  基准测试完成" -ForegroundColor Green
        $benchmarkOutput | Out-File (Join-Path $testResultsPath "benchmark_results.txt")
        
        # 解析基准测试结果
        $throughputLine = $benchmarkOutput | Where-Object { $_ -match "MB/s" }
        if ($throughputLine) {
            Write-Host "  检测到吞吐量: $throughputLine" -ForegroundColor Cyan
        }
    } else {
        Write-Warning "  基准测试失败"
        $benchmarkOutput | Out-File (Join-Path $testResultsPath "benchmark_error.txt")
    }
} catch {
    Write-Warning "  基准测试执行错误: $($_.Exception.Message)"
}

Write-Host "`n[3/5] 内存读取性能测试..." -ForegroundColor Cyan

try {
    # 转换测试大小
    $testSizeBytes = switch ($TestSize) {
        "1MB" { 0x100000 }
        "10MB" { 0xA00000 }
        "100MB" { 0x6400000 }
        "1GB" { 0x40000000 }
        default { 0x40000000 }
    }
    
    $testSizeHex = "0x{0:X}" -f $testSizeBytes
    
    Write-Host "  执行内存读取测试 (大小: $testSizeHex)..." -ForegroundColor Yellow
    
    # 记录开始时间
    $startTime = Get-Date
    
    # 执行内存转储测试
    $dumpOutput = & $exePath "dump" "-min" "0x1000" "-max" $testSizeHex "-out" "none" 2>&1
    
    # 记录结束时间
    $endTime = Get-Date
    $elapsedTime = ($endTime - $startTime).TotalSeconds
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "  内存读取测试完成" -ForegroundColor Green
        Write-Host "  耗时: $([math]::Round($elapsedTime, 2)) 秒" -ForegroundColor Cyan
        
        # 计算吞吐量
        $throughputMBps = ($testSizeBytes / (1024 * 1024)) / $elapsedTime
        Write-Host "  平均吞吐量: $([math]::Round($throughputMBps, 2)) MB/s" -ForegroundColor Green
        
        $dumpOutput | Out-File (Join-Path $testResultsPath "memory_dump_test.txt")
        
        # 保存性能数据
        $perfData = @{
            TestSize = $TestSize
            TestSizeBytes = $testSizeBytes
            ElapsedTimeSeconds = $elapsedTime
            ThroughputMBps = $throughputMBps
            Timestamp = $startTime
        }
        $perfData | ConvertTo-Json | Out-File (Join-Path $testResultsPath "performance_data.json")
        
    } else {
        Write-Warning "  内存读取测试失败"
        $dumpOutput | Out-File (Join-Path $testResultsPath "memory_dump_error.txt")
    }
} catch {
    Write-Warning "  内存读取测试执行错误: $($_.Exception.Message)"
}

Write-Host "`n[4/5] 稳定性测试..." -ForegroundColor Cyan

try {
    Write-Host "  执行 $TestDuration 秒稳定性测试..." -ForegroundColor Yellow
    
    $stabilityResults = @()
    $testInterval = 10  # 每10秒测试一次
    $testIterations = [math]::Floor($TestDuration / $testInterval)
    
    for ($i = 1; $i -le $testIterations; $i++) {
        Write-Host "    迭代 $i/$testIterations..." -ForegroundColor Gray
        
        $iterStartTime = Get-Date
        
        # 执行小规模读取测试
        $iterOutput = & $exePath "dump" "-min" "0x1000" "-max" "0x100000" "-out" "none" 2>&1
        
        $iterEndTime = Get-Date
        $iterElapsed = ($iterEndTime - $iterStartTime).TotalSeconds
        
        $iterResult = @{
            Iteration = $i
            Success = ($LASTEXITCODE -eq 0)
            ElapsedTime = $iterElapsed
            Timestamp = $iterStartTime
        }
        
        $stabilityResults += $iterResult
        
        if ($LASTEXITCODE -ne 0) {
            Write-Warning "    迭代 $i 失败"
        }
        
        # 等待下一次测试
        if ($i -lt $testIterations) {
            Start-Sleep -Seconds ($testInterval - $iterElapsed)
        }
    }
    
    # 分析稳定性结果
    $successCount = ($stabilityResults | Where-Object { $_.Success }).Count
    $successRate = ($successCount / $testIterations) * 100
    
    Write-Host "  稳定性测试完成" -ForegroundColor Green
    Write-Host "  成功率: $([math]::Round($successRate, 1))% ($successCount/$testIterations)" -ForegroundColor Cyan
    
    $stabilityResults | ConvertTo-Json | Out-File (Join-Path $testResultsPath "stability_results.json")
    
} catch {
    Write-Warning "  稳定性测试执行错误: $($_.Exception.Message)"
}

Write-Host "`n[5/5] 生成测试报告..." -ForegroundColor Cyan

# 生成综合测试报告
$reportContent = @"
FT601优化性能测试报告
=====================

测试时间: $(Get-Date)
项目路径: $ProjectPath
测试配置:
- 测试大小: $TestSize
- 测试时长: $TestDuration 秒

测试结果摘要:
"@

# 读取性能数据
$perfDataPath = Join-Path $testResultsPath "performance_data.json"
if (Test-Path $perfDataPath) {
    $perfData = Get-Content $perfDataPath | ConvertFrom-Json
    $reportContent += @"

内存读取性能:
- 测试大小: $($perfData.TestSize)
- 耗时: $([math]::Round($perfData.ElapsedTimeSeconds, 2)) 秒
- 吞吐量: $([math]::Round($perfData.ThroughputMBps, 2)) MB/s
"@
}

# 读取稳定性数据
$stabilityDataPath = Join-Path $testResultsPath "stability_results.json"
if (Test-Path $stabilityDataPath) {
    $stabilityData = Get-Content $stabilityDataPath | ConvertFrom-Json
    $successCount = ($stabilityData | Where-Object { $_.Success }).Count
    $totalCount = $stabilityData.Count
    $successRate = if ($totalCount -gt 0) { ($successCount / $totalCount) * 100 } else { 0 }
    
    $reportContent += @"

稳定性测试:
- 测试迭代: $totalCount
- 成功次数: $successCount
- 成功率: $([math]::Round($successRate, 1))%
"@
}

$reportContent += @"

性能对比 (与优化前):
- 预期吞吐量提升: 150%+
- 预期延迟降低: 50%
- 预期错误率降低: 50%

建议:
1. 如果吞吐量低于预期，检查FPGA配置参数
2. 如果稳定性不佳，考虑降低激进优化程度
3. 监控系统资源使用情况
4. 根据实际应用场景调整缓冲区大小

测试文件位置: $testResultsPath
"@

$reportPath = Join-Path $testResultsPath "test_report.txt"
Set-Content $reportPath $reportContent -Encoding UTF8

Write-Host "  测试报告已生成: $reportPath" -ForegroundColor Green

# 如果需要详细报告，显示更多信息
if ($DetailedReport) {
    Write-Host "`n=== 详细测试报告 ===" -ForegroundColor Yellow
    Get-Content $reportPath | Write-Host
    
    # 显示性能图表 (简化版)
    if (Test-Path $perfDataPath) {
        $perfData = Get-Content $perfDataPath | ConvertFrom-Json
        Write-Host "`n性能指标:" -ForegroundColor Yellow
        Write-Host "  吞吐量: $([math]::Round($perfData.ThroughputMBps, 2)) MB/s" -ForegroundColor White
        
        # 简单的性能等级评估
        $perfLevel = if ($perfData.ThroughputMBps -gt 200) { "优秀" }
                    elseif ($perfData.ThroughputMBps -gt 100) { "良好" }
                    elseif ($perfData.ThroughputMBps -gt 50) { "一般" }
                    else { "需要优化" }
        
        Write-Host "  性能等级: $perfLevel" -ForegroundColor $(
            if ($perfLevel -eq "优秀") { "Green" }
            elseif ($perfLevel -eq "良好") { "Cyan" }
            elseif ($perfLevel -eq "一般") { "Yellow" }
            else { "Red" }
        )
    }
}

Write-Host "`n=== 测试完成 ===" -ForegroundColor Green
Write-Host "所有测试结果已保存到: $testResultsPath" -ForegroundColor Cyan
Write-Host "查看详细报告: $reportPath" -ForegroundColor Cyan
