﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 16
VisualStudioVersion = 16.0.28729.10
MinimumVisualStudioVersion = 10.0.40219.1
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "pcileech", "pcileech\pcileech.vcxproj", "{DFFA1B4C-279B-4356-ADB1-08A6F4795931}"
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "pcileech_shellcode", "pcileech_shellcode\pcileech_shellcode.vcxproj", "{5C698F13-6E9F-46F3-95FC-55376A65D8BF}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Solution Items", "Solution Items", "{41BC2617-A896-4D63-9F5E-ED26C5A613B8}"
	ProjectSection(SolutionItems) = preProject
		LICENSE = LICENSE
		readme.md = readme.md
	EndProjectSection
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "pcileech_kmd", "pcileech_kmd", "{2A4F90E3-A543-4D9C-9F89-CBCE396AE08A}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "linux", "linux", "{2E5DAA3B-99A5-4493-B68B-B24153FCB6E4}"
	ProjectSection(SolutionItems) = preProject
		pcileech_kmd\linux\Makefile = pcileech_kmd\linux\Makefile
		pcileech_kmd\linux\pcileech_kmd.c = pcileech_kmd\linux\pcileech_kmd.c
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "USB3380Flash", "usb3380_flash\windows\USB3380Flash\USB3380Flash.vcxproj", "{E11BECC1-685F-41B9-A352-A6127FAB3758}"
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "USB3380Flash_installer", "usb3380_flash\windows\USB3380Flash_Installer\USB3380Flash_Installer.vcxproj", "{F2F4AA4A-BEFE-4738-9412-820007919334}"
	ProjectSection(ProjectDependencies) = postProject
		{DFFA1B4C-279B-4356-ADB1-08A6F4795931} = {DFFA1B4C-279B-4356-ADB1-08A6F4795931}
		{E11BECC1-685F-41B9-A352-A6127FAB3758} = {E11BECC1-685F-41B9-A352-A6127FAB3758}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "leechcore", "..\LeechCore\leechcore\leechcore.vcxproj", "{3476ABD2-5DEA-43E6-A676-8BE25F74535A}"
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "vmm", "..\MemProcFS\vmm\vmm.vcxproj", "{6326FCE0-1BA5-4AEC-9973-7783309FFD6B}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|x64 = Debug|x64
		Debug|x86 = Debug|x86
		Release|x64 = Release|x64
		Release|x86 = Release|x86
		ReleaseMT|x64 = ReleaseMT|x64
		ReleaseMT|x86 = ReleaseMT|x86
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{DFFA1B4C-279B-4356-ADB1-08A6F4795931}.Debug|x64.ActiveCfg = Debug|x64
		{DFFA1B4C-279B-4356-ADB1-08A6F4795931}.Debug|x64.Build.0 = Debug|x64
		{DFFA1B4C-279B-4356-ADB1-08A6F4795931}.Debug|x86.ActiveCfg = Debug|Win32
		{DFFA1B4C-279B-4356-ADB1-08A6F4795931}.Debug|x86.Build.0 = Debug|Win32
		{DFFA1B4C-279B-4356-ADB1-08A6F4795931}.Release|x64.ActiveCfg = Release|x64
		{DFFA1B4C-279B-4356-ADB1-08A6F4795931}.Release|x64.Build.0 = Release|x64
		{DFFA1B4C-279B-4356-ADB1-08A6F4795931}.Release|x86.ActiveCfg = Release|Win32
		{DFFA1B4C-279B-4356-ADB1-08A6F4795931}.Release|x86.Build.0 = Release|Win32
		{DFFA1B4C-279B-4356-ADB1-08A6F4795931}.ReleaseMT|x64.ActiveCfg = Release|x64
		{DFFA1B4C-279B-4356-ADB1-08A6F4795931}.ReleaseMT|x86.ActiveCfg = Debug|x64
		{5C698F13-6E9F-46F3-95FC-55376A65D8BF}.Debug|x64.ActiveCfg = Release|x64
		{5C698F13-6E9F-46F3-95FC-55376A65D8BF}.Debug|x86.ActiveCfg = Release|x64
		{5C698F13-6E9F-46F3-95FC-55376A65D8BF}.Release|x64.ActiveCfg = Release|x64
		{5C698F13-6E9F-46F3-95FC-55376A65D8BF}.Release|x86.ActiveCfg = Release|x64
		{5C698F13-6E9F-46F3-95FC-55376A65D8BF}.ReleaseMT|x64.ActiveCfg = Release|x64
		{5C698F13-6E9F-46F3-95FC-55376A65D8BF}.ReleaseMT|x86.ActiveCfg = Release|x64
		{E11BECC1-685F-41B9-A352-A6127FAB3758}.Debug|x64.ActiveCfg = Debug|x64
		{E11BECC1-685F-41B9-A352-A6127FAB3758}.Debug|x86.ActiveCfg = Debug|x64
		{E11BECC1-685F-41B9-A352-A6127FAB3758}.Release|x64.ActiveCfg = Release|x64
		{E11BECC1-685F-41B9-A352-A6127FAB3758}.Release|x86.ActiveCfg = Release|x64
		{E11BECC1-685F-41B9-A352-A6127FAB3758}.ReleaseMT|x64.ActiveCfg = ReleaseMT|x64
		{E11BECC1-685F-41B9-A352-A6127FAB3758}.ReleaseMT|x64.Build.0 = ReleaseMT|x64
		{E11BECC1-685F-41B9-A352-A6127FAB3758}.ReleaseMT|x86.ActiveCfg = ReleaseMT|x64
		{F2F4AA4A-BEFE-4738-9412-820007919334}.Debug|x64.ActiveCfg = Debug|x64
		{F2F4AA4A-BEFE-4738-9412-820007919334}.Debug|x86.ActiveCfg = Debug|x64
		{F2F4AA4A-BEFE-4738-9412-820007919334}.Release|x64.ActiveCfg = Release|x64
		{F2F4AA4A-BEFE-4738-9412-820007919334}.Release|x86.ActiveCfg = Release|x64
		{F2F4AA4A-BEFE-4738-9412-820007919334}.ReleaseMT|x64.ActiveCfg = ReleaseMT|x64
		{F2F4AA4A-BEFE-4738-9412-820007919334}.ReleaseMT|x64.Build.0 = ReleaseMT|x64
		{F2F4AA4A-BEFE-4738-9412-820007919334}.ReleaseMT|x86.ActiveCfg = ReleaseMT|x64
		{3476ABD2-5DEA-43E6-A676-8BE25F74535A}.Debug|x64.ActiveCfg = Debug|x64
		{3476ABD2-5DEA-43E6-A676-8BE25F74535A}.Debug|x64.Build.0 = Debug|x64
		{3476ABD2-5DEA-43E6-A676-8BE25F74535A}.Debug|x86.ActiveCfg = Debug|Win32
		{3476ABD2-5DEA-43E6-A676-8BE25F74535A}.Debug|x86.Build.0 = Debug|Win32
		{3476ABD2-5DEA-43E6-A676-8BE25F74535A}.Release|x64.ActiveCfg = Release|x64
		{3476ABD2-5DEA-43E6-A676-8BE25F74535A}.Release|x64.Build.0 = Release|x64
		{3476ABD2-5DEA-43E6-A676-8BE25F74535A}.Release|x86.ActiveCfg = Release|Win32
		{3476ABD2-5DEA-43E6-A676-8BE25F74535A}.Release|x86.Build.0 = Release|Win32
		{3476ABD2-5DEA-43E6-A676-8BE25F74535A}.ReleaseMT|x64.ActiveCfg = Release|x64
		{3476ABD2-5DEA-43E6-A676-8BE25F74535A}.ReleaseMT|x86.ActiveCfg = Release|Win32
		{6326FCE0-1BA5-4AEC-9973-7783309FFD6B}.Debug|x64.ActiveCfg = Debug|x64
		{6326FCE0-1BA5-4AEC-9973-7783309FFD6B}.Debug|x64.Build.0 = Debug|x64
		{6326FCE0-1BA5-4AEC-9973-7783309FFD6B}.Debug|x86.ActiveCfg = Debug|Win32
		{6326FCE0-1BA5-4AEC-9973-7783309FFD6B}.Debug|x86.Build.0 = Debug|Win32
		{6326FCE0-1BA5-4AEC-9973-7783309FFD6B}.Release|x64.ActiveCfg = Release|x64
		{6326FCE0-1BA5-4AEC-9973-7783309FFD6B}.Release|x64.Build.0 = Release|x64
		{6326FCE0-1BA5-4AEC-9973-7783309FFD6B}.Release|x86.ActiveCfg = Release|Win32
		{6326FCE0-1BA5-4AEC-9973-7783309FFD6B}.Release|x86.Build.0 = Release|Win32
		{6326FCE0-1BA5-4AEC-9973-7783309FFD6B}.ReleaseMT|x64.ActiveCfg = Release|x64
		{6326FCE0-1BA5-4AEC-9973-7783309FFD6B}.ReleaseMT|x86.ActiveCfg = Release|x64
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{2A4F90E3-A543-4D9C-9F89-CBCE396AE08A} = {41BC2617-A896-4D63-9F5E-ED26C5A613B8}
		{2E5DAA3B-99A5-4493-B68B-B24153FCB6E4} = {2A4F90E3-A543-4D9C-9F89-CBCE396AE08A}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {7D482A0F-DF96-4909-8C6B-8A4F6353DC23}
	EndGlobalSection
EndGlobal
