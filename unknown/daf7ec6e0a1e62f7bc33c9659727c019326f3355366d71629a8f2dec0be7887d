﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="Source Files">
      <UniqueIdentifier>{4FC737F1-C7A5-4376-A066-2A32D752A2FF}</UniqueIdentifier>
      <Extensions>cpp;c;cc;cxx;def;odl;idl;hpj;bat;asm;asmx</Extensions>
    </Filter>
    <Filter Include="Header Files">
      <UniqueIdentifier>{93995380-89BD-4b04-88EB-625FBE52EBFB}</UniqueIdentifier>
      <Extensions>h;hh;hpp;hxx;hm;inl;inc;xsd</Extensions>
    </Filter>
    <Filter Include="Resource Files">
      <UniqueIdentifier>{67DA6AB6-F800-4c08-8B7A-83BB121AAD01}</UniqueIdentifier>
      <Extensions>rc;ico;cur;bmp;dlg;rc2;rct;bin;rgs;gif;jpg;jpeg;jpe;resx;tiff;tif;png;wav;mfcribbon-ms</Extensions>
    </Filter>
    <Filter Include="Header Files\includes">
      <UniqueIdentifier>{b5332eb1-5727-4efd-84e5-83cda653c431}</UniqueIdentifier>
    </Filter>
    <Filter Include="Source Files\ob">
      <UniqueIdentifier>{95661a3c-2e91-40f9-8af4-abcf2f03eb52}</UniqueIdentifier>
    </Filter>
    <Filter Include="Header Files\ob">
      <UniqueIdentifier>{db37c203-79b1-4412-9064-840ab38c29fd}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <None Include="Makefile">
      <Filter>Resource Files</Filter>
    </None>
    <None Include="Makefile.macos">
      <Filter>Resource Files</Filter>
    </None>
  </ItemGroup>
  <ItemGroup>
    <ResourceCompile Include="pcileech.rc">
      <Filter>Resource Files</Filter>
    </ResourceCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="device.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="executor.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="extra.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="help.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="kmd.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="memdump.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="mempatch.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="oscompatibility.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="pcileech.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="shellcode.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="statistics.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="umd.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="util.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="version.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="vfs.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\includes\dokan.h">
      <Filter>Header Files\includes</Filter>
    </ClInclude>
    <ClInclude Include="..\includes\fileinfo.h">
      <Filter>Header Files\includes</Filter>
    </ClInclude>
    <ClInclude Include="..\includes\leechcore.h">
      <Filter>Header Files\includes</Filter>
    </ClInclude>
    <ClInclude Include="..\includes\public.h">
      <Filter>Header Files\includes</Filter>
    </ClInclude>
    <ClInclude Include="..\includes\vmmdll.h">
      <Filter>Header Files\includes</Filter>
    </ClInclude>
    <ClInclude Include="vmmx.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="ob\ob.h">
      <Filter>Header Files\ob</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="device.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="executor.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="extra.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="help.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="kmd.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="memdump.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="mempatch.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="oscompatibility.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="pcileech.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="statistics.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="umd.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="util.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="vfs.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="vmmx.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="ob\ob_cachemap.c">
      <Filter>Source Files\ob</Filter>
    </ClCompile>
    <ClCompile Include="ob\ob_core.c">
      <Filter>Source Files\ob</Filter>
    </ClCompile>
    <ClCompile Include="ob\ob_map.c">
      <Filter>Source Files\ob</Filter>
    </ClCompile>
    <ClCompile Include="ob\ob_set.c">
      <Filter>Source Files\ob</Filter>
    </ClCompile>
    <ClCompile Include="charutil.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="vfslist.c">
      <Filter>Source Files</Filter>
    </ClCompile>
  </ItemGroup>
</Project>