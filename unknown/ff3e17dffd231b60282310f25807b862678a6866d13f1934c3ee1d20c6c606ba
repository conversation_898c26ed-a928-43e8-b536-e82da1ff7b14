// installer.c : flash driver installation program.
// required to get around windows code signing requirement when importing UMDF
// drivers into the system driver store.
//
// (c) Ulf Frisk, 2016
// Author: Ulf Frisk, <EMAIL>
//
#include <Windows.h>
#include <Newdev.h>
#include <stdio.h>

#pragma comment(lib, "crypt32.lib")
#pragma comment(lib, "newdev.lib")

#define CONFIG_PATH_INF "USB3380Flash.inf"

const BYTE SIGNER_CERTIFICATE[] = {
	0x30, 0x82, 0x03, 0x0e, 0x30, 0x82, 0x01, 0xf6, 0xa0, 0x03, 0x02, 0x01,
	0x02, 0x02, 0x10, 0x32, 0xca, 0x16, 0x38, 0xfd, 0xc9, 0x6d, 0xa6, 0x4f,
	0x36, 0xb9, 0x22, 0x68, 0x3d, 0xba, 0xb4, 0x30, 0x0d, 0x06, 0x09, 0x2a,
	0x86, 0x48, 0x86, 0xf7, 0x0d, 0x01, 0x01, 0x05, 0x05, 0x00, 0x30, 0x30,
	0x31, 0x2e, 0x30, 0x2c, 0x06, 0x03, 0x55, 0x04, 0x03, 0x13, 0x25, 0x57,
	0x44, 0x4b, 0x54, 0x65, 0x73, 0x74, 0x43, 0x65, 0x72, 0x74, 0x20, 0x75,
	0x6c, 0x66, 0x72, 0x31, 0x31, 0x2c, 0x31, 0x33, 0x30, 0x38, 0x37, 0x32,
	0x33, 0x32, 0x33, 0x32, 0x36, 0x32, 0x37, 0x31, 0x38, 0x34, 0x30, 0x38,
	0x30, 0x1e, 0x17, 0x0d, 0x31, 0x35, 0x30, 0x39, 0x32, 0x30, 0x31, 0x34,
	0x31, 0x38, 0x34, 0x37, 0x5a, 0x17, 0x0d, 0x32, 0x35, 0x30, 0x39, 0x32,
	0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x5a, 0x30, 0x30, 0x31, 0x2e,
	0x30, 0x2c, 0x06, 0x03, 0x55, 0x04, 0x03, 0x13, 0x25, 0x57, 0x44, 0x4b,
	0x54, 0x65, 0x73, 0x74, 0x43, 0x65, 0x72, 0x74, 0x20, 0x75, 0x6c, 0x66,
	0x72, 0x31, 0x31, 0x2c, 0x31, 0x33, 0x30, 0x38, 0x37, 0x32, 0x33, 0x32,
	0x33, 0x32, 0x36, 0x32, 0x37, 0x31, 0x38, 0x34, 0x30, 0x38, 0x30, 0x82,
	0x01, 0x22, 0x30, 0x0d, 0x06, 0x09, 0x2a, 0x86, 0x48, 0x86, 0xf7, 0x0d,
	0x01, 0x01, 0x01, 0x05, 0x00, 0x03, 0x82, 0x01, 0x0f, 0x00, 0x30, 0x82,
	0x01, 0x0a, 0x02, 0x82, 0x01, 0x01, 0x00, 0xc8, 0x54, 0xde, 0x6d, 0xfd,
	0x19, 0x97, 0x3f, 0xf0, 0x6e, 0x8a, 0xc2, 0xff, 0x32, 0xbd, 0x61, 0x3d,
	0x9c, 0xbc, 0xd7, 0x0e, 0x80, 0x51, 0x6e, 0xf1, 0xf2, 0xf3, 0x0f, 0x54,
	0x24, 0x82, 0xa4, 0x48, 0xfa, 0xd8, 0xcb, 0xf0, 0x43, 0xc6, 0x44, 0xde,
	0x61, 0x14, 0xe2, 0xf6, 0xc8, 0xf9, 0x2a, 0xb2, 0x61, 0x46, 0xd8, 0x9b,
	0xc6, 0x99, 0x71, 0x1f, 0x05, 0xae, 0x39, 0xd1, 0x86, 0x28, 0x34, 0x63,
	0x35, 0x32, 0x5a, 0x79, 0x09, 0x9d, 0x7f, 0x4b, 0x8a, 0x72, 0xcc, 0xdb,
	0xf4, 0xee, 0x05, 0x7c, 0xb7, 0x6c, 0x24, 0x3d, 0x7a, 0xce, 0x6a, 0x9d,
	0xf6, 0x43, 0xc7, 0x0c, 0x03, 0xa7, 0x0f, 0x3f, 0xc8, 0xb2, 0x80, 0x13,
	0xe3, 0x8c, 0xf2, 0x16, 0x6e, 0x25, 0xbf, 0x53, 0x9d, 0xfc, 0xaa, 0xce,
	0x2b, 0xa8, 0xd3, 0x88, 0xd2, 0xdc, 0x3f, 0x78, 0x30, 0x24, 0x2a, 0x12,
	0x00, 0x2b, 0x59, 0xbf, 0xf3, 0x96, 0x02, 0x73, 0xae, 0xb9, 0x77, 0x03,
	0xa7, 0x12, 0xde, 0xc3, 0x4d, 0x3d, 0x61, 0xb9, 0xd2, 0x57, 0x4a, 0x37,
	0xf0, 0xca, 0x55, 0x97, 0xc2, 0xbd, 0x8a, 0xb0, 0x97, 0xc5, 0x78, 0x5c,
	0x1a, 0x32, 0x22, 0xba, 0x58, 0xaa, 0x32, 0x4d, 0x9a, 0xf3, 0xe5, 0x93,
	0x54, 0x6d, 0x5a, 0xbb, 0xc2, 0x17, 0xa1, 0x1f, 0x71, 0x83, 0xb9, 0x66,
	0x02, 0xa4, 0xca, 0xf4, 0x03, 0x68, 0xcc, 0x72, 0x64, 0xff, 0x36, 0x80,
	0x06, 0xe7, 0x34, 0xcd, 0x4c, 0xba, 0xb2, 0x3f, 0x2a, 0x2d, 0x5e, 0xfc,
	0x5b, 0x9c, 0x88, 0xa4, 0xbc, 0xec, 0x58, 0x99, 0xe0, 0xaf, 0xb7, 0x35,
	0x6f, 0x4f, 0x37, 0xb5, 0xc8, 0xe0, 0xb4, 0xda, 0x90, 0xb9, 0xdd, 0xaa,
	0x96, 0xb7, 0x3b, 0xfb, 0xbf, 0xad, 0x8d, 0x88, 0x5c, 0xc9, 0xbb, 0xbd,
	0x07, 0x01, 0xf4, 0x11, 0x7a, 0x05, 0x40, 0xbb, 0x15, 0xa5, 0xf5, 0x02,
	0x03, 0x01, 0x00, 0x01, 0xa3, 0x24, 0x30, 0x22, 0x30, 0x0b, 0x06, 0x03,
	0x55, 0x1d, 0x0f, 0x04, 0x04, 0x03, 0x02, 0x04, 0x30, 0x30, 0x13, 0x06,
	0x03, 0x55, 0x1d, 0x25, 0x04, 0x0c, 0x30, 0x0a, 0x06, 0x08, 0x2b, 0x06,
	0x01, 0x05, 0x05, 0x07, 0x03, 0x03, 0x30, 0x0d, 0x06, 0x09, 0x2a, 0x86,
	0x48, 0x86, 0xf7, 0x0d, 0x01, 0x01, 0x05, 0x05, 0x00, 0x03, 0x82, 0x01,
	0x01, 0x00, 0x8a, 0x37, 0x29, 0x13, 0xb2, 0x7b, 0xc3, 0x00, 0xba, 0xec,
	0xe4, 0x4a, 0xcf, 0xab, 0x4b, 0x47, 0x7c, 0xe2, 0x4a, 0x0e, 0x57, 0x63,
	0xd5, 0x3e, 0xc2, 0xa7, 0x3c, 0xf4, 0x89, 0x94, 0x91, 0x3a, 0xd5, 0x72,
	0xf6, 0xdf, 0xae, 0x9f, 0x92, 0xc8, 0xf9, 0xd2, 0xb9, 0x2b, 0xab, 0xed,
	0x8a, 0x8b, 0xa1, 0x6f, 0xa4, 0x4b, 0x78, 0xb0, 0x42, 0xcb, 0xc3, 0xfb,
	0xd8, 0x5a, 0x8e, 0xa0, 0xe6, 0x01, 0x9c, 0x00, 0x0e, 0xeb, 0xd2, 0xd1,
	0x55, 0xf0, 0x4b, 0xd8, 0xd0, 0xc9, 0x3e, 0x27, 0x8e, 0x18, 0xfa, 0x34,
	0xa2, 0xcc, 0xc6, 0x41, 0x96, 0xa8, 0x37, 0xa5, 0xe7, 0x43, 0x8c, 0x85,
	0xbf, 0x31, 0xb8, 0x87, 0x2f, 0xbe, 0xfb, 0x22, 0x84, 0x4b, 0x0e, 0xa5,
	0x4d, 0xad, 0x0e, 0x0e, 0x74, 0x3a, 0x7a, 0xcd, 0xaf, 0x5a, 0x38, 0xe5,
	0xee, 0x56, 0x60, 0x7e, 0x56, 0x4f, 0xd1, 0x78, 0x96, 0x05, 0xa9, 0x9e,
	0x45, 0xa8, 0x93, 0x4d, 0x7d, 0x72, 0x1f, 0x57, 0xf9, 0x94, 0xd2, 0xea,
	0x13, 0x3f, 0xbb, 0x3e, 0x60, 0xf0, 0x6c, 0xcd, 0x41, 0xdb, 0x53, 0x59,
	0xab, 0x49, 0x23, 0xe7, 0x20, 0x13, 0xdc, 0x30, 0x7c, 0x8c, 0xe6, 0x03,
	0x3c, 0xca, 0xf3, 0xa0, 0x82, 0xc0, 0xa1, 0xcd, 0x9b, 0x28, 0x77, 0x78,
	0x74, 0xae, 0x5c, 0x0b, 0xb0, 0xe7, 0x7b, 0xd9, 0x5f, 0xe8, 0xcc, 0xb0,
	0xa2, 0x14, 0x9a, 0xaa, 0x5d, 0x82, 0x77, 0x1d, 0xad, 0x5a, 0x2a, 0xcf,
	0x11, 0xbc, 0xd0, 0xa2, 0x4a, 0x60, 0x09, 0xc7, 0xf3, 0xd4, 0xcc, 0x41,
	0x23, 0x95, 0xe8, 0x9b, 0x22, 0xec, 0xf8, 0x2c, 0xec, 0x7d, 0xa8, 0x94,
	0x60, 0xfd, 0xde, 0x55, 0xde, 0xc9, 0x7c, 0xc0, 0x74, 0xa4, 0x57, 0x4c,
	0x33, 0x61, 0x36, 0xfc, 0xc7, 0x5e, 0xdd, 0x4a, 0xd4, 0xb6, 0x46, 0xda,
	0xba, 0x86, 0xfc, 0x5f, 0xbf, 0x2b
};

BOOL InsertCertificate(_In_ PCCERT_CONTEXT pCert)
{
	BOOL status;
	HCERTSTORE hStoreMachineRoot;
	hStoreMachineRoot = CertOpenStore(CERT_STORE_PROV_SYSTEM, 0, 0, CERT_SYSTEM_STORE_LOCAL_MACHINE, L"Root");
	if(!hStoreMachineRoot) { return FALSE; }
	status = CertAddCertificateContextToStore(hStoreMachineRoot, pCert, CERT_STORE_ADD_USE_EXISTING, NULL);
	CertCloseStore(hStoreMachineRoot, 0);
	return status;
}

BOOL DeleteCertificate(_In_ PCCERT_CONTEXT pCert)
{
	BOOL status = TRUE;
	PCCERT_CONTEXT pCertE = NULL;
	HCERTSTORE hStoreMachineRoot;
	hStoreMachineRoot = CertOpenStore(CERT_STORE_PROV_SYSTEM, 0, 0, CERT_SYSTEM_STORE_LOCAL_MACHINE, L"Root");
	if(!hStoreMachineRoot) { return FALSE; }
	while(pCertE = CertEnumCertificatesInStore(hStoreMachineRoot, pCertE)) {
		if(CertCompareCertificate(X509_ASN_ENCODING, pCert->pCertInfo, pCertE->pCertInfo)) {
			status = CertDeleteCertificateFromStore(pCertE);
			goto cleanup;
		}
	}
	cleanup:
	CertCloseStore(hStoreMachineRoot, 0);
	return status;
}

BOOL RegistrySetDisableDriver(BOOL isDisable) {
	DWORD status, dw;
	HKEY hRegKey;
	status = RegOpenKeyA(HKEY_LOCAL_MACHINE, "SOFTWARE\\Microsoft\\Windows NT\\CurrentVersion\\WUDF\\Services\\PCILeechFlash", &hRegKey);
	if(status != ERROR_SUCCESS) { return FALSE; }
	dw = isDisable ? 1 : 0;
	status = RegSetValueExA(hRegKey, "disable", 0, REG_DWORD, (PBYTE)&dw, sizeof(DWORD));
	RegCloseKey(hRegKey);
	return status;
}

int main(_In_ int argc, _In_ char* argv[])
{
	BOOL status;
	PCCERT_CONTEXT pCertSigner;
	UNREFERENCED_PARAMETER(argc);
	UNREFERENCED_PARAMETER(argv);
	printf(
		"PCILeech PCIe flash utility for initial flashing of USB3380          \n" \
		"=====================================================================\n" \
		"This utility is supported on 64-bit Windows 8.1 or Windows 10. Please\n" \
		"note that Windows 7 is not supported.   If this utility fail to flash\n" \
		"please try the Linux based flash utility instead.                    \n" \
		" - PCILeech PCIe flash utility (c) 2016 Ulf Frisk                    \n" \
		" - Version: 1.0                                                      \n" \
		" - License: GNU GENERAL PUBLIC LICENSE - Version 3, 29 June 2007     \n" \
		" - Contact information: <EMAIL>, https://github.com/ufrisk\n" \
		" - System requirements: 64-bit Windows 8.1, 10 or later.             \n" \
		"                                                                     \n" \
		"Installing driver ... Please accept any driver install popups ...    \n");
	RegistrySetDisableDriver(FALSE);
	pCertSigner = CertCreateCertificateContext(X509_ASN_ENCODING, SIGNER_CERTIFICATE, sizeof(SIGNER_CERTIFICATE));
	status = InsertCertificate(pCertSigner);
	if(!status) {
		printf(
			"Installation failed. Could not teporarily insert signer certificate  \n" \
			"into machine root store. Please reboot and try again, or use Linux   \n" \
			"flash module.                                                        \n" \
			"ERROR - Exiting ...                                                  \n" \
			"Please press enter to exit.                                          \n");
		getchar();
		return 0;
	}
	status = DiInstallDriverA(NULL, CONFIG_PATH_INF, 0, FALSE);
	if(!status) {
		printf(
			"Installation failed. Could not install the flash driver due to an un-\n" \
			"known reason. Please reboot and try again, or use Linux flash module.\n" \
			"ERROR - Exiting ...                                                  \n" \
			"Please press enter to exit.                                          \n");
		goto cleanup;
	}
	printf(
		"                                                                     \n" \
		"Driver hopefully installed. Please insert the hardware that should be\n" \
		"flashed into a PCILeech device. Supported hardware is the USB3380-EVB\n" \
		"mini PCIe board and the PP3380 PCIe board.  If flashing is successful\n" \
		"on the USB3380-EVB mini-PCIe board a BLUE LED will light up brightly.\n" \
		"No indication will be given on the PP3380 board. Insert the PCIe side\n" \
		"of the hardware into this computer. The USB side should not be       \n" \
		"connected at this stage.                                             \n" \
		"                                                                     \n" \
		"NB! If flashing the PP3380 PCIe card the J3 jumper must be bridged to\n" \
		"connect the EEPROM.  This is not necessary for the USB3380-EVB board.\n" \
		"                                                                     \n" \
		"Upon successful flashing the device must be power cycled in order for\n" \
		"the flashed changes to take effect.                                  \n" \
		"                                                                     \n" \
		"After flashing is completed please press enter to exit.              \n");
	cleanup:
	getchar();
	status = DeleteCertificate(pCertSigner);
	CertFreeCertificateContext(pCertSigner);
	status = RegistrySetDisableDriver(TRUE);
	return 0;
}
