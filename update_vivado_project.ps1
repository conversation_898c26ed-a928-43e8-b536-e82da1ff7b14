# 更新Vivado项目文件以包含FT601优化版本
# 专门用于修复项目生成脚本的脚本

param(
    [Parameter(Mandatory=$false)]
    [string]$ProjectPath = "G:\FPGA\20250327\100t20250618\100t484-1"
)

$ErrorActionPreference = "Stop"

Write-Host "=== 更新Vivado项目文件 ===" -ForegroundColor Green
Write-Host "项目路径: $ProjectPath" -ForegroundColor Yellow

Set-Location $ProjectPath

# 检查优化版FT601文件是否存在
$optimizedFT601 = Join-Path $ProjectPath "src\pcileech_ft601_optimized.sv"
if (-not (Test-Path $optimizedFT601)) {
    Write-Error "优化版FT601文件不存在: $optimizedFT601"
    exit 1
}

# 更新项目生成脚本
$vivadoProject = Join-Path $ProjectPath "vivado_generate_project_captaindma_100t.tcl"
if (-not (Test-Path $vivadoProject)) {
    Write-Error "Vivado项目生成脚本不存在: $vivadoProject"
    exit 1
}

Write-Host "`n[1/3] 备份原始项目文件..." -ForegroundColor Cyan
$backupFile = "$vivadoProject.backup_$(Get-Date -Format 'yyyyMMdd_HHmmss')"
Copy-Item $vivadoProject $backupFile -Force
Write-Host "  备份文件: $backupFile" -ForegroundColor Gray

Write-Host "`n[2/3] 更新项目生成脚本..." -ForegroundColor Cyan

# 读取文件内容
$content = Get-Content $vivadoProject -Raw

# 检查是否已经包含优化版文件
if ($content -match 'pcileech_ft601_optimized\.sv') {
    Write-Host "  项目文件已包含优化版FT601控制器" -ForegroundColor Green
} else {
    Write-Host "  添加优化版FT601控制器到项目..." -ForegroundColor Yellow
    
    # 在源文件列表中添加优化版文件
    $pattern = '(\[file normalize "\$\{origin_dir\}/src/pcileech_ft601\.sv"\])'
    $replacement = '$1\`n [file normalize "${origin_dir}/src/pcileech_ft601_optimized.sv"]'
    
    $content = $content -replace $pattern, $replacement
    
    # 在文件属性设置部分添加优化版文件的属性
    $filePropsPattern = '(set file "src/pcileech_ft601\.sv"[\s\S]*?set_property -name "file_type" -value "SystemVerilog" -objects \$file_obj)'
    $filePropsReplacement = @'
$1

set file "src/pcileech_ft601_optimized.sv"
set file_obj [get_files -of_objects [get_filesets sources_1] [list "*$file"]]
set_property -name "file_type" -value "SystemVerilog" -objects $file_obj
'@
    
    $content = $content -replace $filePropsPattern, $filePropsReplacement
    
    # 写回文件
    Set-Content $vivadoProject $content -Encoding UTF8
    Write-Host "  项目生成脚本已更新" -ForegroundColor Green
}

Write-Host "`n[3/3] 验证更新结果..." -ForegroundColor Cyan

# 验证更新
$updatedContent = Get-Content $vivadoProject -Raw

$checks = @(
    @{ Name = "包含优化版FT601文件"; Pattern = 'pcileech_ft601_optimized\.sv'; Expected = $true },
    @{ Name = "包含原始FT601文件"; Pattern = 'pcileech_ft601\.sv'; Expected = $true },
    @{ Name = "文件属性设置"; Pattern = 'set file "src/pcileech_ft601_optimized\.sv"'; Expected = $true }
)

$allPassed = $true
foreach ($check in $checks) {
    $found = $updatedContent -match $check.Pattern
    if ($found -eq $check.Expected) {
        Write-Host "  ✓ $($check.Name)" -ForegroundColor Green
    } else {
        Write-Host "  ✗ $($check.Name)" -ForegroundColor Red
        $allPassed = $false
    }
}

if ($allPassed) {
    Write-Host "`n=== 更新完成 ===" -ForegroundColor Green
    Write-Host "项目文件已成功更新，包含以下优化:" -ForegroundColor Yellow
    Write-Host "- 优化版FT601控制器 (pcileech_ft601_optimized.sv)" -ForegroundColor White
    Write-Host "- 保留原始FT601控制器 (pcileech_ft601.sv)" -ForegroundColor White
    Write-Host "- 正确的文件类型设置" -ForegroundColor White
    
    Write-Host "`n下一步操作:" -ForegroundColor Yellow
    Write-Host "1. 在Vivado中运行: source vivado_generate_project_captaindma_100t.tcl" -ForegroundColor White
    Write-Host "2. 或者删除现有项目文件夹，重新生成项目" -ForegroundColor White
    Write-Host "3. 运行综合和实现" -ForegroundColor White
    
} else {
    Write-Host "`n=== 更新失败 ===" -ForegroundColor Red
    Write-Host "请检查错误信息并手动修复" -ForegroundColor Yellow
    Write-Host "备份文件位置: $backupFile" -ForegroundColor Cyan
}

# 显示需要手动检查的内容
Write-Host "`n=== 手动检查建议 ===" -ForegroundColor Yellow
Write-Host "请确认以下文件内容:" -ForegroundColor White

Write-Host "`n1. pcileech_com.sv 应该使用 pcileech_ft601_optimized:" -ForegroundColor Cyan
$comFile = Join-Path $ProjectPath "src\pcileech_com.sv"
if (Test-Path $comFile) {
    $comContent = Get-Content $comFile -Raw
    if ($comContent -match 'pcileech_ft601_optimized') {
        Write-Host "   ✓ pcileech_com.sv 正确使用优化版控制器" -ForegroundColor Green
    } else {
        Write-Host "   ✗ pcileech_com.sv 仍使用原始控制器" -ForegroundColor Red
        Write-Host "   请手动修改 pcileech_com.sv 中的模块实例化" -ForegroundColor Yellow
    }
}

Write-Host "`n2. 优化版文件是否存在:" -ForegroundColor Cyan
if (Test-Path $optimizedFT601) {
    $fileSize = (Get-Item $optimizedFT601).Length
    Write-Host "   ✓ pcileech_ft601_optimized.sv 存在 ($fileSize 字节)" -ForegroundColor Green
} else {
    Write-Host "   ✗ pcileech_ft601_optimized.sv 不存在" -ForegroundColor Red
}

Write-Host "`n3. 项目文件更新状态:" -ForegroundColor Cyan
$projectMatches = ($updatedContent | Select-String -Pattern "pcileech_ft601.*\.sv" -AllMatches).Matches
Write-Host "   找到 $($projectMatches.Count) 个FT601相关文件引用:" -ForegroundColor White
foreach ($match in $projectMatches) {
    Write-Host "     - $($match.Value)" -ForegroundColor Gray
}

Write-Host "`n如果仍有问题，请运行以下命令进行诊断:" -ForegroundColor Yellow
Write-Host "Get-Content '$vivadoProject' | Select-String -Pattern 'pcileech_ft601'" -ForegroundColor Gray
