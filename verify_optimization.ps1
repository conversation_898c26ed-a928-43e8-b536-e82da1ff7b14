# 验证FT601优化方案是否正确应用
# 检查所有关键文件和配置

param(
    [Parameter(Mandatory=$false)]
    [string]$ProjectPath = "G:\FPGA\20250327\100t20250618\100t484-1"
)

Write-Host "=== FT601优化方案验证 ===" -ForegroundColor Green
Write-Host "项目路径: $ProjectPath" -ForegroundColor Yellow

Set-Location $ProjectPath

$allChecks = @()

# 检查1: 优化版FT601控制器文件
Write-Host "`n[1/6] 检查优化版FT601控制器..." -ForegroundColor Cyan
$optimizedFT601 = Join-Path $ProjectPath "src\pcileech_ft601_optimized.sv"
if (Test-Path $optimizedFT601) {
    $fileSize = (Get-Item $optimizedFT601).Length
    Write-Host "  ✓ pcileech_ft601_optimized.sv 存在 ($fileSize 字节)" -ForegroundColor Green
    $allChecks += @{ Name = "优化版FT601控制器"; Status = "通过" }
} else {
    Write-Host "  ✗ pcileech_ft601_optimized.sv 不存在" -ForegroundColor Red
    $allChecks += @{ Name = "优化版FT601控制器"; Status = "失败" }
}

# 检查2: pcileech_com.sv 是否使用优化版
Write-Host "`n[2/6] 检查pcileech_com.sv模块..." -ForegroundColor Cyan
$comFile = Join-Path $ProjectPath "src\pcileech_com.sv"
if (Test-Path $comFile) {
    $comContent = Get-Content $comFile -Raw
    if ($comContent -match 'pcileech_ft601_optimized') {
        Write-Host "  ✓ pcileech_com.sv 使用优化版控制器" -ForegroundColor Green
        $allChecks += @{ Name = "pcileech_com模块更新"; Status = "通过" }
    } else {
        Write-Host "  ✗ pcileech_com.sv 仍使用原始控制器" -ForegroundColor Red
        $allChecks += @{ Name = "pcileech_com模块更新"; Status = "失败" }
    }
} else {
    Write-Host "  ✗ pcileech_com.sv 不存在" -ForegroundColor Red
    $allChecks += @{ Name = "pcileech_com模块更新"; Status = "失败" }
}

# 检查3: Vivado项目文件
Write-Host "`n[3/6] 检查Vivado项目文件..." -ForegroundColor Cyan
$vivadoProject = Join-Path $ProjectPath "vivado_generate_project_captaindma_100t.tcl"
if (Test-Path $vivadoProject) {
    $projectContent = Get-Content $vivadoProject -Raw
    $hasOptimized = $projectContent -match 'pcileech_ft601_optimized\.sv'
    $hasFileProps = $projectContent -match 'set file "src/pcileech_ft601_optimized\.sv"'
    
    if ($hasOptimized -and $hasFileProps) {
        Write-Host "  ✓ Vivado项目文件包含优化版控制器" -ForegroundColor Green
        $allChecks += @{ Name = "Vivado项目文件"; Status = "通过" }
    } else {
        Write-Host "  ✗ Vivado项目文件缺少优化版控制器配置" -ForegroundColor Red
        $allChecks += @{ Name = "Vivado项目文件"; Status = "失败" }
    }
} else {
    Write-Host "  ✗ Vivado项目文件不存在" -ForegroundColor Red
    $allChecks += @{ Name = "Vivado项目文件"; Status = "失败" }
}

# 检查4: 上位机优化文件
Write-Host "`n[4/6] 检查上位机优化文件..." -ForegroundColor Cyan
$hostFiles = @(
    "pcileech-master\pcileech\device_optimized.c",
    "pcileech-master\pcileech\memdump_optimized.c",
    "pcileech-master\pcileech\ft601_performance_config.h",
    "pcileech-master\pcileech\ft601_performance_config.c"
)

$hostFilesOK = 0
foreach ($file in $hostFiles) {
    $fullPath = Join-Path $ProjectPath $file
    if (Test-Path $fullPath) {
        Write-Host "    ✓ $file" -ForegroundColor Green
        $hostFilesOK++
    } else {
        Write-Host "    ✗ $file" -ForegroundColor Red
    }
}

if ($hostFilesOK -eq $hostFiles.Count) {
    Write-Host "  ✓ 所有上位机优化文件存在" -ForegroundColor Green
    $allChecks += @{ Name = "上位机优化文件"; Status = "通过" }
} else {
    Write-Host "  ✗ 缺少 $($hostFiles.Count - $hostFilesOK) 个上位机优化文件" -ForegroundColor Red
    $allChecks += @{ Name = "上位机优化文件"; Status = "失败" }
}

# 检查5: 项目文件更新
Write-Host "`n[5/6] 检查项目文件更新..." -ForegroundColor Cyan
$vcxproj = Join-Path $ProjectPath "pcileech-master\pcileech\pcileech.vcxproj"
if (Test-Path $vcxproj) {
    $projContent = Get-Content $vcxproj -Raw
    $hasOptimizedFiles = ($projContent -match 'device_optimized\.c') -and 
                        ($projContent -match 'memdump_optimized\.c') -and
                        ($projContent -match 'ft601_performance_config')
    
    if ($hasOptimizedFiles) {
        Write-Host "  ✓ 项目文件包含优化版源文件" -ForegroundColor Green
        $allChecks += @{ Name = "项目文件更新"; Status = "通过" }
    } else {
        Write-Host "  ✗ 项目文件缺少优化版源文件引用" -ForegroundColor Red
        $allChecks += @{ Name = "项目文件更新"; Status = "失败" }
    }
} else {
    Write-Host "  ✗ 项目文件不存在" -ForegroundColor Red
    $allChecks += @{ Name = "项目文件更新"; Status = "失败" }
}

# 检查6: 编译状态
Write-Host "`n[6/6] 检查编译状态..." -ForegroundColor Cyan
$exePath = Join-Path $ProjectPath "pcileech-master\files\pcileech.exe"
if (Test-Path $exePath) {
    $exeInfo = Get-Item $exePath
    Write-Host "  ✓ 可执行文件存在 (修改时间: $($exeInfo.LastWriteTime))" -ForegroundColor Green
    $allChecks += @{ Name = "编译状态"; Status = "通过" }
} else {
    Write-Host "  ⚠ 可执行文件不存在，需要重新编译" -ForegroundColor Yellow
    $allChecks += @{ Name = "编译状态"; Status = "需要编译" }
}

# 生成验证报告
Write-Host "`n=== 验证结果汇总 ===" -ForegroundColor Yellow

$passedCount = ($allChecks | Where-Object { $_.Status -eq "通过" }).Count
$failedCount = ($allChecks | Where-Object { $_.Status -eq "失败" }).Count
$warningCount = ($allChecks | Where-Object { $_.Status -eq "需要编译" }).Count

Write-Host "检查项目总数: $($allChecks.Count)" -ForegroundColor White
Write-Host "通过: $passedCount" -ForegroundColor Green
Write-Host "失败: $failedCount" -ForegroundColor Red
Write-Host "警告: $warningCount" -ForegroundColor Yellow

Write-Host "`n详细结果:" -ForegroundColor White
foreach ($check in $allChecks) {
    $color = switch ($check.Status) {
        "通过" { "Green" }
        "失败" { "Red" }
        "需要编译" { "Yellow" }
        default { "White" }
    }
    Write-Host "  $($check.Name): $($check.Status)" -ForegroundColor $color
}

# 给出下一步建议
Write-Host "`n=== 下一步操作建议 ===" -ForegroundColor Yellow

if ($failedCount -eq 0) {
    Write-Host "✓ 所有检查通过！可以继续下一步操作:" -ForegroundColor Green
    Write-Host "1. 在Vivado中重新生成项目:" -ForegroundColor White
    Write-Host "   source vivado_generate_project_captaindma_100t.tcl" -ForegroundColor Gray
    Write-Host "2. 运行综合和实现" -ForegroundColor White
    Write-Host "3. 生成并烧录比特流" -ForegroundColor White
    
    if ($warningCount -gt 0) {
        Write-Host "4. 重新编译上位机代码" -ForegroundColor White
    }
    
    Write-Host "5. 运行性能测试:" -ForegroundColor White
    Write-Host "   .\test_ft601_optimization.ps1" -ForegroundColor Gray
    
} else {
    Write-Host "✗ 发现 $failedCount 个问题，需要修复:" -ForegroundColor Red
    
    $failedChecks = $allChecks | Where-Object { $_.Status -eq "失败" }
    foreach ($failed in $failedChecks) {
        Write-Host "- $($failed.Name)" -ForegroundColor Red
    }
    
    Write-Host "`n建议重新运行部署脚本:" -ForegroundColor Yellow
    Write-Host ".\deploy_ft601_optimization.ps1" -ForegroundColor Gray
}

# 保存验证报告
$reportPath = Join-Path $ProjectPath "verification_report_$(Get-Date -Format 'yyyyMMdd_HHmmss').txt"
$reportContent = @"
FT601优化方案验证报告
====================

验证时间: $(Get-Date)
项目路径: $ProjectPath

验证结果:
- 检查项目总数: $($allChecks.Count)
- 通过: $passedCount
- 失败: $failedCount
- 警告: $warningCount

详细结果:
$($allChecks | ForEach-Object { "- $($_.Name): $($_.Status)" } | Out-String)

状态: $(if ($failedCount -eq 0) { "就绪" } else { "需要修复" })
"@

Set-Content $reportPath $reportContent -Encoding UTF8
Write-Host "`n验证报告已保存: $reportPath" -ForegroundColor Cyan
